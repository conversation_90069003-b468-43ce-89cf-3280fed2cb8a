package com.valve.module.base.dal.dataobject.pressinfo;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 磅级（压力等级）定义 DO
 *
 * <AUTHOR>
 */
@TableName("vb_press_info")
@KeySequence("vb_press_info_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PressInfoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 压力等级代码
     */
    private String pressCode;
    /**
     * 压力等级描述
     */
    private String pressDesc;
    /**
     * 压力等级计量制式，class,PN
     */
    private String pressSystem;
    /**
     * 标准压力值
     */
    private BigDecimal pressVal;
    /**
     * 标准压力计量单位
     */
    private String pressValUnit;
    /**
     * 大零件压力标准值，针对整机主要部件
     */
    private BigDecimal pressPart;
    /**
     * 大零件压力计量单位
     */
    private String pressPartUnit;
    /**
     * 标准压力上限
     */
    private BigDecimal pressUpper;
    /**
     * 标准压力下限
     */
    private BigDecimal pressLow;
    /**
     * 备注
     */
    private String memo;

}