package com.valve.module.base.controller.admin.materialinfo;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.valve.module.base.controller.admin.materialinfo.vo.*;
import com.valve.module.base.dal.dataobject.caliberinfo.CaliberInfoDO;
import com.valve.module.base.dal.dataobject.designinfo.DesignInfoDO;
import com.valve.module.base.dal.dataobject.materialinfo.MaterialInfoDO;
import com.valve.module.base.dal.dataobject.pressinfo.PressInfoDO;
import com.valve.module.base.dal.dataobject.testinfo.TestInfoDO;
import com.valve.module.base.dal.dataobject.valvetype.ValveTypeDO;
import com.valve.module.base.service.caliberinfo.CaliberInfoService;
import com.valve.module.base.service.designinfo.DesignInfoService;
import com.valve.module.base.service.materialinfo.MaterialInfoService;
import com.valve.module.base.service.pressinfo.PressInfoService;
import com.valve.module.base.service.testinfo.TestInfoService;
import com.valve.module.base.service.valvetype.ValveTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.IMPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 物料主数据")
@RestController
@RequestMapping("/base/material-info")
@Validated
public class MaterialInfoController {

    @Resource
    private MaterialInfoService materialInfoService;
    @Resource
    private ValveTypeService valveTypeService;
    @Resource
    private PressInfoService pressInfoService;
    @Resource
    private CaliberInfoService caliberInfoService;
    @Resource
    private DesignInfoService designInfoService;
    @Resource
    private TestInfoService testInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建物料主数据")
    @PreAuthorize("@ss.hasPermission('base:material-info:create')")
    public CommonResult<Long> createMaterialInfo(@Valid @RequestBody MaterialInfoSaveReqVO createReqVO) {
        return success(materialInfoService.createMaterialInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物料主数据")
    @PreAuthorize("@ss.hasPermission('base:material-info:update')")
    public CommonResult<Boolean> updateMaterialInfo(@Valid @RequestBody MaterialInfoSaveReqVO updateReqVO) {
        materialInfoService.updateMaterialInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物料主数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('base:material-info:delete')")
    public CommonResult<Boolean> deleteMaterialInfo(@RequestParam("id") Long id) {
        materialInfoService.deleteMaterialInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物料主数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('base:material-info:query')")
    public CommonResult<MaterialInfoRespVO> getMaterialInfo(@RequestParam("id") Long id) {
        MaterialInfoDO materialInfo = materialInfoService.getMaterialInfo(id);
        return success(BeanUtils.toBean(materialInfo, MaterialInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物料主数据分页")
    @PreAuthorize("@ss.hasPermission('base:material-info:query')")
    public CommonResult<PageResult<MaterialInfoRespVO>> getMaterialInfoPage(@Valid MaterialInfoPageReqVO pageReqVO) {
        PageResult<MaterialInfoDO> pageResult = materialInfoService.getMaterialInfoPage(pageReqVO);
        PageResult<MaterialInfoRespVO> result = BeanUtils.toBean(pageResult, MaterialInfoRespVO.class);
        fillDetailData(result.getList());
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物料主数据 Excel")
    @PreAuthorize("@ss.hasPermission('base:material-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMaterialInfoExcel(@Valid MaterialInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MaterialInfoRespVO> list = BeanUtils.toBean(materialInfoService.getMaterialInfoPage(pageReqVO).getList(), MaterialInfoRespVO.class);
        // 填充数据
        fillDetailData(list);
        // 导出 Excel
        ExcelUtils.write(response, "物料主数据.xls", "数据", MaterialInfoRespVO.class, list);
    }

    private void fillDetailData(List<MaterialInfoRespVO> list) {
        Map<Long, String> valveTypeMap = valveTypeService
                .listByIds(list.stream().map(MaterialInfoRespVO::getValveType).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ValveTypeDO::getId, ValveTypeDO::getDefaultDesc));
        Map<Long, String> caliberInfoMap = caliberInfoService
                .getByIds(list.stream().map(MaterialInfoRespVO::getValveCaliber).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(CaliberInfoDO::getId, CaliberInfoDO::getSizeCode));
        Map<Long, String> pressInfoMap = pressInfoService
                .getByIds(list.stream().map(MaterialInfoRespVO::getPressCode).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(PressInfoDO::getId, PressInfoDO::getPressCode));
        Map<Long, String> designInfoMap = designInfoService
                .getByIds(list.stream().map(MaterialInfoRespVO::getDesignStd).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(DesignInfoDO::getId, DesignInfoDO::getDesignCode));
        Map<Long, String> testInfoMap = testInfoService
                .getByIds(list.stream().map(MaterialInfoRespVO::getValveCaliber).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(TestInfoDO::getId, TestInfoDO::getTestStand));
        list.forEach(item -> {
            item.setValveTypeName(valveTypeMap.get(item.getValveType()));
            item.setCaliberCode(caliberInfoMap.get(item.getValveCaliber()));
            item.setPressLevel(pressInfoMap.get(item.getPressCode()));
            item.setDesignStandard(designInfoMap.get(item.getDesignStd()));
            item.setTestStandard(testInfoMap.get(item.getTestStd()));
        });
    }

    @PostMapping("/import")
    @Operation(summary = "导入物料主数据 Excel")
    @PreAuthorize("@ss.hasPermission('base:material-info:import')")
    @ApiAccessLog(operateType = IMPORT)
    public CommonResult<MaterialInfoImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                              @RequestParam Boolean updateSupport) throws IOException {
        List<MaterialInfoImportVO> list = ExcelUtils.read(file, MaterialInfoImportVO.class);
        return success(materialInfoService.importMaterialList(list, updateSupport));
    }

    @GetMapping("/download-template")
    @Operation(summary = "下载物料主数据导入模板")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        List<MaterialInfoImportVO> demoData = Arrays.asList(
                MaterialInfoImportVO.builder().materialCode("1111111").materialType(0).textureType("不锈钢")
                        .textureGlobal("F304").textureInside("F304L").valveType("安全阀")
                        .valveCaliber("1-1/2").pressCode("PN16").connType("LFT").designStd("DS2001")
                        .testStd("JBT").torqueCode("STEM TORQUE WITH PRESSURE≤150N·M").driveCode("RL").build(),
                MaterialInfoImportVO.builder().materialCode("2000123").materialType(1).textureType("合金")
                        .textureGlobal("WCB").textureInside("WCB-1").valveType("特殊阀")
                        .valveCaliber("1-1/2x1/2").pressCode("1P").connType("RLF").designStd("DS2001")
                        .testStd("JBT").torqueCode("STEM TORQUE WITH PRESSURE≤300N·M").driveCode("RL").build()
        );
        // 导出 Excel 模板
        ExcelUtils.write(response, "物料主数据导入模板.xls", "数据", MaterialInfoImportVO.class, demoData);
    }

}