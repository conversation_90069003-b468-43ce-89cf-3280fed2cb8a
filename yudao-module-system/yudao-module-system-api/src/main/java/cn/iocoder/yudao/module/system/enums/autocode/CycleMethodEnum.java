package cn.iocoder.yudao.module.system.enums.autocode;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自动编码规则分段流水号的循环方式
 * <AUTHOR>
 * @since 2024/6/7 11:25
 **/
@AllArgsConstructor
@Getter
public enum CycleMethodEnum {

    YEAR("YEAR","按年"),
    MONTH("MONTH","按月"),
    DAY("DAY","按日"),
    HOUR("HOUR","按小时"),
    MINUTE("MINUTE","按分钟"),
    OTHER("OTHER","其他");

    private final String code;
    private final String name;

    public static CycleMethodEnum getByCode(String code){
        for(CycleMethodEnum value : CycleMethodEnum.values()){
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return OTHER;
    }
}
