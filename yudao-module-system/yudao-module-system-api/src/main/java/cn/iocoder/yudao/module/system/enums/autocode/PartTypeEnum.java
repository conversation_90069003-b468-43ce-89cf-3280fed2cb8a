package cn.iocoder.yudao.module.system.enums.autocode;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自动编码规则的分段类型
 */
@AllArgsConstructor
@Getter
public enum PartTypeEnum {

    NOW_DATE("INPUTCHAR","传入字符",0),
    INPUT_CHAR("NOWDATE","当前日期",1),
    FIXCHAR("FIXCHAR","固定字符",2),
    SERIALNO("SERIALNO","流水号",3),
    OTHER("OTHER","其他",99);

    private final String code;
    private final String name;
    private final Integer beanIndex;

    public static PartTypeEnum getByCode(String code){
        for(PartTypeEnum value: PartTypeEnum.values()){
            if(value.getCode().equals(code)){
                return value;
            }
        }
        return OTHER;
    }

}
