package cn.iocoder.yudao.module.system.framework.autocode.core.strategy;

import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(2)
public class PartTypeFixCharHandler implements PartTypeTemplate {
    @Override
    public String partHandle(AutoCodePartDO sysAutoCodePart) {
        return sysAutoCodePart.getFixCharacter();
    }
}
