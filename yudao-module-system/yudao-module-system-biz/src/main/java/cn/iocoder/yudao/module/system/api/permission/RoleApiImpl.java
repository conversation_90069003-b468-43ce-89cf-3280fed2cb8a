package cn.iocoder.yudao.module.system.api.permission;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.enums.permission.RoleCodeEnum;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 角色 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class RoleApiImpl implements RoleApi {

    @Resource
    private RoleService roleService;

    @Override
    public void validRoleList(Collection<Long> ids) {
        roleService.validateRoleList(ids);
    }

    @Override
    public List<RoleRespDTO> getRoleListByCode(RoleCodeEnum codeEnum) {
        if (codeEnum == null) return Collections.emptyList();
        List<RoleDO> roleList = roleService.getRoleList().stream()
                .filter(role -> Objects.equals(codeEnum.getCode(), role.getCode()))
                .collect(Collectors.toList());
        return BeanUtils.toBean(roleList, RoleRespDTO.class);
    }
}
