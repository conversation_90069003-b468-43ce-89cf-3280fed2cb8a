package cn.iocoder.yudao.module.system.dal.mysql.autocode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRulePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocode.AutoCodeRuleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 编码生成规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AutoCodeRuleMapper extends BaseMapperX<AutoCodeRuleDO> {

    default PageResult<AutoCodeRuleDO> selectPage(AutoCodeRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AutoCodeRuleDO>()
                .eqIfPresent(AutoCodeRuleDO::getRuleCode, reqVO.getRuleCode())
                .likeIfPresent(AutoCodeRuleDO::getRuleName, reqVO.getRuleName())
                .orderByDesc(AutoCodeRuleDO::getRuleId));
    }

}