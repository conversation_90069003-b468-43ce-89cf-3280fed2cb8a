package cn.iocoder.yudao.module.system.service.autocode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRulePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRuleSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocode.AutoCodeRuleDO;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 编码生成规则 Service 接口
 *
 * <AUTHOR>
 */
public interface AutoCodeRuleService {

    /**
     * 创建编码生成规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAutoCodeRule(@Valid AutoCodeRuleSaveReqVO createReqVO);

    /**
     * 更新编码生成规则
     *
     * @param updateReqVO 更新信息
     */
    void updateAutoCodeRule(@Valid AutoCodeRuleSaveReqVO updateReqVO);

    /**
     * 删除编码生成规则
     *
     * @param id 编号
     */
    void deleteAutoCodeRule(Long id);

    /**
     * 获得编码生成规则
     *
     * @param id 编号
     * @return 编码生成规则
     */
    AutoCodeRuleDO getAutoCodeRule(Long id);

    AutoCodeRuleDO getByCode(@NotEmpty String ruleCode);

    /**
     * 获得编码生成规则分页
     *
     * @param pageReqVO 分页查询
     * @return 编码生成规则分页
     */
    PageResult<AutoCodeRuleDO> getAutoCodeRulePage(AutoCodeRulePageReqVO pageReqVO);

}