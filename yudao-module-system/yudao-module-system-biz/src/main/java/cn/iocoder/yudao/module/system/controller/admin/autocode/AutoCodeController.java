package cn.iocoder.yudao.module.system.controller.admin.autocode;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.framework.autocode.core.strategy.AutoCodeUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/** 
 * 获取自动编码的统一接口
 * <AUTHOR>
 * @since 2024/6/11 13:57
 **/
@AllArgsConstructor
@RestController
@RequestMapping("/system/auto-code")
public class AutoCodeController {

    private final AutoCodeUtil autoCodeUtil;

    @GetMapping("/get")
    @Operation(summary = "生成一个自动编码")
    public CommonResult<String> getAutoCode(@RequestParam String ruleCode, @RequestParam(required = false) String inputChar) {
        return CommonResult.success(autoCodeUtil.genSerialCode(ruleCode, inputChar));
    }

}
