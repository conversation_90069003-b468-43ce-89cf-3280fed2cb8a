package cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 编码生成规则组成新增/修改 Request VO")
@Data
public class AutoCodePartSaveReqVO {

    @Schema(description = "分段ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14141")
    private Long partId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26956")
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    @Schema(description = "分段序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分段序号不能为空")
    private Integer partIndex;

    @Schema(description = "分段类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "分段类型不能为空")
    private String partType;

    @Schema(description = "分段编号")
    @NotEmpty(message = "分段编号不能为空")
    private String partCode;

    @Schema(description = "分段名称", example = "李四")
    @NotEmpty(message = "分段名称不能为空")
    private String partName;

    @Schema(description = "分段长度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分段长度不能为空")
    private Integer partLength;

    @Schema(description = "日期格式")
    private String dateFormat;

    @Schema(description = "输入字符")
    private String inputCharacter;

    @Schema(description = "固定字符")
    private String fixCharacter;

    @Schema(description = "流水号起始值")
    private Integer seriaStartNo;

    @Schema(description = "流水号步长")
    private Integer seriaStep;

    @Schema(description = "流水号当前值")
    private Integer seriaNowNo;

    @Schema(description = "流水号是否循环")
    private Boolean cycleFlag;

    @Schema(description = "循环方式")
    private String cycleMethod;

    @Schema(description = "备注", example = "随便")
    private String remark;

}