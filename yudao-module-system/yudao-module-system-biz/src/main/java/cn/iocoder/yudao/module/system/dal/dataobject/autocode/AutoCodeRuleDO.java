package cn.iocoder.yudao.module.system.dal.dataobject.autocode;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 编码生成规则 DO
 *
 * <AUTHOR>
 */
@TableName("sys_auto_code_rule")
@KeySequence("sys_auto_code_rule_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoCodeRuleDO extends BaseDO {

    /**
     * 规则ID
     */
    @TableId
    private Long ruleId;
    /**
     * 规则编码
     */
    private String ruleCode;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 描述
     */
    private String ruleDesc;
    /**
     * 最大长度
     */
    private Integer maxLength;
    /**
     * 是否补齐
     */
    private Boolean isPadded;
    /**
     * 补齐字符
     */
    private String paddedChar;
    /**
     * 补齐方式
     */
    private String paddedMethod;
    /**
     * 是否启用
     */
    private Boolean enableFlag;
    /**
     * 备注
     */
    private String remark;

}