package cn.iocoder.yudao.module.system.framework.autocode.core.strategy;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_PART_INPUT_CHAR_EMPTY;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_PART_INPUT_CHAR_ERROR;

@Component
@Order(0)
public class PartTypeInputCharHandler implements PartTypeTemplate {


    @Override
    public String partHandle(AutoCodePartDO sysAutoCodePart) {
        String inputCharacter = sysAutoCodePart.getInputCharacter();
        if (StrUtil.isEmpty(inputCharacter)) throw exception(AUTO_CODE_PART_INPUT_CHAR_EMPTY);
        if (inputCharacter.length() > sysAutoCodePart.getPartLength()) throw exception(AUTO_CODE_PART_INPUT_CHAR_ERROR);
        return inputCharacter;
    }
}
