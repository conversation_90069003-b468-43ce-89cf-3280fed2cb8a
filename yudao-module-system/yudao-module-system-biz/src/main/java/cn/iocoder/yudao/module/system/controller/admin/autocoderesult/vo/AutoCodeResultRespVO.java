package cn.iocoder.yudao.module.system.controller.admin.autocoderesult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 编码生成记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AutoCodeResultRespVO {

    @Schema(description = "记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23564")
    @ExcelProperty("记录ID")
    private Long codeId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9836")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "生成日期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生成日期时间")
    private String genDate;

    @Schema(description = "最后产生的序号")
    @ExcelProperty("最后产生的序号")
    private Integer genIndex;

    @Schema(description = "最后产生的值")
    @ExcelProperty("最后产生的值")
    private String lastResult;

    @Schema(description = "最后产生的流水号")
    @ExcelProperty("最后产生的流水号")
    private Integer lastSerialNo;

    @Schema(description = "最后传入的参数")
    @ExcelProperty("最后传入的参数")
    private String lastInputChar;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

}