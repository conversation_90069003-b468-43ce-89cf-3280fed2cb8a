package cn.iocoder.yudao.module.system.controller.admin.autocoderesult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 编码生成记录新增/修改 Request VO")
@Data
public class AutoCodeResultSaveReqVO {

    @Schema(description = "记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23564")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9836")
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    @Schema(description = "生成日期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "生成日期时间不能为空")
    private String genDate;

    @Schema(description = "最后产生的序号")
    private Integer genIndex;

    @Schema(description = "最后产生的值")
    private String lastResult;

    @Schema(description = "最后产生的流水号")
    private Integer lastSerialNo;

    @Schema(description = "最后传入的参数")
    private String lastInputChar;

    @Schema(description = "备注", example = "随便")
    private String remark;

}