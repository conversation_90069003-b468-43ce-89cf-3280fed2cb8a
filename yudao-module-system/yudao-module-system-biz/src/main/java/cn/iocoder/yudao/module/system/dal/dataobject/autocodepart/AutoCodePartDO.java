package cn.iocoder.yudao.module.system.dal.dataobject.autocodepart;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 编码生成规则组成 DO
 *
 * <AUTHOR>
 */
@TableName("sys_auto_code_part")
@KeySequence("sys_auto_code_part_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoCodePartDO extends BaseDO {

    /**
     * 分段ID
     */
    @TableId
    private Long partId;
    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 分段序号
     */
    private Integer partIndex;
    /**
     * 分段类型
     *
     */
    private String partType;
    /**
     * 分段编号
     */
    private String partCode;
    /**
     * 分段名称
     */
    private String partName;
    /**
     * 分段长度
     */
    private Integer partLength;
    /**
     * 日期格式
     */
    private String dateFormat;
    /**
     * 输入字符
     */
    private String inputCharacter;
    /**
     * 固定字符
     */
    private String fixCharacter;
    /**
     * 流水号起始值
     */
    private Integer seriaStartNo;
    /**
     * 流水号步长
     */
    private Integer seriaStep;
    /**
     * 流水号当前值
     */
    private Integer seriaNowNo;
    /**
     * 流水号是否循环
     *
     */
    private Boolean cycleFlag;
    /**
     * 循环方式
     * 枚举 {@link cn.iocoder.yudao.module.system.enums.autocode.CycleMethodEnum}
     */
    private String cycleMethod;
    /**
     * 备注
     */
    private String remark;

}