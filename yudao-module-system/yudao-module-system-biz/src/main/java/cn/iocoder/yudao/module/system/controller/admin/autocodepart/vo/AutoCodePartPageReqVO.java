package cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 编码生成规则组成分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AutoCodePartPageReqVO extends PageParam {

    @Schema(description = "规则ID不能为空")
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    @Schema(description = "分段编号")
    private String partCode;

    @Schema(description = "分段名称", example = "李四")
    private String partName;

}