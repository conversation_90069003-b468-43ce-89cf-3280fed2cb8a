package cn.iocoder.yudao.module.system.service.autocoderesult;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.autocoderesult.vo.AutoCodeResultSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocoderesult.AutoCodeResultDO;
import cn.iocoder.yudao.module.system.dal.mysql.autocoderesult.AutoCodeResultMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_RESULT_NOT_EXISTS;

/**
 * 编码生成记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AutoCodeResultServiceImpl implements AutoCodeResultService {

    @Resource
    private AutoCodeResultMapper autoCodeResultMapper;

    @Override
    public Long createAutoCodeResult(AutoCodeResultSaveReqVO createReqVO) {
        // 插入
        AutoCodeResultDO autoCodeResult = BeanUtils.toBean(createReqVO, AutoCodeResultDO.class);
        autoCodeResultMapper.insert(autoCodeResult);
        // 返回
        return autoCodeResult.getId();
    }

    @Override
    public void updateAutoCodeResult(AutoCodeResultSaveReqVO updateReqVO) {
        // 校验存在
        validateAutoCodeResultExists(updateReqVO.getId());
        // 更新
        AutoCodeResultDO updateObj = BeanUtils.toBean(updateReqVO, AutoCodeResultDO.class);
        autoCodeResultMapper.updateById(updateObj);
    }

    private void validateAutoCodeResultExists(Long id) {
        if (autoCodeResultMapper.selectById(id) == null) {
            throw exception(AUTO_CODE_RESULT_NOT_EXISTS);
        }
    }

    @Override
    public List<AutoCodeResultDO> list(AutoCodeResultDO params) {
        return autoCodeResultMapper.selectList(new LambdaQueryWrapperX<AutoCodeResultDO>()
                .eq(AutoCodeResultDO::getRuleId, params.getRuleId())
                .likeRightIfPresent(AutoCodeResultDO::getGenDate, params.getGenDate())
                .eqIfPresent(AutoCodeResultDO::getLastInputChar, params.getLastInputChar())
                .orderByDesc(AutoCodeResultDO::getGenDate));
    }

}