package cn.iocoder.yudao.module.system.util.tree;

import java.util.ArrayList;
import java.util.List;

public class TreeBuild {

    // 保存参与构建树形的所有数据（通常数据库查询结果）
    public List<TreeNode> nodeList;

    public TreeBuild(List<TreeNode> nodeList) {
        this.nodeList = nodeList;
    }

    public List<TreeNode> getRootNode() {
        // 保存所有根节点（所有根节点的数据）
        List<TreeNode> rootNodeList = new ArrayList<>();
        // treeNode：查询出的每一条数据（节点）
        for (TreeNode treeNode : nodeList) {
            // 判断当前节点是否为根节点，此处注意：若parentId类型是String，则要采用equals()方法判断。
            if (0 == treeNode.getParentId()) {
                // 是，添加
                rootNodeList.add(treeNode);
            }
        }
        return rootNodeList;
    }

    public List<TreeNode> buildTree() {
        // treeNodes：保存一个顶级节点所构建出来的完整树形
        List<TreeNode> treeNodes = new ArrayList<>();
        // getRootNode()：获取所有的根节点    注意 ：此时得执行顺序是  先执行完 getRootNode() 之后，才会到这里开始循环
        for (TreeNode treeRootNode : getRootNode()) {
            // 将顶级节点进行构建子树
            treeRootNode = buildChildTree(treeRootNode);
            // 完成一个顶级节点所构建的树形，增加进来
            treeNodes.add(treeRootNode);
        }
        return treeNodes;
    }

    public TreeNode buildChildTree(TreeNode pNode) {
        List<TreeNode> childTree = new ArrayList<TreeNode>();
        // nodeList：所有节点集合（所有数据）
        for (TreeNode treeNode : nodeList) {
            // 判断当前节点的父节点ID是否等于根节点的ID，即当前节点为其下的子节点
            if (treeNode.getParentId().equals(pNode.getId())) {
                // 再递归进行判断当前节点的情况，调用自身方法
                // 如果发现了顶级节点有了子节点 ，那么就会把当前得子节点 再去执行一次buildChildTree方法
                childTree.add(buildChildTree(treeNode));
            }
        }
        // for循环结束，即节点下没有任何节点，树形构建结束，设置树结果
        pNode.setChildren(childTree);
        return pNode;
    }

}