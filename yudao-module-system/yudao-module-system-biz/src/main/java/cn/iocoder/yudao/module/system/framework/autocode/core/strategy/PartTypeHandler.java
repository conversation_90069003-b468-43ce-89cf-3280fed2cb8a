package cn.iocoder.yudao.module.system.framework.autocode.core.strategy;

import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import cn.iocoder.yudao.module.system.enums.autocode.PartTypeEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@AllArgsConstructor
@Component
public class PartTypeHandler {

    private final List<PartTypeTemplate> partTypeTemplates;

    public String choiceExecute(AutoCodePartDO sysAutoCodePart){
        String partType = sysAutoCodePart.getPartType();
        return partTypeTemplates.get(PartTypeEnum.getByCode(partType).getBeanIndex()).partHandle(sysAutoCodePart);
    }

}
