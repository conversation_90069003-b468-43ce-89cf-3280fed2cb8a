package cn.iocoder.yudao.module.system.controller.admin.autocodepart;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartRespVO;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import cn.iocoder.yudao.module.system.service.autocodepart.AutoCodePartService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 编码生成规则组成")
@RestController
@RequestMapping("/system/auto-code-part")
@Validated
public class AutoCodePartController {

    @Resource
    private AutoCodePartService autoCodePartService;

    @PostMapping("/create")
    @Operation(summary = "创建编码生成规则组成")
    @PreAuthorize("@ss.hasPermission('system:auto-code-part:create')")
    public CommonResult<Long> createAutoCodePart(@Valid @RequestBody AutoCodePartSaveReqVO createReqVO) {
        return success(autoCodePartService.createAutoCodePart(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新编码生成规则组成")
    @PreAuthorize("@ss.hasPermission('system:auto-code-part:update')")
    public CommonResult<Boolean> updateAutoCodePart(@Valid @RequestBody AutoCodePartSaveReqVO updateReqVO) {
        autoCodePartService.updateAutoCodePart(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除编码生成规则组成")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:auto-code-part:delete')")
    public CommonResult<Boolean> deleteAutoCodePart(@RequestParam("id") Long id) {
        autoCodePartService.deleteAutoCodePart(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得编码生成规则组成")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:auto-code-part:query')")
    public CommonResult<AutoCodePartRespVO> getAutoCodePart(@RequestParam("id") Long id) {
        AutoCodePartDO autoCodePart = autoCodePartService.getAutoCodePart(id);
        return success(BeanUtils.toBean(autoCodePart, AutoCodePartRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得编码生成规则组成分页")
    @PreAuthorize("@ss.hasPermission('system:auto-code-part:query')")
    public CommonResult<PageResult<AutoCodePartRespVO>> getAutoCodePartPage(@Valid AutoCodePartPageReqVO pageReqVO) {
        PageResult<AutoCodePartDO> pageResult = autoCodePartService.getAutoCodePartPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AutoCodePartRespVO.class));
    }

}