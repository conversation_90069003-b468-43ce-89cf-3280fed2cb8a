package cn.iocoder.yudao.module.system.dal.dataobject.autocoderesult;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 编码生成记录 DO
 *
 * <AUTHOR>
 */
@TableName("sys_auto_code_result")
@KeySequence("sys_auto_code_result_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoCodeResultDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 生成日期时间
     */
    private String genDate;
    /**
     * 最后产生的序号
     */
    private Integer genIndex;
    /**
     * 最后产生的值
     */
    private String lastResult;
    /**
     * 最后产生的流水号
     */
    private Integer lastSerialNo;
    /**
     * 最后传入的参数
     */
    private String lastInputChar;
    /**
     * 备注
     */
    private String remark;

}