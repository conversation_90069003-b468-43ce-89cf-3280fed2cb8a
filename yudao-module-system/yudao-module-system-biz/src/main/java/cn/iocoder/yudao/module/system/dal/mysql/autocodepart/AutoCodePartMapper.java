package cn.iocoder.yudao.module.system.dal.mysql.autocodepart;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 编码生成规则组成 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AutoCodePartMapper extends BaseMapperX<AutoCodePartDO> {

    default PageResult<AutoCodePartDO> selectPage(AutoCodePartPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AutoCodePartDO>()
                .eq(AutoCodePartDO::getRuleId, reqVO.getRuleId())
                .eqIfPresent(AutoCodePartDO::getPartCode, reqVO.getPartCode())
                .likeIfPresent(AutoCodePartDO::getPartName, reqVO.getPartName())
                .orderByAsc(AutoCodePartDO::getPartIndex));
    }

}