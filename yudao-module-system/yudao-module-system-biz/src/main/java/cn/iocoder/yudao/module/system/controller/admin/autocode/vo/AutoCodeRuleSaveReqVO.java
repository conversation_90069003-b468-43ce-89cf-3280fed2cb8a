package cn.iocoder.yudao.module.system.controller.admin.autocode.vo;

import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 编码生成规则新增/修改 Request VO")
@Data
public class AutoCodeRuleSaveReqVO {

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "644")
    private Long ruleId;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "规则编码不能为空")
    @DiffLogField(name = "规则编码")
    private String ruleCode;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "规则名称不能为空")
    @DiffLogField(name = "规则名称")
    private String ruleName;

    @Schema(description = "描述")
    private String ruleDesc;

    @Schema(description = "最大长度")
    private Integer maxLength;

    @Schema(description = "是否补齐", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否补齐不能为空")
    private Boolean isPadded;

    @Schema(description = "补齐字符")
    private String paddedChar;

    @Schema(description = "补齐方式")
    private String paddedMethod;

    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean enableFlag;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}