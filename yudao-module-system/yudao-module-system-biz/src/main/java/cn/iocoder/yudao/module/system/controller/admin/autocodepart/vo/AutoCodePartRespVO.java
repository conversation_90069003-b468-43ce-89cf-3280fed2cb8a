package cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import static cn.iocoder.yudao.module.infra.enums.DictTypeConstants.BOOLEAN_STRING;
import static cn.iocoder.yudao.module.system.enums.DictTypeConstants.SYS_AUTOCODE_CYCLEMETHOD;
import static cn.iocoder.yudao.module.system.enums.DictTypeConstants.SYS_AUTOCODE_PARTYPE;

@Schema(description = "管理后台 - 编码生成规则组成 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AutoCodePartRespVO {

    @Schema(description = "分段ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14141")
    @ExcelProperty("分段ID")
    private Long partId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26956")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "分段序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分段序号")
    private Integer partIndex;

    @Schema(description = "分段类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "分段类型", converter = DictConvert.class)
    @DictFormat(SYS_AUTOCODE_PARTYPE)
    private String partType;

    @Schema(description = "分段编号")
    @ExcelProperty("分段编号")
    private String partCode;

    @Schema(description = "分段名称", example = "李四")
    @ExcelProperty("分段名称")
    private String partName;

    @Schema(description = "分段长度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分段长度")
    private Integer partLength;

    @Schema(description = "日期格式")
    @ExcelProperty("日期格式")
    private String dateFormat;

    @Schema(description = "输入字符")
    @ExcelProperty("输入字符")
    private String inputCharacter;

    @Schema(description = "固定字符")
    @ExcelProperty("固定字符")
    private String fixCharacter;

    @Schema(description = "流水号起始值")
    @ExcelProperty("流水号起始值")
    private Integer seriaStartNo;

    @Schema(description = "流水号步长")
    @ExcelProperty("流水号步长")
    private Integer seriaStep;

    @Schema(description = "流水号当前值")
    @ExcelProperty("流水号当前值")
    private Integer seriaNowNo;

    @Schema(description = "流水号是否循环")
    @ExcelProperty(value = "流水号是否循环", converter = DictConvert.class)
    @DictFormat(BOOLEAN_STRING)
    private Boolean cycleFlag;

    @Schema(description = "循环方式")
    @ExcelProperty(value = "循环方式", converter = DictConvert.class)
    @DictFormat(SYS_AUTOCODE_CYCLEMETHOD)
    private String cycleMethod;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

}