package cn.iocoder.yudao.module.system.controller.admin.autocode;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRulePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRuleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRuleSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocode.AutoCodeRuleDO;
import cn.iocoder.yudao.module.system.service.autocode.AutoCodeRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 编码生成规则")
@RestController
@RequestMapping("/system/auto-code-rule")
@Validated
public class AutoCodeRuleController {

    @Resource
    private AutoCodeRuleService autoCodeRuleService;

    @PostMapping("/create")
    @Operation(summary = "创建编码生成规则")
    @PreAuthorize("@ss.hasPermission('system:auto-code-rule:create')")
    public CommonResult<Long> createAutoCodeRule(@Valid @RequestBody AutoCodeRuleSaveReqVO createReqVO) {
        return success(autoCodeRuleService.createAutoCodeRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新编码生成规则")
    @PreAuthorize("@ss.hasPermission('system:auto-code-rule:update')")
    public CommonResult<Boolean> updateAutoCodeRule(@Valid @RequestBody AutoCodeRuleSaveReqVO updateReqVO) {
        autoCodeRuleService.updateAutoCodeRule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除编码生成规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:auto-code-rule:delete')")
    public CommonResult<Boolean> deleteAutoCodeRule(@RequestParam("id") Long id) {
        autoCodeRuleService.deleteAutoCodeRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得编码生成规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:auto-code-rule:query')")
    public CommonResult<AutoCodeRuleRespVO> getAutoCodeRule(@RequestParam("id") Long id) {
        AutoCodeRuleDO autoCodeRule = autoCodeRuleService.getAutoCodeRule(id);
        return success(BeanUtils.toBean(autoCodeRule, AutoCodeRuleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得编码生成规则分页")
    @PreAuthorize("@ss.hasPermission('system:auto-code-rule:query')")
    public CommonResult<PageResult<AutoCodeRuleRespVO>> getAutoCodeRulePage(@Valid AutoCodeRulePageReqVO pageReqVO) {
        PageResult<AutoCodeRuleDO> pageResult = autoCodeRuleService.getAutoCodeRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AutoCodeRuleRespVO.class));
    }
}