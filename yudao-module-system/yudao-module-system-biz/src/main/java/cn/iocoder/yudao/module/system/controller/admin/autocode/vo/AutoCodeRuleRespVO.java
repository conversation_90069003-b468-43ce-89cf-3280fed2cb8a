package cn.iocoder.yudao.module.system.controller.admin.autocode.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import static cn.iocoder.yudao.module.infra.enums.DictTypeConstants.BOOLEAN_STRING;

@Schema(description = "管理后台 - 编码生成规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AutoCodeRuleRespVO {

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "644")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则编码")
    private String ruleCode;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("规则名称")
    private String ruleName;

    @Schema(description = "描述")
    @ExcelProperty("描述")
    private String ruleDesc;

    @Schema(description = "最大长度")
    @ExcelProperty("最大长度")
    private Integer maxLength;

    @Schema(description = "是否补齐", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否补齐")
    private Boolean isPadded;

    @Schema(description = "补齐字符")
    @ExcelProperty("补齐字符")
    private String paddedChar;

    @Schema(description = "补齐方式")
    @ExcelProperty("补齐方式")
    private String paddedMethod;

    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否启用", converter = DictConvert.class)
    @DictFormat(BOOLEAN_STRING)
    private Boolean enableFlag;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

}