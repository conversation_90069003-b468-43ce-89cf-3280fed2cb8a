package cn.iocoder.yudao.module.system.service.autocodepart;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import cn.iocoder.yudao.module.system.dal.mysql.autocodepart.AutoCodePartMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_PART_CODE_NAME_INDEX_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_PART_NOT_EXISTS;

/**
 * 编码生成规则组成 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AutoCodePartServiceImpl implements AutoCodePartService {

    @Resource
    private AutoCodePartMapper autoCodePartMapper;

    @Override
    public Long createAutoCodePart(AutoCodePartSaveReqVO createReqVO) {
        // 校验数据
        validateCodeAndNameAndIndex(createReqVO);
        // 插入
        AutoCodePartDO autoCodePart = BeanUtils.toBean(createReqVO, AutoCodePartDO.class);
        autoCodePartMapper.insert(autoCodePart);
        // 返回
        return autoCodePart.getPartId();
    }

    @Override
    public void updateAutoCodePart(AutoCodePartSaveReqVO updateReqVO) {
        // 校验存在
        validateAutoCodePartExists(updateReqVO.getPartId());
        // 校验数据
        validateCodeAndNameAndIndex(updateReqVO);
        // 更新
        AutoCodePartDO updateObj = BeanUtils.toBean(updateReqVO, AutoCodePartDO.class);
        autoCodePartMapper.updateById(updateObj);
    }

    @Override
    public void deleteAutoCodePart(Long id) {
        // 校验存在
        validateAutoCodePartExists(id);
        // 删除
        autoCodePartMapper.deleteById(id);
    }

    private void validateAutoCodePartExists(Long id) {
        if (autoCodePartMapper.selectById(id) == null) {
            throw exception(AUTO_CODE_PART_NOT_EXISTS);
        }
    }

    private void validateCodeAndNameAndIndex(AutoCodePartSaveReqVO saveReqVO) {
        AutoCodePartDO old = autoCodePartMapper.selectOne(new LambdaQueryWrapperX<AutoCodePartDO>()
                .eq(AutoCodePartDO::getRuleId, saveReqVO.getRuleId())
                .and(wrapper -> wrapper.eq(AutoCodePartDO::getPartCode, saveReqVO.getPartCode())
                        .or().eq(AutoCodePartDO::getPartName, saveReqVO.getPartName())
                        .or().eq(AutoCodePartDO::getPartIndex, saveReqVO.getPartIndex())));

        if (old != null && saveReqVO.getPartId() != null && !old.getPartId().equals(saveReqVO.getPartId()))
            throw exception(AUTO_CODE_PART_CODE_NAME_INDEX_DUPLICATE);
    }

    @Override
    public AutoCodePartDO getAutoCodePart(Long id) {
        return autoCodePartMapper.selectById(id);
    }

    @Override
    public PageResult<AutoCodePartDO> getAutoCodePartPage(AutoCodePartPageReqVO pageReqVO) {
        return autoCodePartMapper.selectPage(pageReqVO);
    }

}