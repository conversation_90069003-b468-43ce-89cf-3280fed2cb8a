package cn.iocoder.yudao.module.system.service.autocode;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRulePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRuleSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocode.AutoCodeRuleDO;
import cn.iocoder.yudao.module.system.dal.mysql.autocode.AutoCodeRuleMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.*;

/**
 * 编码生成规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AutoCodeRuleServiceImpl implements AutoCodeRuleService {

    @Resource
    private AutoCodeRuleMapper autoCodeRuleMapper;

    @LogRecord(type = AUTO_CODE_RULE, subType = AUTO_CODE_RULE_CREATE_SUB_TYPE, bizNo = "{{#createReqVO.ruleCode}}",
            success = AUTO_CODE_RULE_CREATE_SUCCESS)
    @Override
    public Long createAutoCodeRule(AutoCodeRuleSaveReqVO createReqVO) {
        // 校验数据
        validateCodeAndName(createReqVO);
        // 插入
        AutoCodeRuleDO autoCodeRule = BeanUtils.toBean(createReqVO, AutoCodeRuleDO.class);
        if (!autoCodeRule.getIsPadded()) {
            autoCodeRule.setPaddedChar(null);
            autoCodeRule.setPaddedMethod(null);
        }
        autoCodeRuleMapper.insert(autoCodeRule);
        // 返回
        return autoCodeRule.getRuleId();
    }

    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = AUTO_CODE_RULE, subType = AUTO_CODE_RULE_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
            success = AUTO_CODE_RULE_UPDATE_SUCCESS)
    @Override
    public void updateAutoCodeRule(AutoCodeRuleSaveReqVO updateReqVO) {
        // 校验存在
        AutoCodeRuleDO oldRule = validateAutoCodeRuleExists(updateReqVO.getRuleId());
        // 校验数据
        validateCodeAndName(updateReqVO);
        // 更新
        AutoCodeRuleDO updateObj = BeanUtils.toBean(updateReqVO, AutoCodeRuleDO.class);
        if (!updateObj.getIsPadded()) {
            updateObj.setPaddedChar(null);
            updateObj.setPaddedMethod(null);
        }
        autoCodeRuleMapper.updateById(updateObj);
        // 日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldRule, AutoCodeRuleSaveReqVO.class));
        LogRecordContext.putVariable("role", updateObj);
    }

    @Override
    public void deleteAutoCodeRule(Long id) {
        // 校验存在
        validateAutoCodeRuleExists(id);
        // 删除
        autoCodeRuleMapper.deleteById(id);
    }

    private AutoCodeRuleDO validateAutoCodeRuleExists(Long id) {
        return Optional.ofNullable(autoCodeRuleMapper.selectById(id))
                .orElseThrow(() -> exception(AUTO_CODE_RULE_NOT_EXISTS));
    }

    private void validateCodeAndName(AutoCodeRuleSaveReqVO reqVO){
        AutoCodeRuleDO codeDO = autoCodeRuleMapper.selectOne(new LambdaQueryWrapperX<AutoCodeRuleDO>()
                .eq(AutoCodeRuleDO::getRuleCode, reqVO.getRuleCode())
                .neIfPresent(AutoCodeRuleDO::getRuleId, reqVO.getRuleId()));
        if (codeDO != null) throw exception(AUTO_CODE_RULE_CODE_DUPLICATE);
        AutoCodeRuleDO nameDO = autoCodeRuleMapper.selectOne(new LambdaQueryWrapperX<AutoCodeRuleDO>()
                .eq(AutoCodeRuleDO::getRuleName, reqVO.getRuleName())
                .neIfPresent(AutoCodeRuleDO::getRuleId, reqVO.getRuleId()));
        if (nameDO != null) throw exception(AUTO_CODE_RULE_NAME_DUPLICATE);
    }

    @Override
    public AutoCodeRuleDO getAutoCodeRule(Long id) {
        return autoCodeRuleMapper.selectById(id);
    }

    @Override
    public AutoCodeRuleDO getByCode(String ruleCode) {
        List<AutoCodeRuleDO> list = autoCodeRuleMapper.selectList(AutoCodeRuleDO::getRuleCode, ruleCode);
        if (CollUtil.isEmpty(list)) throw exception(AUTO_CODE_RULE_NOT_EXISTS);
        return list.get(0);
    }

    @Override
    public PageResult<AutoCodeRuleDO> getAutoCodeRulePage(AutoCodeRulePageReqVO pageReqVO) {
        return autoCodeRuleMapper.selectPage(pageReqVO);
    }

}