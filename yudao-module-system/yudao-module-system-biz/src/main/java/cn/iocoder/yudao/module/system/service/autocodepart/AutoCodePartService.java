package cn.iocoder.yudao.module.system.service.autocodepart;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 编码生成规则组成 Service 接口
 *
 * <AUTHOR>
 */
public interface AutoCodePartService {

    /**
     * 创建编码生成规则组成
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAutoCodePart(@Valid AutoCodePartSaveReqVO createReqVO);

    /**
     * 更新编码生成规则组成
     *
     * @param updateReqVO 更新信息
     */
    void updateAutoCodePart(@Valid AutoCodePartSaveReqVO updateReqVO);

    /**
     * 删除编码生成规则组成
     *
     * @param id 编号
     */
    void deleteAutoCodePart(Long id);

    /**
     * 获得编码生成规则组成
     *
     * @param id 编号
     * @return 编码生成规则组成
     */
    AutoCodePartDO getAutoCodePart(Long id);

    /**
     * 获得编码生成规则组成分页
     *
     * @param pageReqVO 分页查询
     * @return 编码生成规则组成分页
     */
    PageResult<AutoCodePartDO> getAutoCodePartPage(AutoCodePartPageReqVO pageReqVO);

}