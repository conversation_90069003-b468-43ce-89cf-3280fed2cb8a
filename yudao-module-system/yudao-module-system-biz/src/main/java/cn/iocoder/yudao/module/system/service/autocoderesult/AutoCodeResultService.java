package cn.iocoder.yudao.module.system.service.autocoderesult;

import cn.iocoder.yudao.module.system.controller.admin.autocoderesult.vo.AutoCodeResultSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocoderesult.AutoCodeResultDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 编码生成记录 Service 接口
 *
 * <AUTHOR>
 */
public interface AutoCodeResultService {

    /**
     * 创建编码生成记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAutoCodeResult(@Valid AutoCodeResultSaveReqVO createReqVO);

    /**
     * 更新编码生成记录
     *
     * @param updateReqVO 更新信息
     */
    void updateAutoCodeResult(@Valid AutoCodeResultSaveReqVO updateReqVO);

    /**
     * 获得编码生成记录列表
     *
     * @param params 查询参数
     * @return 编码生成记录分页
     */
    List<AutoCodeResultDO> list(AutoCodeResultDO params);

}