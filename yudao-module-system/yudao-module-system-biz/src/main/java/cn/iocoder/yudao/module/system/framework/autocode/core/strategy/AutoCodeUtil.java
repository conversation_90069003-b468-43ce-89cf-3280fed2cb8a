package cn.iocoder.yudao.module.system.framework.autocode.core.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.autocode.AutoCodeApi;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocoderesult.vo.AutoCodeResultSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocode.AutoCodeRuleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocoderesult.AutoCodeResultDO;
import cn.iocoder.yudao.module.system.service.autocode.AutoCodeRuleService;
import cn.iocoder.yudao.module.system.service.autocodepart.AutoCodePartService;
import cn.iocoder.yudao.module.system.service.autocoderesult.AutoCodeResultService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.*;
import static cn.iocoder.yudao.module.system.enums.autocode.PartTypeEnum.SERIALNO;

/**
 * 自动编码并获取的工具类
 * 单体应用线程安全，如果多模块多应用部署，需要修改两个缓存数据方式
 */
@Service
public class AutoCodeUtil implements AutoCodeApi {
    public static ThreadLocal<Boolean> threadLocal = new ThreadLocal<>();
    private String lastSerialNo;

    @Autowired
    private AutoCodeRuleService autoCodeRuleService;

    @Autowired
    private AutoCodePartService autoCodePartService;

    @Autowired
    private AutoCodeResultService autoCodeResultService;

    @Autowired
    private PartTypeHandler partTypeHandler;


    @LogRecord(type = AUTO_CODE_RECORD, subType = AUTO_CODE_RECORD_GEN, bizNo = "{{#rule.ruleId}}",
            success = AUTO_CODE_RECORD_GEN_SUCCESS)
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public synchronized String genSerialCode(String ruleCode, String inputCharacter) {
        //查找编码规则
        AutoCodeRuleDO rule = autoCodeRuleService.getByCode(ruleCode);
        //查找规则组成
        AutoCodePartPageReqVO partParam = new AutoCodePartPageReqVO().setRuleId(rule.getRuleId());
        partParam.setRuleId(rule.getRuleId());
        List<AutoCodePartDO> parts = autoCodePartService.getAutoCodePartPage(partParam).getList();
        long serialCount = parts.stream().filter(part -> SERIALNO.getCode().equals(part.getPartType())).count();
        if (serialCount >= 2) throw exception(AUTO_CODE_PART_SERIALNO_TOO_MUCH);

        StringBuilder buff = new StringBuilder();
        parts.forEach(codePart -> {
            codePart.setInputCharacter(inputCharacter);
            //根据当前组成部分，获取当前组成部分的结果
            String partStr = partTypeHandler.choiceExecute(codePart);

            //如果是流水号部分，则进行记录
            if (StringUtils.equals(codePart.getPartType(), SERIALNO.getCode())) {
                lastSerialNo = partStr;
            }
            //将获取到的部分组装进整体编码中
            buff.append(partStr);
        });
        Assert.notBlank(buff.toString(), "规则：[{}]生成的编码为空！", ruleCode);

        String autoCode = paddingStr(rule, buff);
        //将生成结果保存到数据库
        saveAutoCodeResult(rule, autoCode, inputCharacter);
        // 日志上下文
        LogRecordContext.putVariable("code", autoCode);
        LogRecordContext.putVariable("rule", rule);
        return autoCode;
    }

    /**
     * 根据编码规则的配置进行补齐操作
     *
     * @param rule
     * @param sb
     * @return
     */
    private String paddingStr(AutoCodeRuleDO rule, StringBuilder sb) {
        int maxLength = rule.getMaxLength();
        long length = maxLength - sb.length();
        if (length < 0) throw exception(AUTO_CODE_RESULT_TOO_LONG, sb.toString(), maxLength);
        if (rule.getIsPadded()) {
            String paddingChar = rule.getPaddedChar();
            StringBuilder resultStr = new StringBuilder();
            if ("L".equals(rule.getPaddedMethod())) {
                //左补齐
                //使用指定字符补齐左侧后,再将生成的编码添加到右侧
                for (; length > 0; length--) {
                    resultStr.append(paddingChar);
                }
                resultStr.append(sb);
            } else {
                //右补齐
                //将生成的编码添加到左侧后,再使用指定字符补齐右侧
                resultStr.append(sb);
                for (; length > 0; length--) {
                    resultStr.append(paddingChar);
                }
            }
            return resultStr.toString();
        }
        return sb.toString(); //如果不需要补齐，则直接返回
    }

    private void saveAutoCodeResult(AutoCodeRuleDO rule, String autoCode, String inputChar) {
        Boolean flag = threadLocal.get(); //针对当前线程的判断 flag = true则数据库中没有当前规则的生成记录
        if (flag != null && flag) {
            AutoCodeResultSaveReqVO rs = new AutoCodeResultSaveReqVO()
                    .setRuleId(rule.getRuleId())
                    .setGenDate(DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"))
                    .setLastResult(autoCode)
                    .setGenIndex(1)
                    .setLastSerialNo(NumberUtils.parseInt(lastSerialNo))
                    .setLastInputChar(inputChar);
            autoCodeResultService.createAutoCodeResult(rs);
        } else {
            //直接更新对应的记录（我们默认非流水号模式下一个RULE_CODE只有一种方式）
            AutoCodeResultDO bo = new AutoCodeResultDO();
            bo.setRuleId(rule.getRuleId());
            List<AutoCodeResultDO> results = autoCodeResultService.list(bo);
            if (CollUtil.isEmpty(results)) throw exception(AUTO_CODE_RESULT_NOT_EXISTS);
            AutoCodeResultDO rs = results.get(0);
            rs.setLastResult(autoCode);
            rs.setGenDate(DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
            rs.setGenIndex(rs.getGenIndex() + 1);
            rs.setLastSerialNo(NumberUtils.parseInt(lastSerialNo));
            rs.setLastInputChar(inputChar);
            autoCodeResultService.updateAutoCodeResult(BeanUtils.toBean(rs, AutoCodeResultSaveReqVO.class));
        }

    }
}
