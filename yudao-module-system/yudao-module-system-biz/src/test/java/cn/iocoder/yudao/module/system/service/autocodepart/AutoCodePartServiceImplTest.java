package cn.iocoder.yudao.module.system.service.autocodepart;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocodepart.vo.AutoCodePartSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocodepart.AutoCodePartDO;
import cn.iocoder.yudao.module.system.dal.mysql.autocodepart.AutoCodePartMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_PART_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link AutoCodePartServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(AutoCodePartServiceImpl.class)
public class AutoCodePartServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AutoCodePartServiceImpl autoCodePartService;

    @Resource
    private AutoCodePartMapper autoCodePartMapper;

    @Test
    public void testCreateAutoCodePart_success() {
        // 准备参数
        AutoCodePartSaveReqVO createReqVO = randomPojo(AutoCodePartSaveReqVO.class).setPartId(null);

        // 调用
        Long autoCodePartId = autoCodePartService.createAutoCodePart(createReqVO);
        // 断言
        assertNotNull(autoCodePartId);
        // 校验记录的属性是否正确
        AutoCodePartDO autoCodePart = autoCodePartMapper.selectById(autoCodePartId);
        assertPojoEquals(createReqVO, autoCodePart, "id");
    }

    @Test
    public void testUpdateAutoCodePart_success() {
        // mock 数据
        AutoCodePartDO dbAutoCodePart = randomPojo(AutoCodePartDO.class);
        autoCodePartMapper.insert(dbAutoCodePart);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AutoCodePartSaveReqVO updateReqVO = randomPojo(AutoCodePartSaveReqVO.class, o -> {
            o.setPartId(dbAutoCodePart.getPartId()); // 设置更新的 ID
        });

        // 调用
        autoCodePartService.updateAutoCodePart(updateReqVO);
        // 校验是否更新正确
        AutoCodePartDO autoCodePart = autoCodePartMapper.selectById(updateReqVO.getPartId()); // 获取最新的
        assertPojoEquals(updateReqVO, autoCodePart);
    }

    @Test
    public void testUpdateAutoCodePart_notExists() {
        // 准备参数
        AutoCodePartSaveReqVO updateReqVO = randomPojo(AutoCodePartSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> autoCodePartService.updateAutoCodePart(updateReqVO), AUTO_CODE_PART_NOT_EXISTS);
    }

    @Test
    public void testDeleteAutoCodePart_success() {
        // mock 数据
        AutoCodePartDO dbAutoCodePart = randomPojo(AutoCodePartDO.class);
        autoCodePartMapper.insert(dbAutoCodePart);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAutoCodePart.getPartId();

        // 调用
        autoCodePartService.deleteAutoCodePart(id);
       // 校验数据不存在了
       assertNull(autoCodePartMapper.selectById(id));
    }

    @Test
    public void testDeleteAutoCodePart_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> autoCodePartService.deleteAutoCodePart(id), AUTO_CODE_PART_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetAutoCodePartPage() {
       // mock 数据
       AutoCodePartDO dbAutoCodePart = randomPojo(AutoCodePartDO.class, o -> { // 等会查询到
           o.setPartCode(null);
           o.setPartName(null);
       });
       autoCodePartMapper.insert(dbAutoCodePart);
       // 测试 partCode 不匹配
       autoCodePartMapper.insert(cloneIgnoreId(dbAutoCodePart, o -> o.setPartCode(null)));
       // 测试 partName 不匹配
       autoCodePartMapper.insert(cloneIgnoreId(dbAutoCodePart, o -> o.setPartName(null)));
       // 准备参数
       AutoCodePartPageReqVO reqVO = new AutoCodePartPageReqVO();
       reqVO.setPartCode(null);
       reqVO.setPartName(null);

       // 调用
       PageResult<AutoCodePartDO> pageResult = autoCodePartService.getAutoCodePartPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbAutoCodePart, pageResult.getList().get(0));
    }

}