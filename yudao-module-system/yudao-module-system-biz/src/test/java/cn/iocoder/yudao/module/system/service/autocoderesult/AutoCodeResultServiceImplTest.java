package cn.iocoder.yudao.module.system.service.autocoderesult;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.system.controller.admin.autocoderesult.vo.AutoCodeResultSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocoderesult.AutoCodeResultDO;
import cn.iocoder.yudao.module.system.dal.mysql.autocoderesult.AutoCodeResultMapper;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_RESULT_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * {@link AutoCodeResultServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(AutoCodeResultServiceImpl.class)
public class AutoCodeResultServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AutoCodeResultServiceImpl autoCodeResultService;

    @Resource
    private AutoCodeResultMapper autoCodeResultMapper;

    @Test
    public void testCreateAutoCodeResult_success() {
        // 准备参数
        AutoCodeResultSaveReqVO createReqVO = randomPojo(AutoCodeResultSaveReqVO.class).setId(null);

        // 调用
        Long autoCodeResultId = autoCodeResultService.createAutoCodeResult(createReqVO);
        // 断言
        assertNotNull(autoCodeResultId);
        // 校验记录的属性是否正确
        AutoCodeResultDO autoCodeResult = autoCodeResultMapper.selectById(autoCodeResultId);
        assertPojoEquals(createReqVO, autoCodeResult, "id");
    }

    @Test
    public void testUpdateAutoCodeResult_success() {
        // mock 数据
        AutoCodeResultDO dbAutoCodeResult = randomPojo(AutoCodeResultDO.class);
        autoCodeResultMapper.insert(dbAutoCodeResult);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AutoCodeResultSaveReqVO updateReqVO = randomPojo(AutoCodeResultSaveReqVO.class, o -> {
            o.setId(dbAutoCodeResult.getId()); // 设置更新的 ID
        });

        // 调用
        autoCodeResultService.updateAutoCodeResult(updateReqVO);
        // 校验是否更新正确
        AutoCodeResultDO autoCodeResult = autoCodeResultMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, autoCodeResult);
    }

    @Test
    public void testUpdateAutoCodeResult_notExists() {
        // 准备参数
        AutoCodeResultSaveReqVO updateReqVO = randomPojo(AutoCodeResultSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> autoCodeResultService.updateAutoCodeResult(updateReqVO), AUTO_CODE_RESULT_NOT_EXISTS);
    }

}