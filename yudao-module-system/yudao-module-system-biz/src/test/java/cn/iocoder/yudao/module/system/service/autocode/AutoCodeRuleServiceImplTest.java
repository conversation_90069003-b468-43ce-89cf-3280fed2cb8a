package cn.iocoder.yudao.module.system.service.autocode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRulePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.autocode.vo.AutoCodeRuleSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.autocode.AutoCodeRuleDO;
import cn.iocoder.yudao.module.system.dal.mysql.autocode.AutoCodeRuleMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTO_CODE_RULE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link AutoCodeRuleServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(AutoCodeRuleServiceImpl.class)
public class AutoCodeRuleServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AutoCodeRuleServiceImpl autoCodeRuleService;

    @Resource
    private AutoCodeRuleMapper autoCodeRuleMapper;

    @Test
    public void testCreateAutoCodeRule_success() {
        // 准备参数
        AutoCodeRuleSaveReqVO createReqVO = randomPojo(AutoCodeRuleSaveReqVO.class).setRuleId(null);

        // 调用
        Long autoCodeRuleId = autoCodeRuleService.createAutoCodeRule(createReqVO);
        // 断言
        assertNotNull(autoCodeRuleId);
        // 校验记录的属性是否正确
        AutoCodeRuleDO autoCodeRule = autoCodeRuleMapper.selectById(autoCodeRuleId);
        assertPojoEquals(createReqVO, autoCodeRule, "id");
    }

    @Test
    public void testUpdateAutoCodeRule_success() {
        // mock 数据
        AutoCodeRuleDO dbAutoCodeRule = randomPojo(AutoCodeRuleDO.class);
        autoCodeRuleMapper.insert(dbAutoCodeRule);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AutoCodeRuleSaveReqVO updateReqVO = randomPojo(AutoCodeRuleSaveReqVO.class, o -> {
            o.setRuleId(dbAutoCodeRule.getRuleId()); // 设置更新的 ID
        });

        // 调用
        autoCodeRuleService.updateAutoCodeRule(updateReqVO);
        // 校验是否更新正确
        AutoCodeRuleDO autoCodeRule = autoCodeRuleMapper.selectById(updateReqVO.getRuleId()); // 获取最新的
        assertPojoEquals(updateReqVO, autoCodeRule);
    }

    @Test
    public void testUpdateAutoCodeRule_notExists() {
        // 准备参数
        AutoCodeRuleSaveReqVO updateReqVO = randomPojo(AutoCodeRuleSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> autoCodeRuleService.updateAutoCodeRule(updateReqVO), AUTO_CODE_RULE_NOT_EXISTS);
    }

    @Test
    public void testDeleteAutoCodeRule_success() {
        // mock 数据
        AutoCodeRuleDO dbAutoCodeRule = randomPojo(AutoCodeRuleDO.class);
        autoCodeRuleMapper.insert(dbAutoCodeRule);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAutoCodeRule.getRuleId();

        // 调用
        autoCodeRuleService.deleteAutoCodeRule(id);
       // 校验数据不存在了
       assertNull(autoCodeRuleMapper.selectById(id));
    }

    @Test
    public void testDeleteAutoCodeRule_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> autoCodeRuleService.deleteAutoCodeRule(id), AUTO_CODE_RULE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetAutoCodeRulePage() {
       // mock 数据
       AutoCodeRuleDO dbAutoCodeRule = randomPojo(AutoCodeRuleDO.class, o -> { // 等会查询到
           o.setRuleCode(null);
           o.setRuleName(null);
       });
       autoCodeRuleMapper.insert(dbAutoCodeRule);
       // 测试 ruleCode 不匹配
       autoCodeRuleMapper.insert(cloneIgnoreId(dbAutoCodeRule, o -> o.setRuleCode(null)));
       // 测试 ruleName 不匹配
       autoCodeRuleMapper.insert(cloneIgnoreId(dbAutoCodeRule, o -> o.setRuleName(null)));
       // 准备参数
       AutoCodeRulePageReqVO reqVO = new AutoCodeRulePageReqVO();
       reqVO.setRuleCode(null);
       reqVO.setRuleName(null);

       // 调用
       PageResult<AutoCodeRuleDO> pageResult = autoCodeRuleService.getAutoCodeRulePage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbAutoCodeRule, pageResult.getList().get(0));
    }

}