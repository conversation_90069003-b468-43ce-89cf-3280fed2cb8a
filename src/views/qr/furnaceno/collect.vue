<template>
  <div class="app-container">
    <!-- 搜索栏 -->
      <el-form :inline="true" :model="orderInfo" class="search-form" label-width="auto">
        <el-row :gutter="16">
          <el-col :span="4">
            <el-form-item label="生产工单号" class="required-label">
              <el-input
                v-model="orderInfo.moNo"
                placeholder="请输入生产工单号"
                size="small"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售订单号">
              <el-input
                v-model="orderInfo.orderNo"
                placeholder="销售订单号"
                size="small"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售订单行号">
              <el-input
                v-model="orderInfo.orderLineNo"
                placeholder="销售订单行号"
                size="small"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" size="small" @click="getList" style="margin-right: 12px;">
                <Icon icon="ep:search" class="mr-5px" />
                {{ t('common.query') }}
              </el-button>
              <el-button
                :disabled="tableList.length <= 0"
                type="success"
                size="small"
                @click="submit"
              >
                <Icon icon="ep:position" class="mr-5px" />
                {{ t('common.submit') }}
              </el-button>
              <el-button
                :disabled="!copiedRowData"
                type="warning"
                size="small"
                @click="pasteToAllRows"
                style="margin-left: 8px;"
              >
                <Icon icon="ep:document-copy" class="mr-5px" />
                粘贴到所有行
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    <!-- 数据表格 -->
      <el-table v-show="tableList.length > 0" :data="tableList" :key="tableKey" row-key="serial" border stripe style="width: 100%">
        <el-table-column align="center" label="整机系列号" prop="serial" width="150" />
        <el-table-column align="center" label="操作" width="140" fixed="left">
          <template #default="{ row, $index }">
            <div class="operation-buttons">
              <el-button
                type="primary"
                size="small"
                @click="copyRowData(row, $index)"
                :disabled="!hasRowData(row)"
                title="复制当前行的材质、炉号等数据"
              >
                <Icon icon="ep:document-copy" style="margin-right: 2px;" />
                复制
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="pasteRowData(row, $index)"
                :disabled="!copiedRowData"
                title="粘贴已复制的数据到当前行"
              >
                <Icon icon="ep:document-add" style="margin-right: 2px;" />
                粘贴
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-for="part in partsList"
          :key="part.partClassCode"
          :label="part.partClassDesc"
          align="center"
        >
          <el-table-column
            v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL"
            align="center"
            label="零件系列号"
          >
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).partSerial">
                <el-input
                  v-model="(row[part.partClassCode] as PartDetail).partSerial"
                  placeholder="请输入零件系列号"
                  @blur="checkPartSerial(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" label="材质">
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).texture">
                <span v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL">{{
                  (row[part.partClassCode] as PartDetail).texture
                }}</span>
                <el-input
                  v-else
                  v-model="(row[part.partClassCode] as PartDetail).texture"
                  placeholder="请输入材质"
                  @blur="checkTextureFurnaceNo(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" label="炉号">
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).furnaceNo">
                <span v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL">{{
                  (row[part.partClassCode] as PartDetail).furnaceNo
                }}</span>
                <el-input
                  v-else
                  v-model="(row[part.partClassCode] as PartDetail).furnaceNo"
                  placeholder="请输入炉号"
                  @blur="checkTextureFurnaceNo(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" label="热处理炉号" v-if="part.isHotFurnace">
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).hotFurnaceNo" >
                <span v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL" >{{
                    (row[part.partClassCode] as PartDetail).hotFurnaceNo
                  }}</span>
                <el-input
                  v-else
                  v-model="(row[part.partClassCode] as PartDetail).hotFurnaceNo"
                  placeholder="请输入热处理炉号"
                  @blur="checkTextureFurnaceNo(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
  </div>
</template>
<script lang="ts" setup>
import { FurnaceNoCollectApi, FurnaceNoCListApi, FurnaceNoCollectVO } from '@/api/qr/furnaceno'
import { SalesOrderApi } from '@/api/qr/reports/salesOrder'
import { MoInfoApi } from '@/api/qr/reports/mo'
import { ValveTypePartsBatchFlagEnum } from '@/utils/constants'


// 重新定义类型结构
interface PartDetail {
    id?: number
    moNo: string
    partSerial: string
    texture: string
    furnaceNo: string
    hotFurnaceNo: string
    batchFlag?: number
  isHotFurnace?: string
  isRt?: string
  partClassCode?: string
  }

interface FunaceNoCollectTable {
  serial: string
  [key: string]: PartDetail | string
}

const message = useMessage()
const { t } = useI18n()

const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

// 复制粘贴相关变量
const copiedRowData = ref<any>(null)
// 表格刷新key
const tableKey = ref(0)

/** 订单行号 */
const orderInfo = reactive<{
  orderNo?: string
  orderLineNo?: string
  moNo?: string
}>({})

// 订单行的历史采集情况
const furnaceNoCollectRecord = ref<FurnaceNoCollectVO[]>([])

// 通过工单号获取销售订单信息
const getOrderInfoByMoNo = async () => {
  if (!orderInfo.moNo) {
    ElMessage.error('生产工单号不能为空')
    return false
  }
  
  try {
    // 使用MoInfoApi获取销售订单信息
    const result = await MoInfoApi.getOrderInfoByMoNo(orderInfo.moNo)
    if (result && result.soNo && result.soLineNo) {
      orderInfo.orderNo = result.soNo
      orderInfo.orderLineNo = result.soLineNo
      return true
    } else {
      ElMessage.error('未找到该生产工单号对应的销售订单信息')
      return false
    }
  } catch (error) {
    ElMessage.error('获取销售订单信息失败')
    return false
  }
}

const getFurnaceNoCollectInfo = async () => {
  if (!orderInfo.orderNo || !orderInfo.orderLineNo) {
    return
  }

      furnaceNoCollectRecord.value = await FurnaceNoCListApi.getFurnaceNoCollectInfo({
        orderNo: orderInfo.orderNo,
        orderLineNo: orderInfo.orderLineNo
      })
}

/** 销售订单行的物料主要部件列表  */
const partsList = ref<
  Array<{ partClassCode: string;
  partClassDesc: string;
  index: number;
  batchFlag?: number
  isHotFurnace?: boolean
}>>([])

const getPartsList = async () => {
  if (!orderInfo.orderNo || !orderInfo.orderLineNo) {
    return false
  }

  partsList.value = (await SalesOrderApi.getSoPartsList({
    orderNo: orderInfo.orderNo,
    orderLineNo: orderInfo.orderLineNo
  } as any)).filter(
      (item) => item.batchFlag > ValveTypePartsBatchFlagEnum.NONE
    )

  return true
}

const tableList = ref<FunaceNoCollectTable[]>([])

/** 初始化炉号采集表格 */
const getList = async () => {
  // 首先通过工单号获取销售订单信息
  const hasOrderInfo = await getOrderInfoByMoNo()
  if (!hasOrderInfo) return

  // 获取部件列表
  const hasPartsList = await getPartsList()
  if (!hasPartsList) return

  // 获取历史采集信息
  await getFurnaceNoCollectInfo()

  // 获取整机序列号
  const pageResult = await FurnaceNoCollectApi.pageSerials({ 
    orderNo: orderInfo.orderNo,
    orderLineNo: orderInfo.orderLineNo,
    moNo: orderInfo.moNo,
    ...queryParams 
  })
  total.value = pageResult.total


  tableList.value = pageResult.list.map((item) => {
    const tableItem: FunaceNoCollectTable = {
      serial: item.serial
    }


    partsList.value.map((part) => {
      const record = furnaceNoCollectRecord.value.find(
        (r) => r.partClassCode === part.partClassCode && r.serial === item.serial
      )

      tableItem[part.partClassCode] = {
        id: record?.id,
        partClassCode: part.partClassCode,
        partSerial: record?.partSerial || '',
        texture: record?.texture || '',
        furnaceNo: record?.furnaceNo || '',
        hotFurnaceNo: record?.hotFurnaceNo || '',
        isHotFurnace: record?.isHotFurnace || '0',
        isRt: record?.isRt || '0',
        batchFlag: part.batchFlag,
        moNo: record?.moNo || ''
      }
    })

    return tableItem
  })

  // 重置表格key确保重新渲染
  tableKey.value++
}


/** 校验零件系列号 */
const checkPartSerial = async (row: FunaceNoCollectTable, partClassCode: string) => {
  // 添加类型断言
  const partDetail = row[partClassCode] as PartDetail
  if (!partDetail.partSerial) return
  try {
    const partSerials = tableList.value
      .map((item) => partsList.value.map((part) => (item[part.partClassCode] as PartDetail).partSerial))
      .flatMap((a) => a)
    if (partSerials.filter((item) => item === partDetail.partSerial).length > 1) {
      message.error('零件系列号重复')
      throw new Error()
    }

    const res = await FurnaceNoCollectApi.checkPartSerial({
      partSerial: partDetail.partSerial,
      partClassCode
    })

    if (res.serial && res.serial != row.serial) {
      message.error('零件系列号已被其他整机占用')
      throw new Error()
    }
    partDetail.texture = res.texture
    partDetail.furnaceNo = res.furnaceNo
    partDetail.hotFurnaceNo= res.hotFurnaceNo
    partDetail.isRt = res.isRt || '0'
  } catch (e) {
    partDetail.partSerial = ''
    partDetail.texture = ''
    partDetail.furnaceNo = ''
    partDetail.hotFurnaceNo= ''
    partDetail.isRt = '0'
  }
}

/** 校验材质和炉号 */
const checkTextureFurnaceNo = async (row: FunaceNoCollectTable, partClassCode: string) => {
  // 添加类型断言
  const partDetail = row[partClassCode] as PartDetail
  if (!partDetail.texture || !partDetail.furnaceNo) return

  // 保存原始数据
  const originalData = {
    texture: partDetail.texture,
    furnaceNo: partDetail.furnaceNo,
    hotFurnaceNo: partDetail.hotFurnaceNo
  }

  try {
    await FurnaceNoCollectApi.checkTextureAndFurnaceNo({
      texture: partDetail.texture,
      furnaceNo: partDetail.furnaceNo,
      hotFurnaceNo: partDetail.hotFurnaceNo,
      partClassCode
    })

    // 校验成功
    message.success('材质炉号校验通过')

  } catch (e: any) {
    console.error('校验材质炉号时发生错误:', e)

    // 根据错误码和消息判断错误类型
    const errorCode = e.response?.data?.code
    const errorMsg = e.response?.data?.msg || e.message

    if (errorCode === 1201008003) {
      // TEXTURE_FURNACE_NO_REPORT - 不存在相关材质炉号的报告数据
      message.warning('不存在相关材质炉号的报告数据，已清空炉号，请重新输入正确的炉号')
      // 只清空炉号相关字段，保留材质
      partDetail.furnaceNo = ''
      partDetail.hotFurnaceNo = ''
    } else if (errorCode === 1201008006) {
      // TEXTURE_FURNACE_NO_PART_CLASS_ERROR - 材质炉号不属于部件大类
      message.warning(`该材质炉号不属于当前部件大类，请确认输入是否正确`)
      // 这种情况下询问用户是否清空，因为确实不匹配
      ElMessageBox.confirm(
        '该材质炉号不属于当前部件大类，是否清空重新输入？',
        '确认操作',
        {
          confirmButtonText: '清空重新输入',
          cancelButtonText: '保留当前输入',
          type: 'warning',
        }
      ).then(() => {
        partDetail.texture = ''
        partDetail.furnaceNo = ''
        partDetail.hotFurnaceNo = ''
        message.info('已清空材质炉号信息，请重新输入')
      }).catch(() => {
        // 用户选择保留数据，不做任何操作
      })
    } else if (e.response?.status === 404) {
      // 404错误，表示没有找到相关数据
      message.warning('未找到相关的材质炉号报告数据，请确认输入是否正确')
    } else if (e.response?.status >= 400 && e.response?.status < 500) {
      // 其他4xx错误
      message.warning(`材质炉号校验失败: ${errorMsg || '请检查输入的材质和炉号格式'}`)
    } else {
      // 网络错误或服务器错误
      message.warning('校验材质炉号时发生网络错误，请检查网络连接或稍后重试')
    }

    // 重要：除了特定错误情况外，不清空用户输入的数据
  }
}

/** 提交数据 */
const submit = async () => {
  // 校验
  const saveList = tableList.value.filter((tableItem) => {
    let hasEmpty = false // 当前行是否有空项
    let allEmpty = true // 当前行是否全是空项

    partsList.value.forEach((part) => {
      // 添加类型断言
      const partDetail = tableItem[part.partClassCode] as PartDetail
      if (partDetail.texture || partDetail.furnaceNo) {
        allEmpty = false
      }
      if (!partDetail.texture || !partDetail.furnaceNo) {
        hasEmpty = true
      }
      if (!orderInfo.moNo || orderInfo.moNo.includes(' ')) {
        message.error('生产工单号不能包含空格或为空')
        throw new Error()
      } else {
        partDetail.moNo = orderInfo.moNo
      }
    })

    if (allEmpty) return true
    if (hasEmpty) return false
    return true
  })
  
  if (saveList.length === 0) {
    message.error('没有可提交的数据')
    return
  }
  
  // 转换数据格式
  const list = saveList
    .map((tableItem) =>
      partsList.value.map((part) => {
        const partDetail = tableItem[part.partClassCode] as PartDetail
        return {
          id: partDetail.id,
          moNo: partDetail.moNo,
          partSerial: partDetail.partSerial,
          texture: partDetail.texture,
          furnaceNo: partDetail.furnaceNo,
          hotFurnaceNo: partDetail.hotFurnaceNo,
          batchFlag: partDetail.batchFlag,
        partClassCode: part.partClassCode,
          serial: tableItem.serial,
          isRt: partDetail.isRt || '0',
          isHotFurnace: part.isHotFurnace ? '1' : '0'
        }
      })
    )
    .flatMap((a) => a)
    
  await message.confirm('确认提交吗？注意：存在空项的数据将不会被提交')

  // 保存
  await FurnaceNoCollectApi.saveFurnaceNoCollect(list)
  message.success(t('common.submit') + t('common.success'))
}

/** 检查行是否有数据 */
const hasRowData = (row: FunaceNoCollectTable) => {
  return partsList.value.some(part => {
    const partDetail = row[part.partClassCode] as PartDetail
    return partDetail.texture || partDetail.furnaceNo || partDetail.hotFurnaceNo ||
           (part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL && partDetail.partSerial)
  })
}

/** 复制行数据 */
const copyRowData = (row: FunaceNoCollectTable, index: number) => {
  const copyData: any = {}
  let hasData = false

  partsList.value.forEach(part => {
    const partDetail = row[part.partClassCode] as PartDetail

    // 只复制有数据的字段
    const partData: any = {}
    if (partDetail.texture) {
      partData.texture = partDetail.texture
      hasData = true
    }
    if (partDetail.furnaceNo) {
      partData.furnaceNo = partDetail.furnaceNo
      hasData = true
    }
    if (partDetail.hotFurnaceNo) {
      partData.hotFurnaceNo = partDetail.hotFurnaceNo
      hasData = true
    }
    // 对于序列号类型的零件，也复制零件系列号
    if (part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL && partDetail.partSerial) {
      partData.partSerial = partDetail.partSerial
      hasData = true
    }

    copyData[part.partClassCode] = partData
  })

  if (!hasData) {
    message.warning('当前行没有可复制的数据')
    return
  }

  copiedRowData.value = copyData
  message.success(`已复制第 ${index + 1} 行数据（材质、炉号、热处理炉号等）`)
}

/** 粘贴行数据 */
const pasteRowData = (row: FunaceNoCollectTable, index: number) => {
  if (!copiedRowData.value) {
    message.warning('没有可粘贴的数据，请先复制一行数据')
    return
  }

  let pastedCount = 0

  partsList.value.forEach(part => {
    const partDetail = row[part.partClassCode] as PartDetail
    const copiedPartDetail = copiedRowData.value[part.partClassCode]

    if (copiedPartDetail && partDetail) {
      // 对于批次类型的零件，复制材质、炉号和热处理炉号
      if (part.batchFlag !== ValveTypePartsBatchFlagEnum.SERIAL) {
        if (copiedPartDetail.texture !== undefined) {
          partDetail.texture = copiedPartDetail.texture
          pastedCount++
        }
        if (copiedPartDetail.furnaceNo !== undefined) {
          partDetail.furnaceNo = copiedPartDetail.furnaceNo
          pastedCount++
        }
        if (copiedPartDetail.hotFurnaceNo !== undefined) {
          partDetail.hotFurnaceNo = copiedPartDetail.hotFurnaceNo
          pastedCount++
        }
      }
    }
  })

  if (pastedCount > 0) {
    // 强制触发响应式更新 - 通过重新赋值整个数组
    tableList.value = [...tableList.value]
    tableKey.value++

    nextTick(() => {
      message.success(`已粘贴数据到第 ${index + 1} 行，共更新 ${pastedCount} 个字段`)
    })
  } else {
    message.warning('没有可粘贴的数据字段')
  }
}

/** 粘贴到所有行 */
const pasteToAllRows = async () => {
  if (!copiedRowData.value) {
    message.warning('没有可粘贴的数据，请先复制一行数据')
    return
  }

  if (tableList.value.length === 0) {
    message.warning('没有可操作的数据行')
    return
  }

  try {
    await message.confirm('确认要将复制的数据粘贴到所有行吗？这将覆盖现有的材质、炉号等数据。')

    let totalPastedCount = 0
    let updatedRowsCount = 0

    tableList.value.forEach((row, index) => {
      let rowPastedCount = 0

      partsList.value.forEach(part => {
        const partDetail = row[part.partClassCode] as PartDetail
        const copiedPartDetail = copiedRowData.value[part.partClassCode]

        if (copiedPartDetail && partDetail) {
          // 对于批次类型的零件，复制材质、炉号和热处理炉号
          if (part.batchFlag !== ValveTypePartsBatchFlagEnum.SERIAL) {
            if (copiedPartDetail.texture !== undefined) {
              partDetail.texture = copiedPartDetail.texture
              rowPastedCount++
            }
            if (copiedPartDetail.furnaceNo !== undefined) {
              partDetail.furnaceNo = copiedPartDetail.furnaceNo
              rowPastedCount++
            }
            if (copiedPartDetail.hotFurnaceNo !== undefined) {
              partDetail.hotFurnaceNo = copiedPartDetail.hotFurnaceNo
              rowPastedCount++
            }
          }
        }
      })

      if (rowPastedCount > 0) {
        updatedRowsCount++
        totalPastedCount += rowPastedCount
      }
    })

    // 强制触发响应式更新
    tableList.value = [...tableList.value]
    tableKey.value++

    nextTick(() => {
      if (totalPastedCount > 0) {
        message.success(`已成功粘贴到 ${updatedRowsCount} 行，共更新 ${totalPastedCount} 个字段`)
      } else {
        message.warning('没有可粘贴的数据字段')
      }
    })
  } catch (error) {
    // 用户取消操作
  }
}

onMounted(() => {})
</script>

<style scoped>
.app-container {
  padding: 3px;
  min-height: calc(100vh - 60px);
  background-color: #f5f5f5;
}

.search-form {
  background: #fff;
  padding: 24px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.search-form :deep(.el-form) {
  margin: 0 !important;
  margin-bottom: 0 !important;
}

.search-form .el-form-item {
  margin-bottom: 12px;
  margin-right: 0;
}

.search-form .el-row {
  margin: 0;
  align-items: center;
}

.search-form .el-col {
  padding: 0 12px;
  display: flex;
  align-items: center;
}

.search-form :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  padding-right: 12px;
}

.required-label :deep(.el-form-item__label) {
  color: #f56c6c;
  font-weight: 600;
}

.required-label :deep(.el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.mr-5px {
  margin-right: 5px;
}

/* ContentWrap 样式优化 */
:deep(.content-wrap) {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  padding: 24px;
  margin-bottom: 20px;
}

/* 第一个ContentWrap去除上边距 */
:deep(.content-wrap:first-child) {
  margin-top: 0;
}

/* 最后一个ContentWrap去除下边距 */
:deep(.content-wrap:last-child) {
  margin-bottom: 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #ebeef5;
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
  font-size: 13px;
  padding: 12px 12px;
  border-bottom: 1px solid #ebeef5;
  height: 48px;
}

:deep(.el-table td) {
  padding: 8px 12px;
  font-size: 13px;
  height: 52px;
}

:deep(.el-table .el-table__row) {
  height: 52px;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table .cell) {
  padding: 0 8px;
  line-height: 1.4;
}

/* 输入框样式优化 */
:deep(.el-input__inner) {
  font-size: 13px;
  padding: 8px 12px;
}

:deep(.el-input--small) {
  height: 36px;
}

:deep(.el-input--small .el-input__inner) {
  height: 36px;
  line-height: 36px;
}

:deep(.el-table .el-input) {
  font-size: 13px;
}

:deep(.el-table .el-input__inner) {
  padding: 6px 10px;
  height: 32px;
  line-height: 32px;
  font-size: 13px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  font-size: 13px;
  padding: 8px 16px;
  height: 36px;
}

:deep(.el-button--small) {
  font-size: 13px;
  padding: 8px 16px;
  height: 36px;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 24px;
  text-align: center;
  padding: 12px 0;
}

:deep(.el-pagination .el-pager li) {
  font-size: 13px;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
}

:deep(.el-pagination .btn-prev, .el-pagination .btn-next) {
  font-size: 13px;
  height: 32px;
  line-height: 32px;
}

:deep(.el-pagination .el-pagination__total) {
  font-size: 13px;
}

:deep(.el-pagination .el-pagination__sizes .el-select .el-input) {
  width: 90px;
}

:deep(.el-pagination .el-pagination__jump) {
  font-size: 12px;
}

/* Tooltip 样式 */
:deep(.el-tooltip__trigger) {
  width: 100%;
}

/* 紧凑布局 */
:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid #ebeef5;
}

/* 去除多余空白 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
  margin-bottom: 12px;
}

:deep(.el-form-item__label) {
  font-size: 13px;
  padding-right: 8px;
}

/* Tooltip 内容样式 */
:deep(.el-tooltip__popper) {
  font-size: 13px;
  max-width: 300px;
}

/* 操作列按钮样式 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.operation-buttons .el-button {
  padding: 2px 6px;
  font-size: 11px;
  height: 22px;
  min-width: 36px;
  border-radius: 3px;
}

.operation-buttons .el-button:disabled {
  opacity: 0.4;
}

.operation-buttons .el-button .el-icon {
  font-size: 10px;
}

/* 操作列固定列样式 */
:deep(.el-table__fixed-left) {
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form .el-col:nth-child(1) { flex: 0 0 25%; max-width: 25%; }
  .search-form .el-col:nth-child(2) { flex: 0 0 20%; max-width: 20%; }
  .search-form .el-col:nth-child(3) { flex: 0 0 20%; max-width: 20%; }
  .search-form .el-col:nth-child(4) { flex: 0 0 35%; max-width: 35%; }
}

@media (max-width: 768px) {
  .app-container {
    padding: 16px 12px 16px 12px;
  }

  .search-form {
    padding: 16px;
    margin-bottom: 16px;
  }

  .search-form .el-col {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 12px;
    padding: 0 8px;
  }

  .search-form .el-col:last-child {
    margin-bottom: 0;
  }

  :deep(.content-wrap) {
    padding: 16px;
    margin-bottom: 16px;
  }

  :deep(.el-table th), :deep(.el-table td) {
    padding: 6px 4px;
    font-size: 12px;
  }
}
</style>

<style scoped>
/* 全局样式重置，去除可能的外边距 */
.app-main {
  padding: 0 !important;
  margin: 0 !important;
}

.main-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* 重置可能的父级容器样式 */
.el-main {
  padding: 0 !important;
}

.content-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* 重置 Element Plus 表单组件的默认外边距 */
.el-form {
  margin: 0 !important;
  margin-bottom: 0 !important;
}

.el-form-item {
  margin-bottom: 0 !important;
}

.el-form-item:last-child {
  margin-bottom: 0 !important;
}
</style>
