<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <ContentWrap>
      <el-form :inline="true" :model="orderInfo" class="search-form" label-width="auto">
        <el-row :gutter="16">
          <el-col :span="4">
            <el-form-item label="生产工单号" class="required-label">
              <el-input
                v-model="orderInfo.moNo"
                placeholder="请输入生产工单号"
                size="small"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售订单号">
              <el-input
                v-model="orderInfo.orderNo"
                placeholder="销售订单号"
                size="small"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="销售订单行号">
              <el-input
                v-model="orderInfo.orderLineNo"
                placeholder="销售订单行号"
                size="small"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" size="small" @click="getList">
                <Icon icon="ep:search" class="mr-5px" />
                {{ t('common.query') }}
              </el-button>
              <el-button
                :disabled="tableList.length <= 0"
                type="success"
                size="small"
                @click="submit"
              >
                <Icon icon="ep:position" class="mr-5px" />
                {{ t('common.submit') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ContentWrap>
    <!-- 数据表格 -->
    <ContentWrap v-show="tableList.length > 0">
      <el-table :data="tableList" border stripe style="width: 100%">
        <el-table-column align="center" label="整机系列号" prop="serial" width="150" />
        <el-table-column
          v-for="part in partsList"
          :key="part.partClassCode"
          :label="part.partClassDesc"
          align="center"
        >
          <el-table-column
            v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL"
            align="center"
            label="零件系列号"
          >
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).partSerial">
                <el-input
                  v-model="(row[part.partClassCode] as PartDetail).partSerial"
                  placeholder="请输入零件系列号"
                  @blur="checkPartSerial(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" label="材质">
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).texture">
                <span v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL">{{
                  (row[part.partClassCode] as PartDetail).texture
                }}</span>
                <el-input
                  v-else
                  v-model="(row[part.partClassCode] as PartDetail).texture"
                  placeholder="请输入材质"
                  @blur="checkTextureFurnaceNo(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" label="炉号">
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).furnaceNo">
                <span v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL">{{
                  (row[part.partClassCode] as PartDetail).furnaceNo
                }}</span>
                <el-input
                  v-else
                  v-model="(row[part.partClassCode] as PartDetail).furnaceNo"
                  placeholder="请输入炉号"
                  @blur="checkTextureFurnaceNo(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column align="center" label="热处理炉号" v-if="part.isHotFurnace">
            <template #default="{ row }">
              <el-tooltip :content="(row[part.partClassCode] as PartDetail).hotFurnaceNo" >
                <span v-if="part.batchFlag === ValveTypePartsBatchFlagEnum.SERIAL" >{{
                    (row[part.partClassCode] as PartDetail).hotFurnaceNo
                  }}</span>
                <el-input
                  v-else
                  v-model="(row[part.partClassCode] as PartDetail).hotFurnaceNo"
                  placeholder="请输入热处理炉号"
                  @blur="checkTextureFurnaceNo(row, part.partClassCode)"
                />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>
  </div>
</template>
<script lang="ts" setup>
import { FurnaceNoCollectApi, FurnaceNoCListApi, FurnaceNoCollectVO } from '@/api/qr/furnaceno'
import { SalesOrderApi } from '@/api/qr/reports/salesOrder'
import { MoInfoApi } from '@/api/qr/reports/mo'
import { ValveTypePartsBatchFlagEnum } from '@/utils/constants'


// 重新定义类型结构
interface PartDetail {
    id?: number
    moNo: string
    partSerial: string
    texture: string
    furnaceNo: string
    hotFurnaceNo: string
    batchFlag?: number
  isHotFurnace?: string
  isRt?: string
  partClassCode?: string
  }

interface FunaceNoCollectTable {
  serial: string
  [key: string]: PartDetail | string
}

const message = useMessage()
const { t } = useI18n()

const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

/** 订单行号 */
const orderInfo = reactive<{
  orderNo?: string
  orderLineNo?: string
  moNo?: string
}>({})

// 订单行的历史采集情况
const furnaceNoCollectRecord = ref<FurnaceNoCollectVO[]>([])

// 通过工单号获取销售订单信息
const getOrderInfoByMoNo = async () => {
  if (!orderInfo.moNo) {
    ElMessage.error('生产工单号不能为空')
    return false
  }
  
  try {
    // 使用MoInfoApi获取销售订单信息
    const result = await MoInfoApi.getOrderInfoByMoNo(orderInfo.moNo)
    if (result && result.soNo && result.soLineNo) {
      orderInfo.orderNo = result.soNo
      orderInfo.orderLineNo = result.soLineNo
      return true
    } else {
      ElMessage.error('未找到该生产工单号对应的销售订单信息')
      return false
    }
  } catch (error) {
    ElMessage.error('获取销售订单信息失败')
    return false
  }
}

const getFurnaceNoCollectInfo = async () => {
  if (!orderInfo.orderNo || !orderInfo.orderLineNo) {
    return
  }

      furnaceNoCollectRecord.value = await FurnaceNoCListApi.getFurnaceNoCollectInfo({
        orderNo: orderInfo.orderNo,
        orderLineNo: orderInfo.orderLineNo
      })
}

/** 销售订单行的物料主要部件列表  */
const partsList = ref<
  Array<{ partClassCode: string;
  partClassDesc: string;
  index: number;
  batchFlag?: number
  isHotFurnace?: boolean
}>>([])

const getPartsList = async () => {
  if (!orderInfo.orderNo || !orderInfo.orderLineNo) {
    return false
  }

  partsList.value = (await SalesOrderApi.getSoPartsList({
    orderNo: orderInfo.orderNo,
    orderLineNo: orderInfo.orderLineNo
  } as any)).filter(
      (item) => item.batchFlag > ValveTypePartsBatchFlagEnum.NONE
    )

  return true
}

const tableList = ref<FunaceNoCollectTable[]>([])

/** 初始化炉号采集表格 */
const getList = async () => {
  // 首先通过工单号获取销售订单信息
  const hasOrderInfo = await getOrderInfoByMoNo()
  if (!hasOrderInfo) return

  // 获取部件列表
  const hasPartsList = await getPartsList()
  if (!hasPartsList) return

  // 获取历史采集信息
  await getFurnaceNoCollectInfo()

  // 获取整机序列号
  const pageResult = await FurnaceNoCollectApi.pageSerials({ 
    orderNo: orderInfo.orderNo,
    orderLineNo: orderInfo.orderLineNo,
    moNo: orderInfo.moNo,
    ...queryParams 
  })
  total.value = pageResult.total


  tableList.value = pageResult.list.map((item) => {
    const tableItem: FunaceNoCollectTable = {
      serial: item.serial
    }


    partsList.value.map((part) => {
      const record = furnaceNoCollectRecord.value.find(
        (r) => r.partClassCode === part.partClassCode && r.serial === item.serial
      )

      tableItem[part.partClassCode] = {
        id: record?.id,
        partClassCode: part.partClassCode,
        partSerial: record?.partSerial || '',
        texture: record?.texture || '',
        furnaceNo: record?.furnaceNo || '',
        hotFurnaceNo: record?.hotFurnaceNo || '',
        isHotFurnace: record?.isHotFurnace || '0',
        isRt: record?.isRt || '0',
        batchFlag: part.batchFlag,
        moNo: record?.moNo || ''
      }
    })

    return tableItem
  })
}


/** 校验零件系列号 */
const checkPartSerial = async (row: FunaceNoCollectTable, partClassCode: string) => {
  // 添加类型断言
  const partDetail = row[partClassCode] as PartDetail
  if (!partDetail.partSerial) return
  try {
    const partSerials = tableList.value
      .map((item) => partsList.value.map((part) => (item[part.partClassCode] as PartDetail).partSerial))
      .flatMap((a) => a)
    if (partSerials.filter((item) => item === partDetail.partSerial).length > 1) {
      message.error('零件系列号重复')
      throw new Error()
    }

    const res = await FurnaceNoCollectApi.checkPartSerial({
      partSerial: partDetail.partSerial,
      partClassCode
    })

    if (res.serial && res.serial != row.serial) {
      message.error('零件系列号已被其他整机占用')
      throw new Error()
    }
    partDetail.texture = res.texture
    partDetail.furnaceNo = res.furnaceNo
    partDetail.hotFurnaceNo= res.hotFurnaceNo
    partDetail.isRt = res.isRt || '0'
  } catch (e) {
    partDetail.partSerial = ''
    partDetail.texture = ''
    partDetail.furnaceNo = ''
    partDetail.hotFurnaceNo= ''
    partDetail.isRt = '0'
  }
}

/** 校验材质和炉号 */
const checkTextureFurnaceNo = async (row: FunaceNoCollectTable, partClassCode: string) => {
  // 添加类型断言
  const partDetail = row[partClassCode] as PartDetail
  if (!partDetail.texture || !partDetail.furnaceNo) return
  try {
    await FurnaceNoCollectApi.checkTextureAndFurnaceNo({
      texture: partDetail.texture,
      furnaceNo: partDetail.furnaceNo,
      hotFurnaceNo: partDetail.hotFurnaceNo,
      partClassCode
    })
  } catch (e) {
    partDetail.texture = ''
    partDetail.furnaceNo = ''
    partDetail.hotFurnaceNo = ''
  }
}

/** 提交数据 */
const submit = async () => {
  // 校验
  const saveList = tableList.value.filter((tableItem) => {
    let hasEmpty = false // 当前行是否有空项
    let allEmpty = true // 当前行是否全是空项

    partsList.value.forEach((part) => {
      // 添加类型断言
      const partDetail = tableItem[part.partClassCode] as PartDetail
      if (partDetail.texture || partDetail.furnaceNo) {
        allEmpty = false
      }
      if (!partDetail.texture || !partDetail.furnaceNo) {
        hasEmpty = true
      }
      if (!orderInfo.moNo || orderInfo.moNo.includes(' ')) {
        message.error('生产工单号不能包含空格或为空')
        throw new Error()
      } else {
        partDetail.moNo = orderInfo.moNo
      }
    })

    if (allEmpty) return true
    if (hasEmpty) return false
    return true
  })
  
  if (saveList.length === 0) {
    message.error('没有可提交的数据')
    return
  }
  
  // 转换数据格式
  const list = saveList
    .map((tableItem) =>
      partsList.value.map((part) => {
        const partDetail = tableItem[part.partClassCode] as PartDetail
        return {
          id: partDetail.id,
          moNo: partDetail.moNo,
          partSerial: partDetail.partSerial,
          texture: partDetail.texture,
          furnaceNo: partDetail.furnaceNo,
          hotFurnaceNo: partDetail.hotFurnaceNo,
          batchFlag: partDetail.batchFlag,
        partClassCode: part.partClassCode,
          serial: tableItem.serial,
          isRt: partDetail.isRt || '0',
          isHotFurnace: part.isHotFurnace ? '1' : '0'
        }
      })
    )
    .flatMap((a) => a)
    
  await message.confirm('确认提交吗？注意：存在空项的数据将不会被提交')

  // 保存
  await FurnaceNoCollectApi.saveFurnaceNoCollect(list)
  message.success(t('common.submit') + t('common.success'))
}

onMounted(() => {})
</script>

<style scoped>
.app-container {
  padding: 16px 12px 16px 12px;
  min-height: calc(100vh - 60px);
  background-color: #f5f5f5;
}

.search-form {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.search-form :deep(.el-form) {
  margin: 0 !important;
  margin-bottom: 0 !important;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}

.search-form .el-row {
  margin: 0;
  align-items: center;
}

.search-form .el-col {
  padding: 0 8px;
  display: flex;
  align-items: center;
}

.search-form :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  padding-right: 8px;
}

.required-label :deep(.el-form-item__label) {
  color: #f56c6c;
  font-weight: 600;
}

.required-label :deep(.el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.mr-5px {
  margin-right: 5px;
}

/* ContentWrap 样式优化 */
:deep(.content-wrap) {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  padding: 16px;
  margin-bottom: 16px;
}

/* 第一个ContentWrap去除上边距 */
:deep(.content-wrap:first-child) {
  margin-top: 0;
}

/* 最后一个ContentWrap去除下边距 */
:deep(.content-wrap:last-child) {
  margin-bottom: 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ebeef5;
  font-size: 12px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
  font-size: 12px;
  padding: 6px 8px;
  border-bottom: 1px solid #ebeef5;
  height: 36px;
}

:deep(.el-table td) {
  padding: 4px 8px;
  font-size: 12px;
  height: 40px;
}

:deep(.el-table .el-table__row) {
  height: 40px;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table .cell) {
  padding: 0 4px;
  line-height: 1.2;
}

/* 输入框样式优化 */
:deep(.el-input__inner) {
  font-size: 12px;
  padding: 4px 8px;
}

:deep(.el-input--small) {
  height: 28px;
}

:deep(.el-input--small .el-input__inner) {
  height: 28px;
  line-height: 28px;
}

:deep(.el-table .el-input) {
  font-size: 12px;
}

:deep(.el-table .el-input__inner) {
  padding: 2px 6px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  font-size: 12px;
  padding: 6px 12px;
  height: 28px;
}

:deep(.el-button--small) {
  font-size: 12px;
  padding: 6px 12px;
  height: 28px;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 16px;
  text-align: center;
  padding: 8px 0;
}

:deep(.el-pagination .el-pager li) {
  font-size: 12px;
  min-width: 26px;
  height: 26px;
  line-height: 26px;
}

:deep(.el-pagination .btn-prev, .el-pagination .btn-next) {
  font-size: 12px;
  height: 26px;
  line-height: 26px;
}

:deep(.el-pagination .el-pagination__total) {
  font-size: 12px;
}

:deep(.el-pagination .el-pagination__sizes .el-select .el-input) {
  width: 90px;
}

:deep(.el-pagination .el-pagination__jump) {
  font-size: 12px;
}

/* Tooltip 样式 */
:deep(.el-tooltip__trigger) {
  width: 100%;
}

/* 紧凑布局 */
:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid #ebeef5;
}

/* 去除多余空白 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
  margin-bottom: 8px;
}

:deep(.el-form-item__label) {
  font-size: 12px;
  padding-right: 6px;
}

/* Tooltip 内容样式 */
:deep(.el-tooltip__popper) {
  font-size: 12px;
  max-width: 300px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form .el-col:nth-child(1) { flex: 0 0 25%; max-width: 25%; }
  .search-form .el-col:nth-child(2) { flex: 0 0 20%; max-width: 20%; }
  .search-form .el-col:nth-child(3) { flex: 0 0 20%; max-width: 20%; }
  .search-form .el-col:nth-child(4) { flex: 0 0 35%; max-width: 35%; }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px 8px 12px 8px;
  }

  .search-form {
    padding: 12px;
    margin-bottom: 12px;
  }

  .search-form .el-col {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 8px;
    padding: 0 4px;
  }

  .search-form .el-col:last-child {
    margin-bottom: 0;
  }

  :deep(.content-wrap) {
    padding: 12px;
    margin-bottom: 12px;
  }

  :deep(.el-table th), :deep(.el-table td) {
    padding: 4px 2px;
    font-size: 11px;
  }
}
</style>

<style scoped>
/* 全局样式重置，去除可能的外边距 */
.app-main {
  padding: 0 !important;
  margin: 0 !important;
}

.main-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* 重置可能的父级容器样式 */
.el-main {
  padding: 0 !important;
}

.content-container {
  padding: 0 !important;
  margin: 0 !important;
}

/* 重置 Element Plus 表单组件的默认外边距 */
.el-form {
  margin: 0 !important;
  margin-bottom: 0 !important;
}

.el-form-item {
  margin-bottom: 0 !important;
}

.el-form-item:last-child {
  margin-bottom: 0 !important;
}
</style>
