package com.neway.assets.module.info.api.zcfl;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.zcfl.dto.ZcflRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 资产分类API接口
 * <AUTHOR>
 * @since 2023/5/8 8:18
 **/
public interface ZcflApi {

    List<ZcflRespDTO> getZcflList(Collection<Long> ids);

    default Map<Long, ZcflRespDTO> getZcflMap(Set<Long> ids) {
        List<ZcflRespDTO> zcflList = getZcflList(ids);
        return CollectionUtils.convertMap(zcflList, ZcflRespDTO::getId);
    }
}
