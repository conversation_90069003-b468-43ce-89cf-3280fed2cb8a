package com.neway.assets.module.info.api.zcflZgy;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.zcflZgy.dto.ZcflZgyRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/10/16 14:03
 **/
public interface ZcflZgyApi {

    List<ZcflZgyRespDTO> getZcflZgyList(Collection<Long> ids);

    default Map<Long, ZcflZgyRespDTO> getZcflZgyMap(Set<Long> ids) {
        return CollectionUtils.convertMap(getZcflZgyList(ids), ZcflZgyRespDTO::getId);
    }

    List<ZcflZgyRespDTO> getZcflZgyListByUserId(Long userId);
}
