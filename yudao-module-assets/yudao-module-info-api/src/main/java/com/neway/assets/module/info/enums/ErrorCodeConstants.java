package com.neway.assets.module.info.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * info资产基础信息模块 错误码枚举类
 *
 * 使用 1-100-000-000 段
 * <AUTHOR>
 * @since 2023/4/18 11:39
 **/
public interface ErrorCodeConstants {

    // ========== 资产性质 模块 1-100-001-000 ==========
    ErrorCode ZCXZ_NOT_EXISTS = new ErrorCode( 1100001000, "资产性质不存在");

    // ========== 资产分类方式 1-100-002-000 ==========
    ErrorCode ZCFLFS_NOT_EXISTS = new ErrorCode(1100002000, "资产分类方式不存在");

    // ========== 资产分类 1-*********** ==========
    ErrorCode ZCFL_NOT_EXISTS = new ErrorCode(1100003000, "资产分类不存在");

    // ========== 工厂信息 1-*********** ==========
    ErrorCode GCXX_NOT_EXISTS = new ErrorCode(1100004000, "工厂信息不存在");

    // ========== 库存地点 1-*********** ==========
    ErrorCode KCDD_NOT_EXISTS = new ErrorCode(1100005000, "库存地点不存在");
    ErrorCode KCDD_NOT_MATCH_FACTORY_COMPANY = new ErrorCode(1100005001, "库存地点的工厂与公司信息不对应");

    // ========== 库位信息 1-*********** ==========
    ErrorCode KWXX_NOT_EXISTS = new ErrorCode(1100006000, "库位信息不存在");

    // ========== 货架 1-*********** ==========
    ErrorCode HJXX_NOT_EXISTS = new ErrorCode(1100007000, "货架不存在");

    // ========== 计量单位 1-*********** ==========
    ErrorCode JLDW_NOT_EXISTS = new ErrorCode(1100008000, "计量单位不存在");

    // ========== 计量单位转换对照 1-*********** ==========
    ErrorCode JLDWDZ_NOT_EXISTS = new ErrorCode(1100009000, "计量单位转换对照不存在");
    ErrorCode JLDWDZ_NO_MATCHED = new ErrorCode(1100009001, "相匹配的计量单位转换关系不存在");
    ErrorCode JLDWDZ_NOT_ROUNDED = new ErrorCode(1100009001, "转换关系无法整除，不能实现转换");

    ErrorCode WXYY_NOT_EXISTS = new ErrorCode(1100010000, "维护/维修原因不存在");

    // ========== 资产折旧方式 1-100-011-000 ==========
    ErrorCode ZJFS_NOT_EXISTS = new ErrorCode(1100011000, "资产折旧方式不存在");

    // ========== 行业 1-100-012-000 ==========
    ErrorCode HY_NOT_EXISTS = new ErrorCode(1100012000, "行业不存在");

    // ========== 耗材/备品备件分类 1-100-013-000 ==========
    ErrorCode HBJFL_NOT_EXISTS = new ErrorCode(1100013000, "耗材/备品备件分类不存在");

    // ========== 币种信息 1-100-014-000 ==========
    ErrorCode BZXX_NOT_EXISTS = new ErrorCode(1100014000, "币种信息不存在");

    // ========== 资产二级分类 1-100-015-000 ==========
    ErrorCode SECOND_ZCFL_NOT_EXISTS = new ErrorCode(1100015000, "资产二级分类不存在");

    // ========== 资产分类资管员关联 1-100-016-000 ==========
    ErrorCode ZCFL_ZGY_NOT_EXISTS = new ErrorCode(1100016000, "资产分类资管员关联不存在");
    ErrorCode ZCFL_ZGY_DUPLICATE = new ErrorCode(1100016001, "该公司资产分类下已有资管员");
}
