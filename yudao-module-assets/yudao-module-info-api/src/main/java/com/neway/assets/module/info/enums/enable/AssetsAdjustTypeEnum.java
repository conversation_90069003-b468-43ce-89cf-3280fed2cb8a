package com.neway.assets.module.info.enums.enable;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资产异动方式枚举
 * <AUTHOR>
 * @since 2023/5/5 10:48
 */
@Getter
@AllArgsConstructor
public enum AssetsAdjustTypeEnum {
    /** 借用 */
    BORROW(1),
    /** 归还 */
    RETURN(2),
    /** 转移 */
    TRANSFER(3),
    /** 接收 */
    RECEIVE(4),
    /** 调拨 */
    ALLOCATE(5),
    /** 出售 */
    SELL(6),
    /** 损毁 */
    DAMAGE(7),
    /** 作废 */
    SCRAP(8),
    /** 对外投资 */
    INVESTMENT_ABROAD(9),
    /** 捐献 */
    DONATE(10),
    /** 抵债 */
    DEBT_REPAYMENT(11),
    /** 盘亏 */
    INVENTORY_LOSS(12),
    /** 维修 */
    MAINTAIN(13),
    /** 委外维修 */
    MAINTAIN_ABROAD(14),
    /** 盘盈 */
    INVENTORY_PROFIT(15);

    private final Integer val;
}
