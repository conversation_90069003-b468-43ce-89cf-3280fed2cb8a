package com.neway.assets.module.info.enums.enable;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 资产类别
 * <AUTHOR>
 * @since 2023/5/22 13:57
 */
@Getter
@AllArgsConstructor
public enum AssetsClassEnum {
    PART(1), ENTIRE(0);

    private final Integer val;

    public static AssetsClassEnum getByVal(Integer val) {
        for (AssetsClassEnum classEnum : values()) {
            if (Objects.equals(classEnum.getVal(), val)) return classEnum;
        }
        return null;
    }
}
