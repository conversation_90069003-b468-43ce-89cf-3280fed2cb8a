package com.neway.assets.module.info.api.zjfs;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.zjfs.dto.ZjfsRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 折旧方式API接口
 * <AUTHOR>
 * @since 2023/5/8 13:54
 **/
public interface ZjfsApi {

    List<ZjfsRespDTO> getZjfsList(Collection<Long> ids);

    default Map<Long, ZjfsRespDTO> getZjfsMap(Set<Long> ids) {
        List<ZjfsRespDTO> zjfsList = getZjfsList(ids);
        return CollectionUtils.convertMap(zjfsList, ZjfsRespDTO::getId);
    }
}
