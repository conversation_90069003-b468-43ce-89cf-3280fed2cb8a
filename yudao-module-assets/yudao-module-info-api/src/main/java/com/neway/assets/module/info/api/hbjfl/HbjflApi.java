package com.neway.assets.module.info.api.hbjfl;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.hbjfl.dto.HbjflRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/6/6 16:32
 **/
public interface HbjflApi {

    List<HbjflRespDTO> getHbflList(Set<Long> ids);

    default Map<Long, HbjflRespDTO> getHbflMap(Set<Long> ids) {
        List<HbjflRespDTO> hbflList = getHbflList(ids);
        return CollectionUtils.convertMap(hbflList, HbjflRespDTO::getId);
    }
}
