package com.neway.assets.module.info.api.secondzcfl;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.secondzcfl.dto.SecondZcflRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/6/6 9:28
 **/
public interface SecondZcflApi {

    List<SecondZcflRespDTO> getSecondZcflList(Set<Long> ids);

    default Map<Long, SecondZcflRespDTO> getSecondZcflMap(Set<Long> ids) {
        List<SecondZcflRespDTO> secondZcflList = getSecondZcflList(ids);
        return CollectionUtils.convertMap(secondZcflList, SecondZcflRespDTO::getId);
    }
}
