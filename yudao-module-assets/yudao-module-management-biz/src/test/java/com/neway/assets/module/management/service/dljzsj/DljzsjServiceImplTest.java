package com.neway.assets.module.management.service.dljzsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.DljzsjCreateReqVO;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.DljzsjExportReqVO;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.DljzsjPageReqVO;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.DljzsjUpdateReqVO;
import com.neway.assets.module.management.dal.dataobject.dljzsj.DljzsjDO;
import com.neway.assets.module.management.dal.mysql.dljzsj.DljzsjMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.DLJZSJ_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link DljzsjServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(DljzsjServiceImpl.class)
public class DljzsjServiceImplTest extends BaseDbUnitTest {

    @Resource
    private DljzsjServiceImpl dljzsjService;

    @Resource
    private DljzsjMapper dljzsjMapper;

    @Test
    public void testCreateDljzsj_success() {
        // 准备参数
        DljzsjCreateReqVO reqVO = randomPojo(DljzsjCreateReqVO.class);

        // 调用
        Long dljzsjId = dljzsjService.createDljzsj(reqVO);
        // 断言
        assertNotNull(dljzsjId);
        // 校验记录的属性是否正确
        DljzsjDO dljzsj = dljzsjMapper.selectById(dljzsjId);
        assertPojoEquals(reqVO, dljzsj);
    }

    @Test
    public void testUpdateDljzsj_success() {
        // mock 数据
        DljzsjDO dbDljzsj = randomPojo(DljzsjDO.class);
        dljzsjMapper.insert(dbDljzsj);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DljzsjUpdateReqVO reqVO = randomPojo(DljzsjUpdateReqVO.class, o -> {
            o.setId(dbDljzsj.getId()); // 设置更新的 ID
        });

        // 调用
        dljzsjService.updateDljzsj(reqVO);
        // 校验是否更新正确
        DljzsjDO dljzsj = dljzsjMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, dljzsj);
    }

    @Test
    public void testUpdateDljzsj_notExists() {
        // 准备参数
        DljzsjUpdateReqVO reqVO = randomPojo(DljzsjUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> dljzsjService.updateDljzsj(reqVO), DLJZSJ_NOT_EXISTS);
    }

    @Test
    public void testDeleteDljzsj_success() {
        // mock 数据
        DljzsjDO dbDljzsj = randomPojo(DljzsjDO.class);
        dljzsjMapper.insert(dbDljzsj);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDljzsj.getId();

        // 调用
        dljzsjService.deleteDljzsj(id);
       // 校验数据不存在了
       assertNull(dljzsjMapper.selectById(id));
    }

    @Test
    public void testDeleteDljzsj_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> dljzsjService.deleteDljzsj(id), DLJZSJ_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetDljzsjPage() {
       // mock 数据
       DljzsjDO dbDljzsj = randomPojo(DljzsjDO.class, o -> { // 等会查询到
           o.setDljfl(null);
           o.setDljlh(null);
           o.setDljmc(null);
           o.setDljgexh(null);
           o.setJzzq(null);
           o.setSyzt(null);
           o.setGlbm(null);
           o.setGlren(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       dljzsjMapper.insert(dbDljzsj);
       // 测试 dljfl 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljfl(null)));
       // 测试 dljlh 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljlh(null)));
       // 测试 dljmc 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljmc(null)));
       // 测试 dljgexh 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljgexh(null)));
       // 测试 jzzq 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setJzzq(null)));
       // 测试 syzt 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setSyzt(null)));
       // 测试 glbm 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setGlbm(null)));
       // 测试 glren 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setGlren(null)));
       // 测试 bz 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setCreateTime(null)));
       // 准备参数
       DljzsjPageReqVO reqVO = new DljzsjPageReqVO();
       reqVO.setDljfl(null);
       reqVO.setDljlh(null);
       reqVO.setDljmc(null);
       reqVO.setDljgexh(null);
       reqVO.setJzzq(null);
       reqVO.setSyzt(null);
       reqVO.setGlbm(null);
       reqVO.setGlren(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<DljzsjDO> pageResult = dljzsjService.getDljzsjPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbDljzsj, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetDljzsjList() {
       // mock 数据
       DljzsjDO dbDljzsj = randomPojo(DljzsjDO.class, o -> { // 等会查询到
           o.setDljfl(null);
           o.setDljlh(null);
           o.setDljmc(null);
           o.setDljgexh(null);
           o.setJzzq(null);
           o.setSyzt(null);
           o.setGlbm(null);
           o.setGlren(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       dljzsjMapper.insert(dbDljzsj);
       // 测试 dljfl 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljfl(null)));
       // 测试 dljlh 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljlh(null)));
       // 测试 dljmc 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljmc(null)));
       // 测试 dljgexh 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setDljgexh(null)));
       // 测试 jzzq 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setJzzq(null)));
       // 测试 syzt 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setSyzt(null)));
       // 测试 glbm 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setGlbm(null)));
       // 测试 glren 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setGlren(null)));
       // 测试 bz 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       dljzsjMapper.insert(cloneIgnoreId(dbDljzsj, o -> o.setCreateTime(null)));
       // 准备参数
       DljzsjExportReqVO reqVO = new DljzsjExportReqVO();
       reqVO.setDljfl(null);
       reqVO.setDljlh(null);
       reqVO.setDljmc(null);
       reqVO.setDljgexh(null);
       reqVO.setJzzq(null);
       reqVO.setSyzt(null);
       reqVO.setGlbm(null);
       reqVO.setGlren(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<DljzsjDO> list = dljzsjService.getDljzsjList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbDljzsj, list.get(0));
    }

}
