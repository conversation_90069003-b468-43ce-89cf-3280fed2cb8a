package com.neway.assets.module.management.service.zzcxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxCreateReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxExportReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxPageReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxUpdateReqVO;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import com.neway.assets.module.management.dal.mysql.zzcxx.ZzcxxMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZZCXX_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZzcxxServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZzcxxServiceImpl.class)
public class ZzcxxServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZzcxxServiceImpl zzcxxService;

    @Resource
    private ZzcxxMapper zzcxxMapper;

    @Test
    public void testCreateZzcxx_success() {
        // 准备参数
        ZzcxxCreateReqVO reqVO = randomPojo(ZzcxxCreateReqVO.class);

        // 调用
        Long zzcxxId = zzcxxService.createZzcxx(reqVO);
        // 断言
        assertNotNull(zzcxxId);
        // 校验记录的属性是否正确
        ZzcxxDO zzcxx = zzcxxMapper.selectById(zzcxxId);
        assertPojoEquals(reqVO, zzcxx);
    }

    @Test
    public void testUpdateZzcxx_success() {
        // mock 数据
        ZzcxxDO dbZzcxx = randomPojo(ZzcxxDO.class);
        zzcxxMapper.insert(dbZzcxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZzcxxUpdateReqVO reqVO = randomPojo(ZzcxxUpdateReqVO.class, o -> {
            o.setId(dbZzcxx.getId()); // 设置更新的 ID
        });

        // 调用
        zzcxxService.updateZzcxx(reqVO);
        // 校验是否更新正确
        ZzcxxDO zzcxx = zzcxxMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zzcxx);
    }

    @Test
    public void testUpdateZzcxx_notExists() {
        // 准备参数
        ZzcxxUpdateReqVO reqVO = randomPojo(ZzcxxUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zzcxxService.updateZzcxx(reqVO), ZZCXX_NOT_EXISTS);
    }

    @Test
    public void testDeleteZzcxx_success() {
        // mock 数据
        ZzcxxDO dbZzcxx = randomPojo(ZzcxxDO.class);
        zzcxxMapper.insert(dbZzcxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZzcxx.getId();

        // 调用
        zzcxxService.deleteZzcxx(id);
       // 校验数据不存在了
       assertNull(zzcxxMapper.selectById(id));
    }

    @Test
    public void testDeleteZzcxx_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zzcxxService.deleteZzcxx(id), ZZCXX_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZzcxxPage() {
       // mock 数据
       ZzcxxDO dbZzcxx = randomPojo(ZzcxxDO.class, o -> { // 等会查询到
           o.setZczsjid(null);
           o.setZzcbh(null);
           o.setZzcmc(null);
           o.setZzcge(null);
           o.setZzczbq(null);
           o.setZzcsl(null);
           o.setZzczzs(null);
           o.setZzczt(null);
           o.setZzczp(null);
           o.setCreateTime(null);
       });
       zzcxxMapper.insert(dbZzcxx);
       // 测试 zczsjid 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZczsjid(null)));
       // 测试 zzcbh 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcbh(null)));
       // 测试 zzcmc 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcmc(null)));
       // 测试 zzcge 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcge(null)));
       // 测试 zzczbq 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczbq(null)));
       // 测试 zzcsl 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcsl(null)));
       // 测试 zzczzs 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczzs(null)));
       // 测试 zzczt 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczt(null)));
       // 测试 zzczp 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczp(null)));
       // 测试 createTime 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setCreateTime(null)));
       // 准备参数
       ZzcxxPageReqVO reqVO = new ZzcxxPageReqVO();
       reqVO.setZczsjid(null);
       reqVO.setZzcbh(null);
       reqVO.setZzcmc(null);
       reqVO.setZzcge(null);
       reqVO.setZzczbq(null);
       reqVO.setZzcsl(null);
       reqVO.setZzczzs(null);
       reqVO.setZzczt(null);
       reqVO.setZzczp(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZzcxxDO> pageResult = zzcxxService.getZzcxxPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZzcxx, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZzcxxList() {
       // mock 数据
       ZzcxxDO dbZzcxx = randomPojo(ZzcxxDO.class, o -> { // 等会查询到
           o.setZczsjid(null);
           o.setZzcbh(null);
           o.setZzcmc(null);
           o.setZzcge(null);
           o.setZzczbq(null);
           o.setZzcsl(null);
           o.setZzczzs(null);
           o.setZzczt(null);
           o.setZzczp(null);
           o.setCreateTime(null);
       });
       zzcxxMapper.insert(dbZzcxx);
       // 测试 zczsjid 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZczsjid(null)));
       // 测试 zzcbh 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcbh(null)));
       // 测试 zzcmc 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcmc(null)));
       // 测试 zzcge 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcge(null)));
       // 测试 zzczbq 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczbq(null)));
       // 测试 zzcsl 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzcsl(null)));
       // 测试 zzczzs 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczzs(null)));
       // 测试 zzczt 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczt(null)));
       // 测试 zzczp 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setZzczp(null)));
       // 测试 createTime 不匹配
       zzcxxMapper.insert(cloneIgnoreId(dbZzcxx, o -> o.setCreateTime(null)));
       // 准备参数
       ZzcxxExportReqVO reqVO = new ZzcxxExportReqVO();
       reqVO.setZczsjid(null);
       reqVO.setZzcbh(null);
       reqVO.setZzcmc(null);
       reqVO.setZzcge(null);
       reqVO.setZzczbq(null);
       reqVO.setZzcsl(null);
       reqVO.setZzczzs(null);
       reqVO.setZzczt(null);
       reqVO.setZzczp(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZzcxxDO> list = zzcxxService.getZzcxxList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZzcxx, list.get(0));
    }

}
