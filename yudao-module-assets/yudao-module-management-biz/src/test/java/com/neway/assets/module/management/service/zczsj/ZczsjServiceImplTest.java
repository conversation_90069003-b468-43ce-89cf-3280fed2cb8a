package com.neway.assets.module.management.service.zczsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.management.controller.admin.zczsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import com.neway.assets.module.management.dal.mysql.zczsj.ZczsjMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenDate;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCZSJ_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZczsjServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZczsjServiceImpl.class)
public class ZczsjServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZczsjServiceImpl zczsjService;

    @Resource
    private ZczsjMapper zczsjMapper;

    @Test
    public void testCreateZczsj_success() {
        // 准备参数
        ZczsjCreateReqVO reqVO = randomPojo(ZczsjCreateReqVO.class);

        // 调用
        Long zczsjId = zczsjService.createZczsj(reqVO);
        // 断言
        assertNotNull(zczsjId);
        // 校验记录的属性是否正确
        ZczsjDO zczsj = zczsjMapper.selectById(zczsjId);
        assertPojoEquals(reqVO, zczsj);
    }

    @Test
    public void testUpdateZczsj_success() {
        // mock 数据
        ZczsjDO dbZczsj = randomPojo(ZczsjDO.class);
        zczsjMapper.insert(dbZczsj);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZczsjUpdateReqVO reqVO = randomPojo(ZczsjUpdateReqVO.class, o -> {
            o.setId(dbZczsj.getId()); // 设置更新的 ID
        });

        // 调用
        zczsjService.updateZczsj(reqVO);
        // 校验是否更新正确
        ZczsjDO zczsj = zczsjMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zczsj);
    }

    @Test
    public void testUpdateZczsj_notExists() {
        // 准备参数
        ZczsjUpdateReqVO reqVO = randomPojo(ZczsjUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zczsjService.updateZczsj(reqVO), ZCZSJ_NOT_EXISTS);
    }

    @Test
    public void testDeleteZczsj_success() {
        // mock 数据
        ZczsjDO dbZczsj = randomPojo(ZczsjDO.class);
        zczsjMapper.insert(dbZczsj);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZczsj.getId();

        // 调用
        zczsjService.deleteZczsj(id);
       // 校验数据不存在了
       assertNull(zczsjMapper.selectById(id));
    }

    @Test
    public void testDeleteZczsj_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zczsjService.deleteZczsj(id), ZCZSJ_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZczsjPage() {
       // mock 数据
       ZczsjDO dbZczsj = randomPojo(ZczsjDO.class, o -> { // 等会查询到
           o.setZcfl(null);
           o.setZcbh(null);
           o.setCwbh(null);
           o.setZcmc(null);
           o.setGgxh(null);
           o.setZcbm(null);
           o.setZcdd(null);
           o.setGys(null);
           o.setWhs(null);
           o.setHtbh(null);
           o.setGrrq(null);
           o.setZcsl(null);
           o.setJldw(null);
           o.setZcdjrq(null);
           o.setHsgrje(null);
           o.setBhsgrje(null);
           o.setZcbz(null);
           o.setZbq(null);
           o.setCzl(null);
           o.setCzs(null);
           o.setDqsycz(null);
           o.setSynxian(null);
           o.setBfrq(null);
           o.setZczt(null);
           o.setZczp(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zczsjMapper.insert(dbZczsj);
       // 测试 zcfl 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcfl(null)));
       // 测试 zcbh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcbh(null)));
       // 测试 cwbh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCwbh(null)));
       // 测试 zcmc 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcmc(null)));
       // 测试 ggxh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setGgxh(null)));
       // 测试 zcbm 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcbm(null)));
       // 测试 zcdd 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcdd(null)));
       // 测试 gys 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setGys(null)));
       // 测试 whs 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setWhs(null)));
       // 测试 htbh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setHtbh(null)));
       // 测试 grrq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setGrrq(null)));
       // 测试 zcsl 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcsl(null)));
       // 测试 jldw 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setJldw(null)));
       // 测试 zcdjrq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcdjrq(null)));
       // 测试 hsgrje 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setHsgrje(null)));
       // 测试 bhsgrje 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setBhsgrje(null)));
       // 测试 zcbz 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcbz(null)));
       // 测试 zbq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZbq(null)));
       // 测试 czl 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCzl(null)));
       // 测试 czs 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCzs(null)));
       // 测试 dqsycz 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setDqsycz(null)));
       // 测试 synxian 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setSynxian(null)));
       // 测试 bfrq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setBfrq(null)));
       // 测试 zczt 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZczt(null)));
       // 测试 zczp 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZczp(null)));
       // 测试 bz 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCreateTime(null)));
       // 准备参数
       ZczsjPageReqVO reqVO = new ZczsjPageReqVO();
       reqVO.setZcfl(null);
       reqVO.setZcbh(null);
       reqVO.setCwbh(null);
       reqVO.setZcmc(null);
       reqVO.setGgxh(null);
       reqVO.setZcbm(null);
       reqVO.setZcdd(null);
       reqVO.setZgy(null);
       reqVO.setGys(null);
       reqVO.setWhs(null);
       reqVO.setHtbh(null);
       reqVO.setGrrq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setZcsl(null);
       reqVO.setJldw(null);
       reqVO.setZcdjrq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setHsgrje(null);
       reqVO.setBhsgrje(null);
       reqVO.setZcbz(null);
       reqVO.setZbq(null);
       reqVO.setCzl(null);
       reqVO.setCzs(null);
       reqVO.setDqsycz(null);
       reqVO.setSynxian(null);
       reqVO.setBfrq(null);
       reqVO.setZczt(null);
       reqVO.setZczp(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZczsjPageItemRespVO> pageResult = zczsjService.getZczsjPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZczsj, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZczsjList() {
       // mock 数据
       ZczsjDO dbZczsj = randomPojo(ZczsjDO.class, o -> { // 等会查询到
           o.setZcfl(null);
           o.setZcbh(null);
           o.setCwbh(null);
           o.setZcmc(null);
           o.setGgxh(null);
           o.setZcbm(null);
           o.setZcdd(null);
           o.setGys(null);
           o.setWhs(null);
           o.setHtbh(null);
           o.setGrrq(null);
           o.setZcsl(null);
           o.setJldw(null);
           o.setZcdjrq(null);
           o.setHsgrje(null);
           o.setBhsgrje(null);
           o.setZcbz(null);
           o.setZbq(null);
           o.setCzl(null);
           o.setCzs(null);
           o.setDqsycz(null);
           o.setSynxian(null);
           o.setBfrq(null);
           o.setZczt(null);
           o.setZczp(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zczsjMapper.insert(dbZczsj);
       // 测试 zcfl 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcfl(null)));
       // 测试 zcbh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcbh(null)));
       // 测试 cwbh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCwbh(null)));
       // 测试 zcmc 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcmc(null)));
       // 测试 ggxh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setGgxh(null)));
       // 测试 zcbm 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcbm(null)));
       // 测试 zcdd 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcdd(null)));
       // 测试 gys 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setGys(null)));
       // 测试 whs 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setWhs(null)));
       // 测试 htbh 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setHtbh(null)));
       // 测试 grrq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setGrrq(null)));
       // 测试 zcsl 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcsl(null)));
       // 测试 jldw 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setJldw(null)));
       // 测试 zcdjrq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcdjrq(null)));
       // 测试 hsgrje 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setHsgrje(null)));
       // 测试 bhsgrje 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setBhsgrje(null)));
       // 测试 zcbz 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZcbz(null)));
       // 测试 zbq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZbq(null)));
       // 测试 czl 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCzl(null)));
       // 测试 czs 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCzs(null)));
       // 测试 dqsycz 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setDqsycz(null)));
       // 测试 synxian 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setSynxian(null)));
       // 测试 bfrq 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setBfrq(null)));
       // 测试 zczt 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZczt(null)));
       // 测试 zczp 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setZczp(null)));
       // 测试 bz 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zczsjMapper.insert(cloneIgnoreId(dbZczsj, o -> o.setCreateTime(null)));
       // 准备参数
       ZczsjExportReqVO reqVO = new ZczsjExportReqVO();
       reqVO.setZcfl(null);
       reqVO.setZcbh(null);
       reqVO.setCwbh(null);
       reqVO.setZcmc(null);
       reqVO.setGgxh(null);
       reqVO.setZcbm(null);
       reqVO.setZcdd(null);
       reqVO.setGys(null);
       reqVO.setWhs(null);
       reqVO.setHtbh(null);
       reqVO.setGrrq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setZcsl(null);
       reqVO.setJldw(null);
       reqVO.setZcdjrq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setHsgrje(null);
       reqVO.setBhsgrje(null);
       reqVO.setZcbz(null);
       reqVO.setZbq(null);
       reqVO.setCzl(null);
       reqVO.setCzs(null);
       reqVO.setDqsycz(null);
       reqVO.setSynxian(null);
       reqVO.setBfrq(null);
       reqVO.setZczt(null);
       reqVO.setZczp(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZczsjDO> list = zczsjService.getZczsjList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZczsj, list.get(0));
    }

}
