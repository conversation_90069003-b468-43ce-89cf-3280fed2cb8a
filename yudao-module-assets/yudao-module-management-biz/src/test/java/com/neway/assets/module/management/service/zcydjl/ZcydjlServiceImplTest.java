package com.neway.assets.module.management.service.zcydjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.management.controller.admin.zcydjl.vo.*;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import com.neway.assets.module.management.dal.mysql.zcydjl.ZcydjlMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCYDJL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcydjlServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcydjlServiceImpl.class)
public class ZcydjlServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcydjlServiceImpl zcydjlService;

    @Resource
    private ZcydjlMapper zcydjlMapper;

    @Test
    public void testCreateZcydjl_success() {
        // 准备参数
        ZcydjlCreateReqVO reqVO = randomPojo(ZcydjlCreateReqVO.class);

        // 调用
        Long zcydjlId = zcydjlService.createZcydjl(reqVO);
        // 断言
        assertNotNull(zcydjlId);
        // 校验记录的属性是否正确
        ZcydjlDO zcydjl = zcydjlMapper.selectById(zcydjlId);
        assertPojoEquals(reqVO, zcydjl);
    }

    @Test
    public void testUpdateZcydjl_success() {
        // mock 数据
        ZcydjlDO dbZcydjl = randomPojo(ZcydjlDO.class);
        zcydjlMapper.insert(dbZcydjl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcydjlUpdateReqVO reqVO = randomPojo(ZcydjlUpdateReqVO.class, o -> {
            o.setId(dbZcydjl.getId()); // 设置更新的 ID
        });

        // 调用
        zcydjlService.updateZcydjl(reqVO);
        // 校验是否更新正确
        ZcydjlDO zcydjl = zcydjlMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcydjl);
    }

    @Test
    public void testUpdateZcydjl_notExists() {
        // 准备参数
        ZcydjlUpdateReqVO reqVO = randomPojo(ZcydjlUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcydjlService.updateZcydjl(reqVO), ZCYDJL_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcydjl_success() {
        // mock 数据
        ZcydjlDO dbZcydjl = randomPojo(ZcydjlDO.class);
        zcydjlMapper.insert(dbZcydjl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcydjl.getId();

        // 调用
        zcydjlService.deleteZcydjl(id);
       // 校验数据不存在了
       assertNull(zcydjlMapper.selectById(id));
    }

    @Test
    public void testDeleteZcydjl_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcydjlService.deleteZcydjl(id), ZCYDJL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcydjlPage() {
       // mock 数据
       ZcydjlDO dbZcydjl = randomPojo(ZcydjlDO.class, o -> { // 等会查询到
           o.setZclb(null);
           o.setZcxxid(null);
           o.setYdfs(null);
           o.setYzgbm(null);
           o.setYzgy(null);
           o.setMbzgbm(null);
           o.setMbzgy(null);
           o.setKsrq(null);
           o.setJzrq(null);
           o.setYdzt(null);
           o.setProcessInstanceId(null);
           o.setCreateTime(null);
       });
       zcydjlMapper.insert(dbZcydjl);
       // 测试 zclb 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setZclb(null)));
       // 测试 zcxxid 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setZcxxid(null)));
       // 测试 ydfs 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYdfs(null)));
       // 测试 yzgbm 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYzgbm(null)));
       // 测试 yzgy 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYzgy(null)));
       // 测试 mbzgbm 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setMbzgbm(null)));
       // 测试 mbzgy 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setMbzgy(null)));
       // 测试 ksrq 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setKsrq(null)));
       // 测试 jzrq 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setJzrq(null)));
       // 测试 ydzt 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYdzt(null)));
       // 测试 processInstanceId 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setProcessInstanceId(null)));
       // 测试 createTime 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setCreateTime(null)));
       // 准备参数
       ZcydjlPageReqVO reqVO = new ZcydjlPageReqVO();
       reqVO.setZclb(null);
       reqVO.setZcxxid(null);
       reqVO.setYdfs(null);
       reqVO.setYzgbm(null);
       reqVO.setYzgy(null);
       reqVO.setMbzgbm(null);
       reqVO.setMbzgy(null);
       reqVO.setKsrq(null);
       reqVO.setJzrq(null);
       reqVO.setYdzt(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcydjlPageItemRespVO> pageResult = zcydjlService.getZcydjlPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcydjl, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcydjlList() {
       // mock 数据
       ZcydjlDO dbZcydjl = randomPojo(ZcydjlDO.class, o -> { // 等会查询到
           o.setZclb(null);
           o.setZcxxid(null);
           o.setYdfs(null);
           o.setYzgbm(null);
           o.setYzgy(null);
           o.setMbzgbm(null);
           o.setMbzgy(null);
           o.setKsrq(null);
           o.setJzrq(null);
           o.setYdzt(null);
           o.setProcessInstanceId(null);
           o.setCreateTime(null);
       });
       zcydjlMapper.insert(dbZcydjl);
       // 测试 zclb 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setZclb(null)));
       // 测试 zcxxid 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setZcxxid(null)));
       // 测试 ydfs 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYdfs(null)));
       // 测试 yzgbm 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYzgbm(null)));
       // 测试 yzgy 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYzgy(null)));
       // 测试 mbzgbm 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setMbzgbm(null)));
       // 测试 mbzgy 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setMbzgy(null)));
       // 测试 ksrq 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setKsrq(null)));
       // 测试 jzrq 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setJzrq(null)));
       // 测试 ydzt 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setYdzt(null)));
       // 测试 processInstanceId 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setProcessInstanceId(null)));
       // 测试 createTime 不匹配
       zcydjlMapper.insert(cloneIgnoreId(dbZcydjl, o -> o.setCreateTime(null)));
       // 准备参数
       ZcydjlExportReqVO reqVO = new ZcydjlExportReqVO();
       reqVO.setZclb(null);
       reqVO.setZcxxid(null);
       reqVO.setYdfs(null);
       reqVO.setYzgbm(null);
       reqVO.setYzgy(null);
       reqVO.setMbzgbm(null);
       reqVO.setMbzgy(null);
       reqVO.setKsrq(null);
       reqVO.setJzrq(null);
       reqVO.setYdzt(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcydjlDO> list = zcydjlService.getZcydjlList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcydjl, list.get(0));
    }

}
