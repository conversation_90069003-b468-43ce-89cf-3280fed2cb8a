package com.neway.assets.module.management.service.hbjzsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.management.controller.admin.hbjzsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.hbjzsj.HbjzsjDO;
import com.neway.assets.module.management.dal.mysql.hbjzsj.HbjzsjMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.HBJZSJ_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link HbjzsjServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(HbjzsjServiceImpl.class)
public class HbjzsjServiceImplTest extends BaseDbUnitTest {

    @Resource
    private HbjzsjServiceImpl hbjzsjService;

    @Resource
    private HbjzsjMapper hbjzsjMapper;

    @Test
    public void testCreateHbjzsj_success() {
        // 准备参数
        HbjzsjCreateReqVO reqVO = randomPojo(HbjzsjCreateReqVO.class);

        // 调用
        Long hbjzsjId = hbjzsjService.createHbjzsj(reqVO);
        // 断言
        assertNotNull(hbjzsjId);
        // 校验记录的属性是否正确
        HbjzsjDO hbjzsj = hbjzsjMapper.selectById(hbjzsjId);
        assertPojoEquals(reqVO, hbjzsj);
    }

    @Test
    public void testUpdateHbjzsj_success() {
        // mock 数据
        HbjzsjDO dbHbjzsj = randomPojo(HbjzsjDO.class);
        hbjzsjMapper.insert(dbHbjzsj);// @Sql: 先插入出一条存在的数据
        // 准备参数
        HbjzsjUpdateReqVO reqVO = randomPojo(HbjzsjUpdateReqVO.class, o -> {
            o.setId(dbHbjzsj.getId()); // 设置更新的 ID
        });

        // 调用
        hbjzsjService.updateHbjzsj(reqVO);
        // 校验是否更新正确
        HbjzsjDO hbjzsj = hbjzsjMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, hbjzsj);
    }

    @Test
    public void testUpdateHbjzsj_notExists() {
        // 准备参数
        HbjzsjUpdateReqVO reqVO = randomPojo(HbjzsjUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> hbjzsjService.updateHbjzsj(reqVO), HBJZSJ_NOT_EXISTS);
    }

    @Test
    public void testDeleteHbjzsj_success() {
        // mock 数据
        HbjzsjDO dbHbjzsj = randomPojo(HbjzsjDO.class);
        hbjzsjMapper.insert(dbHbjzsj);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbHbjzsj.getId();

        // 调用
        hbjzsjService.deleteHbjzsj(id);
       // 校验数据不存在了
       assertNull(hbjzsjMapper.selectById(id));
    }

    @Test
    public void testDeleteHbjzsj_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> hbjzsjService.deleteHbjzsj(id), HBJZSJ_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHbjzsjPage() {
       // mock 数据
       HbjzsjDO dbHbjzsj = randomPojo(HbjzsjDO.class, o -> { // 等会查询到
           o.setBjflid(null);
           o.setWlbh(null);
           o.setWlms(null);
           o.setXhge(null);
           o.setJldw(null);
           o.setLyyz(null);
           o.setAqkcsl(null);
           o.setZxcgpl(null);
           o.setMrck(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       hbjzsjMapper.insert(dbHbjzsj);
       // 测试 bjflid 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setBjflid(null)));
       // 测试 wlbh 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setWlbh(null)));
       // 测试 wlms 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setWlms(null)));
       // 测试 xhge 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setXhge(null)));
       // 测试 jldw 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setJldw(null)));
       // 测试 lyyz 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setLyyz(null)));
       // 测试 aqkcsl 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setAqkcsl(null)));
       // 测试 zxcgpl 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setZxcgpl(null)));
       // 测试 mrck 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setMrck(null)));
       // 测试 bz 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setCreateTime(null)));
       // 准备参数
       HbjzsjPageReqVO reqVO = new HbjzsjPageReqVO();
       reqVO.setBjflid(null);
       reqVO.setWlbh(null);
       reqVO.setWlms(null);
       reqVO.setXhge(null);
       reqVO.setJldw(null);
       reqVO.setLyyz(null);
       reqVO.setAqkcsl(null);
       reqVO.setZxcgpl(null);
       reqVO.setMrck(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<HbjzsjPageItemRespVO> pageResult = hbjzsjService.getHbjzsjPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbHbjzsj, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHbjzsjList() {
       // mock 数据
       HbjzsjDO dbHbjzsj = randomPojo(HbjzsjDO.class, o -> { // 等会查询到
           o.setBjflid(null);
           o.setWlbh(null);
           o.setWlms(null);
           o.setXhge(null);
           o.setJldw(null);
           o.setLyyz(null);
           o.setAqkcsl(null);
           o.setZxcgpl(null);
           o.setMrck(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       hbjzsjMapper.insert(dbHbjzsj);
       // 测试 bjflid 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setBjflid(null)));
       // 测试 wlbh 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setWlbh(null)));
       // 测试 wlms 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setWlms(null)));
       // 测试 xhge 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setXhge(null)));
       // 测试 jldw 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setJldw(null)));
       // 测试 lyyz 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setLyyz(null)));
       // 测试 aqkcsl 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setAqkcsl(null)));
       // 测试 zxcgpl 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setZxcgpl(null)));
       // 测试 mrck 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setMrck(null)));
       // 测试 bz 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       hbjzsjMapper.insert(cloneIgnoreId(dbHbjzsj, o -> o.setCreateTime(null)));
       // 准备参数
       HbjzsjExportReqVO reqVO = new HbjzsjExportReqVO();
       reqVO.setBjflid(null);
       reqVO.setWlbh(null);
       reqVO.setWlms(null);
       reqVO.setXhge(null);
       reqVO.setJldw(null);
       reqVO.setLyyz(null);
       reqVO.setAqkcsl(null);
       reqVO.setZxcgpl(null);
       reqVO.setMrck(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<HbjzsjDO> list = hbjzsjService.getHbjzsjList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbHbjzsj, list.get(0));
    }

}
