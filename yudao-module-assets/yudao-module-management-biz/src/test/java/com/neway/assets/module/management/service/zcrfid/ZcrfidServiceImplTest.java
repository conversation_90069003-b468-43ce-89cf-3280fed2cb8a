package com.neway.assets.module.management.service.zcrfid;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidCreateReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidExportReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidPageReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidUpdateReqVO;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;
import com.neway.assets.module.management.dal.mysql.zcrfid.ZcrfidMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCRFID_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcrfidServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcrfidServiceImpl.class)
public class ZcrfidServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcrfidServiceImpl zcrfidService;

    @Resource
    private ZcrfidMapper zcrfidMapper;

    @Test
    public void testCreateZcrfid_success() {
        // 准备参数
        ZcrfidCreateReqVO reqVO = randomPojo(ZcrfidCreateReqVO.class);

        // 调用
        Long zcrfidId = zcrfidService.createZcrfid(reqVO);
        // 断言
        assertNotNull(zcrfidId);
        // 校验记录的属性是否正确
        ZcrfidDO zcrfid = zcrfidMapper.selectById(zcrfidId);
        assertPojoEquals(reqVO, zcrfid);
    }

    @Test
    public void testUpdateZcrfid_success() {
        // mock 数据
        ZcrfidDO dbZcrfid = randomPojo(ZcrfidDO.class);
        zcrfidMapper.insert(dbZcrfid);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcrfidUpdateReqVO reqVO = randomPojo(ZcrfidUpdateReqVO.class, o -> {
            o.setId(dbZcrfid.getId()); // 设置更新的 ID
        });

        // 调用
        zcrfidService.updateZcrfid(reqVO);
        // 校验是否更新正确
        ZcrfidDO zcrfid = zcrfidMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcrfid);
    }

    @Test
    public void testUpdateZcrfid_notExists() {
        // 准备参数
        ZcrfidUpdateReqVO reqVO = randomPojo(ZcrfidUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcrfidService.updateZcrfid(reqVO), ZCRFID_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcrfid_success() {
        // mock 数据
        ZcrfidDO dbZcrfid = randomPojo(ZcrfidDO.class);
        zcrfidMapper.insert(dbZcrfid);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcrfid.getId();

        // 调用
        zcrfidService.deleteZcrfid(id);
       // 校验数据不存在了
       assertNull(zcrfidMapper.selectById(id));
    }

    @Test
    public void testDeleteZcrfid_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcrfidService.deleteZcrfid(id), ZCRFID_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcrfidPage() {
       // mock 数据
       ZcrfidDO dbZcrfid = randomPojo(ZcrfidDO.class, o -> { // 等会查询到
           o.setZclb(null);
           o.setQybs(null);
           o.setZcid(null);
           o.setKsrq(null);
           o.setJzrq(null);
           o.setRfidh(null);
           o.setBz(null);
           o.setFfyy(null);
           o.setCreateTime(null);
       });
       zcrfidMapper.insert(dbZcrfid);
       // 测试 zclb 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setZclb(null)));
       // 测试 qybs 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setQybs(null)));
       // 测试 zcid 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setZcid(null)));
       // 测试 ksrq 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setKsrq(null)));
       // 测试 jzrq 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setJzrq(null)));
       // 测试 rfidh 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setRfidh(null)));
       // 测试 bz 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setBz(null)));
       // 测试 ffyy 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setFfyy(null)));
       // 测试 createTime 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setCreateTime(null)));
       // 准备参数
       ZcrfidPageReqVO reqVO = new ZcrfidPageReqVO();
       reqVO.setZclb(null);
       reqVO.setQybs(null);
       reqVO.setZcid(null);
       reqVO.setKsrq(null);
       reqVO.setJzrq(null);
       reqVO.setRfidh(null);
       reqVO.setBz(null);
       reqVO.setFfyy(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcrfidDO> pageResult = zcrfidService.getZcrfidPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcrfid, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcrfidList() {
       // mock 数据
       ZcrfidDO dbZcrfid = randomPojo(ZcrfidDO.class, o -> { // 等会查询到
           o.setZclb(null);
           o.setQybs(null);
           o.setZcid(null);
           o.setKsrq(null);
           o.setJzrq(null);
           o.setRfidh(null);
           o.setBz(null);
           o.setFfyy(null);
           o.setCreateTime(null);
       });
       zcrfidMapper.insert(dbZcrfid);
       // 测试 zclb 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setZclb(null)));
       // 测试 qybs 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setQybs(null)));
       // 测试 zcid 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setZcid(null)));
       // 测试 ksrq 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setKsrq(null)));
       // 测试 jzrq 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setJzrq(null)));
       // 测试 rfidh 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setRfidh(null)));
       // 测试 bz 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setBz(null)));
       // 测试 ffyy 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setFfyy(null)));
       // 测试 createTime 不匹配
       zcrfidMapper.insert(cloneIgnoreId(dbZcrfid, o -> o.setCreateTime(null)));
       // 准备参数
       ZcrfidExportReqVO reqVO = new ZcrfidExportReqVO();
       reqVO.setZclb(null);
       reqVO.setQybs(null);
       reqVO.setZcid(null);
       reqVO.setKsrq(null);
       reqVO.setJzrq(null);
       reqVO.setRfidh(null);
       reqVO.setBz(null);
       reqVO.setFfyy(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcrfidDO> list = zcrfidService.getZcrfidList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcrfid, list.get(0));
    }

}
