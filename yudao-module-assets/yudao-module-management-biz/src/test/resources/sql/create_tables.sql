CREATE TABLE IF NOT EXISTS "mt_zczsj" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zcfl" bigint NOT NULL,
    "zcbh" varchar NOT NULL,
    "cwbh" varchar NOT NULL,
    "zcmc" varchar NOT NULL,
    "ggxh" varchar NOT NULL,
    "zcbm" bigint NOT NULL,
    "zcdd" bigint,
    "zgy" bigint,
    "gys" bigint,
    "whs" bigint,
    "htbh" varchar,
    "grrq" varchar,
    "zcsl" varchar,
    "jldw" bigint,
    "zcdjrq" varchar,
    "hsgrje" varchar,
    "bhsgrje" varchar,
    "zcbz" bigint,
    "zbq" int,
    "czl" varchar,
    "czs" varchar,
    "dqsycz" varchar,
    "synxian" int,
    "bfrq" varchar,
    "zczt" int,
    "zczp" varchar,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产主数据';

CREATE TABLE IF NOT EXISTS "mt_zzcxx" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,"zczsjid" bigint NOT NULL,
    "zzcbh" varchar NOT NULL,
    "zzcmc" varchar NOT NULL,
    "zzcge" varchar NOT NULL,
    "zzczbq" int,
    "zzcsl" varchar,
    "zzczzs" int,
    "zzczt" int,
    "zzczp" varchar,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产主数据子资产';

CREATE TABLE IF NOT EXISTS "op_zcydjl" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zclb" int NOT NULL,
    "zcxxid" bigint NOT NULL,
    "ydfs" varchar,
    "yzgbm" bigint,
    "yzgy" bigint,
    "mbzgbm" bigint,
    "mbzgy" bigint,
    "ksrq" varchar,
    "jzrq" varchar,
    "ydzt" int,
    "bz" varchar,
    "process_instance_id" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产异动记录表';

CREATE TABLE IF NOT EXISTS "mt_dljzsj" (
   "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
   "dljfl" int NOT NULL,
   "dljlh" varchar NOT NULL,
   "dljmc" varchar NOT NULL,
   "dljgexh" varchar NOT NULL,
   "jzzq" int,
   "syzt" int,
   "glbm" bigint,
   "glren" bigint,
   "bz" varchar,
   "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
   "creator" varchar DEFAULT '',
   "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   "updater" varchar DEFAULT '',
   "deleted" bit NOT NULL DEFAULT FALSE,
   PRIMARY KEY ("id")
) COMMENT '刀/量具主数据';


