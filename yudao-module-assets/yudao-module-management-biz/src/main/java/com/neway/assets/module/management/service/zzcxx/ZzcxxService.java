package com.neway.assets.module.management.service.zzcxx;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.*;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 子资产数据 Service 接口
 *
 * <AUTHOR>
 */
public interface ZzcxxService {

    /**
     * 创建子资产数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZzcxx(@Valid ZzcxxCreateReqVO createReqVO);

    /**
     * 更新子资产数据
     *
     * @param updateReqVO 更新信息
     */
    void updateZzcxx(@Valid ZzcxxUpdateReqVO updateReqVO);

    /**
     * 删除子资产数据
     *
     * @param id 编号
     */
    void deleteZzcxx(Long id);

    /**
     * 获得子资产数据
     *
     * @param id 编号
     * @return 子资产数据
     */
    ZzcxxDO getZzcxx(Long id);

    /**
     * 获得子资产数据列表
     *
     * @param ids 编号
     * @return 子资产数据列表
     */
    List<ZzcxxDO> getZzcxxList(Collection<Long> ids);

    /**
     * 获得子资产数据分页
     *
     * @param pageReqVO 分页查询
     * @return 子资产数据分页
     */
    PageResult<ZzcxxDO> getZzcxxPage(ZzcxxPageReqVO pageReqVO);

    /**
     * 获得子资产数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 子资产数据列表
     */
    List<ZzcxxDO> getZzcxxList(ZzcxxExportReqVO exportReqVO);

}
