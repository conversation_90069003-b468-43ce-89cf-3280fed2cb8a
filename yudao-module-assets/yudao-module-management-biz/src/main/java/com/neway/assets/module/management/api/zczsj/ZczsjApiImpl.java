package com.neway.assets.module.management.api.zczsj;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;
import com.neway.assets.module.management.convert.zczsj.ZczsjConvert;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import com.neway.assets.module.management.service.zczsj.ZczsjService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/25 15:03
 **/
@Service
@RequiredArgsConstructor
public class ZczsjApiImpl implements ZczsjApi{

    private final ZczsjService zczsjService;

    @Override
    public List<ZczsjRespDTO> getZczsjList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<ZczsjDO> list = zczsjService.getZczsjList(ids);
        return ZczsjConvert.INSTANCE.convertList03(list);
    }

    @Override
    public List<ZczsjRespDTO> getZczsjListByTypeId(Collection<Long> typeIds) {
        return ZczsjConvert.INSTANCE.convertList03(zczsjService.getZczsjListByTypeIds(typeIds));
    }
}
