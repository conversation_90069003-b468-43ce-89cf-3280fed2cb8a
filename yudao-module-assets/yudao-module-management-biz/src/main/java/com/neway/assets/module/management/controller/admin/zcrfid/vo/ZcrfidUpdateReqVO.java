package com.neway.assets.module.management.controller.admin.zcrfid.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 资产RFID关联更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcrfidUpdateReqVO extends ZcrfidBaseVO {

    @Schema(description = "自增长id", required = true, example = "29782")
    @NotNull(message = "自增长id不能为空")
    private Long id;

    @Schema(description = "启用标识")
    private Integer qybs;

}
