package com.neway.assets.module.management.dal.mysql.dljzsj;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.management.dal.dataobject.dljzsj.DljzsjDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.*;

/**
 * 刀/量具主数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DljzsjMapper extends BaseMapperX<DljzsjDO> {

    default PageResult<DljzsjDO> selectPage(DljzsjPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DljzsjDO>()
                .eqIfPresent(DljzsjDO::getDljfl, reqVO.getDljfl())
                .eqIfPresent(DljzsjDO::getDljlh, reqVO.getDljlh())
                .eqIfPresent(DljzsjDO::getDljmc, reqVO.getDljmc())
                .eqIfPresent(DljzsjDO::getDljgexh, reqVO.getDljgexh())
                .eqIfPresent(DljzsjDO::getJzzq, reqVO.getJzzq())
                .eqIfPresent(DljzsjDO::getSyzt, reqVO.getSyzt())
                .eqIfPresent(DljzsjDO::getGlbm, reqVO.getGlbm())
                .eqIfPresent(DljzsjDO::getGlren, reqVO.getGlren())
                .likeIfPresent(DljzsjDO::getBz, reqVO.getBz())
                .betweenIfPresent(DljzsjDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DljzsjDO::getId));
    }

    default List<DljzsjDO> selectList(DljzsjExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DljzsjDO>()
                .eqIfPresent(DljzsjDO::getDljfl, reqVO.getDljfl())
                .eqIfPresent(DljzsjDO::getDljlh, reqVO.getDljlh())
                .eqIfPresent(DljzsjDO::getDljmc, reqVO.getDljmc())
                .eqIfPresent(DljzsjDO::getDljgexh, reqVO.getDljgexh())
                .eqIfPresent(DljzsjDO::getJzzq, reqVO.getJzzq())
                .eqIfPresent(DljzsjDO::getSyzt, reqVO.getSyzt())
                .eqIfPresent(DljzsjDO::getGlbm, reqVO.getGlbm())
                .eqIfPresent(DljzsjDO::getGlren, reqVO.getGlren())
                .likeIfPresent(DljzsjDO::getBz, reqVO.getBz())
                .betweenIfPresent(DljzsjDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DljzsjDO::getId));
    }

}
