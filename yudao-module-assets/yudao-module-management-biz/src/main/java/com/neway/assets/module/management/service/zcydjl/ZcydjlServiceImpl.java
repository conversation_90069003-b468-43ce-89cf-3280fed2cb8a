package com.neway.assets.module.management.service.zcydjl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.management.controller.admin.zcydjl.vo.*;
import com.neway.assets.module.management.convert.zcydjl.ZcydjlConvert;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import com.neway.assets.module.management.dal.mysql.zcydjl.ZcydjlMapper;
import com.neway.assets.module.management.handler.AssetsAdjustHandlerFactory;
import com.neway.assets.module.management.service.zczsj.ZczsjService;
import com.neway.assets.module.management.service.zzcxx.ZzcxxService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.ENTIRE;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.PART;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCLB_NOT_EXISTS;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCYDJL_NOT_EXISTS;

/**
 * 资产异动记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class ZcydjlServiceImpl implements ZcydjlService {

    private final ZcydjlMapper zcydjlMapper;

    private final ZczsjService zczsjService;
    private final ZzcxxService zzcxxService;

    private final AssetsAdjustHandlerFactory assetsAdjustHandlerFactory;

    private final DeptApi deptApi;
    private final AdminUserApi adminUserApi;
    private final BpmProcessInstanceApi processInstanceApi;

    @Override
    public Long createZcydjl(ZcydjlCreateReqVO createReqVO) {
        // 插入
        ZcydjlDO zcydjl = ZcydjlConvert.INSTANCE.convert(createReqVO);
        zcydjlMapper.insert(zcydjl);
        // 返回
        return zcydjl.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createZcydjl(Long userId, ZcydjlCreateReqVO createReqVO) {
        // 保存异动数据
        LocalDateTime now = LocalDateTime.now();
        ZcydjlDO zcydjl = (ZcydjlDO) ZcydjlConvert.INSTANCE.convert(createReqVO)
                .setCreator(userId.toString()).setCreateTime(now)
                .setUpdater(userId.toString()).setUpdateTime(now);
        zcydjlMapper.insert(zcydjl);
        // 发起异动流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(createReqVO.getYdfs())
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(zcydjl.getId())));
        // 更新流程ID到异动表
        zcydjlMapper.updateById(new ZcydjlDO().setId(zcydjl.getId())
                .setProcessInstanceId(processInstanceId)
                .setYdzt(BpmTaskStatusEnum.RUNNING.getStatus()));
        return zcydjl.getId();
    }

    @Override
    public void updateZcydjl(ZcydjlUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcydjlExists(updateReqVO.getId());
        // 更新
        ZcydjlDO updateObj = ZcydjlConvert.INSTANCE.convert(updateReqVO);
        zcydjlMapper.updateById(updateObj);
    }

    @Override
    public void updateZcydjlStatus(Long id, Integer result) {
        validateZcydjlExists(id);
        zcydjlMapper.updateById(new ZcydjlDO().setId(id).setYdzt(result));
    }

    @Override
    public void deleteZcydjl(Long id) {
        // 校验存在
        validateZcydjlExists(id);
        // 删除
        zcydjlMapper.deleteById(id);
    }

    private void validateZcydjlExists(Long id) {
        if (zcydjlMapper.selectById(id) == null) {
            throw exception(ZCYDJL_NOT_EXISTS);
        }
    }

    @Override
    public ZcydjlDO getZcydjl(Long id) {
        return zcydjlMapper.selectById(id);
    }

    @Override
    public ZcydjlPageItemRespVO getZcydjlDetail(Long id) {
        ZcydjlDO zcydjl = getZcydjl(id);
        List<ZcydjlPageItemRespVO> list = convertPageItemVO(Collections.singletonList(zcydjl));
        return list.get(0);
    }

    @Override
    public List<ZcydjlDO> getZcydjlList(Collection<Long> ids) {
        return zcydjlMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcydjlPageItemRespVO> getZcydjlPage(ZcydjlPageReqVO pageReqVO) {
        PageResult<ZcydjlDO> pageResult = zcydjlMapper.selectPage(pageReqVO);
        return new PageResult<>(convertPageItemVO(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public List<ZcydjlDO> getZcydjlList(ZcydjlExportReqVO exportReqVO) {
        return zcydjlMapper.selectList(exportReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateZcydjlResult(Long id, Integer result) {
        validateZcydjlExists(id);
        // 流程通过，则完成相关资产调度
        if (Objects.equals(result, BpmProcessInstanceStatusEnum.APPROVE.getStatus())) {
            ZcydjlDO record = getZcydjl(id);
            assetsAdjustHandlerFactory.getAssetsAdjustHandler(record).handle(record);
        }
        // 更新数据库结果
        zcydjlMapper.updateById(new ZcydjlDO().setId(id).setYdzt(result));
    }


    private List<ZcydjlPageItemRespVO> convertPageItemVO(List<ZcydjlDO> source) {
        if (CollUtil.isEmpty(source)) return Collections.emptyList();
        // 获取基础数据
        Set<Long> deptIdSet = convertSet(source, ZcydjlDO::getYzgbm);
        deptIdSet.addAll(convertSet(source, ZcydjlDO::getMbzgbm));
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(deptIdSet);
        Set<Long> adminIdSet = convertSet(source, ZcydjlDO::getYzgy);
        adminIdSet.addAll(convertSet(source, ZcydjlDO::getMbzgy));
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(adminIdSet);

        Set<Long> mainIds = source.stream()
                .filter(o -> Objects.equals(o.getZclb(), ENTIRE.getVal()))
                .map(ZcydjlDO::getZcxxid)
                .collect(Collectors.toSet());
        Map<Long, ZczsjDO> zczsjMap = mainIds.isEmpty() ? Collections.emptyMap()
                : CollectionUtils.convertMap(zczsjService.getZczsjList(mainIds), ZczsjDO::getId);
        Set<Long> childIds = source.stream()
                .filter(o -> Objects.equals(o.getZclb(), PART.getVal()))
                .map(ZcydjlDO::getZcxxid)
                .collect(Collectors.toSet());
        Map<Long, ZzcxxDO> zzcxxMap = childIds.isEmpty() ? Collections.emptyMap()
                : CollectionUtils.convertMap(zzcxxService.getZzcxxList(childIds), ZzcxxDO::getId);
        // 拼接数据
        List<ZcydjlPageItemRespVO> result = new ArrayList<>();
        source.forEach(o -> {
            ZcydjlPageItemRespVO respVO = ZcydjlConvert.INSTANCE.convert2(o);
            respVO.setSrcAdmin(userMap.get(respVO.getYzgy()).getNickname());
            respVO.setSrcDept(deptMap.get(respVO.getYzgbm()).getName());
            respVO.setTarAdmin(userMap.get(respVO.getMbzgy()).getNickname());
            respVO.setTarDept(deptMap.get(respVO.getMbzgbm()).getName());

            AssetsClassEnum classEnum = AssetsClassEnum.getByVal(o.getZclb());
            if (classEnum == null) throw exception(ZCLB_NOT_EXISTS);
            switch (classEnum) {
                case PART:
                    respVO.setAssetsName(zzcxxMap.get(o.getZcxxid()).getZzcmc());
                    break;
                case ENTIRE:
                    respVO.setAssetsName(zczsjMap.get(o.getZcxxid()).getZcmc());
                    break;
            }
            result.add(respVO);
        });
        return result;
    }
}
