package com.neway.assets.module.management.handler;

import com.neway.assets.module.info.enums.enable.AssetsAdjustTypeEnum;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ADJUST_TYPE_NOT_EXISTS;

/**
 *
 * <AUTHOR>
 * @since 2023/5/24 16:34
 **/
@Component
public class AssetsAdjustHandlerFactory {

    @Resource
    private List<IAssetsAdjustHandler> assetsAdjustHandlers;

    public IAssetsAdjustHandler getAssetsAdjustHandler(ZcydjlDO record) {
        AssetsAdjustTypeEnum adjustType = AssetsAdjustTypeEnum.valueOf(record.getYdfs().toUpperCase());
        return assetsAdjustHandlers.stream()
                .filter(o -> Objects.equals(adjustType, o.getAdjustType()))
                .findAny()
                .orElseThrow(() -> exception(ADJUST_TYPE_NOT_EXISTS));
    }
}
