package com.neway.assets.module.management.convert.zcydjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.management.api.zcydjl.dto.ZcydjlReqDTO;
import com.neway.assets.module.management.controller.admin.zcydjl.vo.*;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资产异动记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcydjlConvert {

    ZcydjlConvert INSTANCE = Mappers.getMapper(ZcydjlConvert.class);

    ZcydjlDO convert(ZcydjlCreateReqVO bean);

    ZcydjlDO convert(ZcydjlUpdateReqVO bean);

    ZcydjlCreateReqVO convert(ZcydjlReqDTO bean);

    @Named("one")
    ZcydjlRespVO convert(ZcydjlDO bean);

    @Named("two")
    ZcydjlPageItemRespVO convert2(ZcydjlDO bean);

    @IterableMapping(qualifiedByName = "one")
    List<ZcydjlRespVO> convertList(List<ZcydjlDO> list);

    PageResult<ZcydjlRespVO> convertPage(PageResult<ZcydjlDO> page);

    List<ZcydjlExcelVO> convertList02(List<ZcydjlDO> list);

}
