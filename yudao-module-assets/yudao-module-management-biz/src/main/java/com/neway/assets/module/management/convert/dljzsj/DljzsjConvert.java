package com.neway.assets.module.management.convert.dljzsj;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.dljzsj.DljzsjDO;

/**
 * 刀/量具主数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DljzsjConvert {

    DljzsjConvert INSTANCE = Mappers.getMapper(DljzsjConvert.class);

    DljzsjDO convert(DljzsjCreateReqVO bean);

    DljzsjDO convert(DljzsjUpdateReqVO bean);

    DljzsjRespVO convert(DljzsjDO bean);

    List<DljzsjRespVO> convertList(List<DljzsjDO> list);

    PageResult<DljzsjRespVO> convertPage(PageResult<DljzsjDO> page);

    List<DljzsjExcelVO> convertList02(List<DljzsjDO> list);

}
