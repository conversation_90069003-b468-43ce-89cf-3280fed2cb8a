package com.neway.assets.module.management.controller.admin.zzcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 子资产数据 Excel 导出 Request VO，参数和 ZzcxxPageReqVO 是一致的")
@Data
public class ZzcxxExportReqVO {

    @Schema(description = "资产主数据", example = "24853")
    private String zczsjid;

    @Schema(description = "子资产编号")
    private String zzcbh;

    @Schema(description = "子资产名称")
    private String zzcmc;

    @Schema(description = "规格型号")
    private String zzcge;

    @Schema(description = "质保期(天)")
    private Integer zzczbq;

    @Schema(description = "数量")
    private BigDecimal zzcsl;

    @Schema(description = "制造商")
    private Integer zzczzs;

    @Schema(description = "子资产状态")
    private Integer zzczt;

    @Schema(description = "资产照片")
    private String zzczp;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
