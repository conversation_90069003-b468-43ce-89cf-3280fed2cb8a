package com.neway.assets.module.management.controller.admin.hbjzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2023/6/7 9:09
 **/
@Schema(description = "管理后台 - 备品/备件主数据详细数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HbjzsjPageItemRespVO extends HbjzsjRespVO{
    @Schema(description = "资类描述", required = true)
    private String classification;
    @Schema(description = "计量单位名称", required = true)
    private String unit;
    @Schema(description = "默认库位", required = true)
    private String stockLocation;
}
