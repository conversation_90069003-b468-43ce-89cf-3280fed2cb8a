package com.neway.assets.module.management.controller.admin.zczsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产主数据 Excel 导出 Request VO，参数和 ZczsjPageReqVO 是一致的")
@Data
public class ZczsjExportReqVO {

    @Schema(description = "资产分类")
    private Long zcfl;

    @Schema(description = "资产编号")
    private String zcbh;

    @Schema(description = "财务编码")
    private String cwbh;

    @Schema(description = "资产名称")
    private String zcmc;

    @Schema(description = "规格型号")
    private String ggxh;

    @Schema(description = "资产部门")
    private Long zcbm;

    @Schema(description = "资产地点")
    private Long zcdd;

    @Schema(description = "SRM系统供应商")
    private Long gys;

    @Schema(description = "维护商")
    private Long whs;

    @Schema(description = "合同编号")
    private String htbh;

    @Schema(description = "购入日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] grrq;

    @Schema(description = "数量")
    private BigDecimal zcsl;

    @Schema(description = "计量单位")
    private Long jldw;

    @Schema(description = "资产登记日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] zcdjrq;

    @Schema(description = "购入金额(含税)")
    private BigDecimal hsgrje;

    @Schema(description = "购入金额(不含税)")
    private BigDecimal bhsgrje;

    @Schema(description = "资产币种")
    private Long zcbz;

    @Schema(description = "质保期")
    private LocalDate zbq;

    @Schema(description = "残值率")
    private BigDecimal czl;

    @Schema(description = "残值")
    private BigDecimal czs;

    @Schema(description = "当期剩余价值")
    private BigDecimal dqsycz;

    @Schema(description = "使用年限")
    private Integer synxian;

    @Schema(description = "报废日期")
    private LocalDate bfrq;

    @Schema(description = "资产状态")
    private Integer zczt;

    @Schema(description = "资产照片")
    private String zczp;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
