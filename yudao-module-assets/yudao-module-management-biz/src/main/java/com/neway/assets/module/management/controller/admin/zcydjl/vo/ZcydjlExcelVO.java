package com.neway.assets.module.management.controller.admin.zcydjl.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 资产异动记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcydjlExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("资产类别")
    private Integer zclb;

    @ExcelProperty("资产id")
    private Long zcxxid;

    @ExcelProperty(value = "异动方式", converter = DictConvert.class)
    @DictFormat("assets_adjust_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private String ydfs;

    @ExcelProperty("原资管部门")
    private Long yzgbm;

    @ExcelProperty("原资管员")
    private Long yzgy;

    @ExcelProperty("目标资管部门")
    private Long mbzgbm;

    @ExcelProperty("目标资管员")
    private Long mbzgy;

    @ExcelProperty("开始日期")
    private LocalDate ksrq;

    @ExcelProperty("截止日期")
    private LocalDate jzrq;

    @ExcelProperty(value = "异动状态", converter = DictConvert.class)
    @DictFormat("assets_adjust_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer ydzt;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("流程编号")
    private String processInstanceId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
