package com.neway.assets.module.management.service.hbjzsj;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.api.hbjfl.HbjflApi;
import com.neway.assets.module.info.api.hbjfl.dto.HbjflRespDTO;
import com.neway.assets.module.info.api.jldw.JldwApi;
import com.neway.assets.module.info.api.jldw.dto.JldwRespDTO;
import com.neway.assets.module.info.api.kwxx.KwxxApi;
import com.neway.assets.module.info.api.kwxx.dto.KwxxRespDTO;
import com.neway.assets.module.management.controller.admin.hbjzsj.vo.*;
import com.neway.assets.module.management.convert.hbjzsj.HbjzsjConvert;
import com.neway.assets.module.management.dal.dataobject.hbjzsj.HbjzsjDO;
import com.neway.assets.module.management.dal.mysql.hbjzsj.HbjzsjMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.HBJZSJ_NOT_EXISTS;

/**
 * 耗材/备品备件主数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class HbjzsjServiceImpl implements HbjzsjService {

    private final HbjzsjMapper hbjzsjMapper;

    private final HbjflApi hbjflApi;
    private final JldwApi jldwApi;
    private final KwxxApi kwxxApi;

    @Override
    public Long createHbjzsj(HbjzsjCreateReqVO createReqVO) {
        // 插入
        HbjzsjDO hbjzsj = HbjzsjConvert.INSTANCE.convert(createReqVO);
        hbjzsjMapper.insert(hbjzsj);
        // 返回
        return hbjzsj.getId();
    }

    @Override
    public void updateHbjzsj(HbjzsjUpdateReqVO updateReqVO) {
        // 校验存在
        validateHbjzsjExists(updateReqVO.getId());
        // 更新
        HbjzsjDO updateObj = HbjzsjConvert.INSTANCE.convert(updateReqVO);
        hbjzsjMapper.updateById(updateObj);
    }

    @Override
    public void deleteHbjzsj(Long id) {
        // 校验存在
        validateHbjzsjExists(id);
        // 删除
        hbjzsjMapper.deleteById(id);
    }

    private void validateHbjzsjExists(Long id) {
        if (hbjzsjMapper.selectById(id) == null) {
            throw exception(HBJZSJ_NOT_EXISTS);
        }
    }

    @Override
    public HbjzsjDO getHbjzsj(Long id) {
        return hbjzsjMapper.selectById(id);
    }

    @Override
    public List<HbjzsjDO> getHbjzsjList(Collection<Long> ids) {
        return hbjzsjMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HbjzsjPageItemRespVO> getHbjzsjPage(HbjzsjPageReqVO pageReqVO) {
        PageResult<HbjzsjDO> pageResult = hbjzsjMapper.selectPage(pageReqVO);
        return new PageResult<>(convertPageItemVO(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public List<HbjzsjDO> getHbjzsjList(HbjzsjExportReqVO exportReqVO) {
        return hbjzsjMapper.selectList(exportReqVO);
    }

    private List<HbjzsjPageItemRespVO> convertPageItemVO(List<HbjzsjDO> source) {
        if (CollUtil.isEmpty(source)) return Collections.emptyList();
        // 获取基础数据
        Map<Long, HbjflRespDTO> classificationMap = hbjflApi.getHbflMap(convertSet(source, HbjzsjDO::getBjflid));
        Map<Long, JldwRespDTO> unitMap = jldwApi.getJldwMap(convertSet(source, HbjzsjDO::getJldw));
        Map<Long, KwxxRespDTO> locationMap = kwxxApi.getKwxxMap(convertSet(source, HbjzsjDO::getMrck));

        // 拼接数据
        List<HbjzsjPageItemRespVO> result = new ArrayList<>();
        source.forEach(item -> {
            HbjzsjPageItemRespVO respVo = HbjzsjConvert.INSTANCE.convert2(item);
            respVo.setClassification(classificationMap.get(item.getBjflid()).getBflmc());
            respVo.setUnit(unitMap.get(item.getJldw()).getJldwmc());
            respVo.setStockLocation(locationMap.get(item.getMrck()).getKwms());

            result.add(respVo);
        });
        return result;
    }
}
