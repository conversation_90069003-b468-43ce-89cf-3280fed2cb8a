package com.neway.assets.module.management.dal.mysql.zzcxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxExportReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxPageReqVO;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 子资产数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZzcxxMapper extends BaseMapperX<ZzcxxDO> {

    default PageResult<ZzcxxDO> selectPage(ZzcxxPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZzcxxDO>()
                .eqIfPresent(ZzcxxDO::getZczsjid, reqVO.getZczsjid())
                .likeIfPresent(ZzcxxDO::getZzcbh, reqVO.getZzcbh())
                .eqIfPresent(ZzcxxDO::getZzcmc, reqVO.getZzcmc())
                .eqIfPresent(ZzcxxDO::getZzcge, reqVO.getZzcge())
                .eqIfPresent(ZzcxxDO::getZzczbq, reqVO.getZzczbq())
                .eqIfPresent(ZzcxxDO::getZzcsl, reqVO.getZzcsl())
                .eqIfPresent(ZzcxxDO::getZzczzs, reqVO.getZzczzs())
                .eqIfPresent(ZzcxxDO::getZzczt, reqVO.getZzczt())
                .eqIfPresent(ZzcxxDO::getZzczp, reqVO.getZzczp())
                .betweenIfPresent(ZzcxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZzcxxDO::getId));
    }

    default List<ZzcxxDO> selectList(ZzcxxExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZzcxxDO>()
                .eqIfPresent(ZzcxxDO::getZczsjid, reqVO.getZczsjid())
                .likeIfPresent(ZzcxxDO::getZzcbh, reqVO.getZzcbh())
                .eqIfPresent(ZzcxxDO::getZzcmc, reqVO.getZzcmc())
                .eqIfPresent(ZzcxxDO::getZzcge, reqVO.getZzcge())
                .eqIfPresent(ZzcxxDO::getZzczbq, reqVO.getZzczbq())
                .eqIfPresent(ZzcxxDO::getZzcsl, reqVO.getZzcsl())
                .eqIfPresent(ZzcxxDO::getZzczzs, reqVO.getZzczzs())
                .eqIfPresent(ZzcxxDO::getZzczt, reqVO.getZzczt())
                .eqIfPresent(ZzcxxDO::getZzczp, reqVO.getZzczp())
                .betweenIfPresent(ZzcxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZzcxxDO::getId));
    }

}
