package com.neway.assets.module.management.api.zzcxx;

import com.neway.assets.module.management.api.zzcxx.dto.ZzcxxRespDTO;
import com.neway.assets.module.management.convert.zzcxx.ZzcxxConvert;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import com.neway.assets.module.management.service.zzcxx.ZzcxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/25 17:11
 **/
@Service
public class ZzcxxApiImpl implements ZzcxxApi{
    @Resource
    private ZzcxxService zzcxxService;

    @Override
    public List<ZzcxxRespDTO> getZzcxxList(Collection<Long> ids) {
        List<ZzcxxDO> list = zzcxxService.getZzcxxList(ids);
        return ZzcxxConvert.INSTANCE.convertList03(list);
    }
}
