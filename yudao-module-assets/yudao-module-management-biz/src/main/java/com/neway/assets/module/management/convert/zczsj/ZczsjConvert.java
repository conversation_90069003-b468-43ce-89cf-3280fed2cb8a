package com.neway.assets.module.management.convert.zczsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlCreateReqDTO;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;
import com.neway.assets.module.management.controller.admin.zczsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资产主数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZczsjConvert {

    ZczsjConvert INSTANCE = Mappers.getMapper(ZczsjConvert.class);

    ZczsjDO convert(ZczsjCreateReqVO bean);

    ZczsjDO convert(ZczsjUpdateReqVO bean);

    @Named("one")
    ZczsjRespVO convert(ZczsjDO bean);

    @Named("two")
    ZczsjPageItemRespVO convert2(ZczsjDO bean);

    default SbybjlCreateReqDTO convert3(ZczsjDO bean) {
        SbybjlCreateReqDTO createReqDTO = new SbybjlCreateReqDTO();
        createReqDTO.setGysid(bean.getGys());
        createReqDTO.setHtxy(bean.getHtbh());
        createReqDTO.setZbdqr(bean.getZbq());
        createReqDTO.setZcid(bean.getId());
        createReqDTO.setZzbs(AssetsClassEnum.ENTIRE.getVal());
        createReqDTO.setWbfy(bean.getBhsgrje());
        return createReqDTO;
    }

    @IterableMapping(qualifiedByName = "one")
    List<ZczsjRespVO> convertList(List<ZczsjDO> list);

    PageResult<ZczsjRespVO> convertPage(PageResult<ZczsjDO> page);

    List<ZczsjExcelVO> convertList02(List<ZczsjDO> list);

    List<ZczsjRespDTO> convertList03(List<ZczsjDO> list);

    List<ZczsjSimpleRespVO> convertSimpleList(List<ZczsjDO> list);

}
