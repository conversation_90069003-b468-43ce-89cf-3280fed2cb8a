package com.neway.assets.module.management.dal.dataobject.dljzsj;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 刀/量具主数据 DO
 *
 * <AUTHOR>
 */
@TableName(value = "mt_dljzsj", schema = "gdzc")
@KeySequence("mt_dljzsj_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DljzsjDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 刀/量具分类（0：量具 1：刀具）
     *
     * 枚举 {@link TODO dljfl 对应的类}
     */
    private Integer dljfl;
    /**
     * 刀/量具料号
     */
    private String dljlh;
    /**
     * 刀/量具名称
     */
    private String dljmc;
    /**
     * 规格型号
     */
    private String dljgexh;
    /**
     * 校准周期
     */
    private Integer jzzq;
    /**
     * 使用状态（0：报废，1：使用  2：维修 3：校准）
     *
     * 枚举 {@link TODO dljsyzt 对应的类}
     */
    private Integer syzt;
    /**
     * 管理部门（组织结构->id）
     */
    private Long glbm;
    /**
     * 管理人（人员管理->id）
     */
    private Long glren;
    /**
     * 备注
     */
    private String bz;

}
