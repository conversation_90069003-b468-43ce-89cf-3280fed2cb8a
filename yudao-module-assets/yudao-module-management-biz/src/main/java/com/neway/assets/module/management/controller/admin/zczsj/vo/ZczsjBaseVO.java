package com.neway.assets.module.management.controller.admin.zczsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
* 资产主数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZczsjBaseVO {

    @Schema(description = "资产分类", required = true)
    @NotNull(message = "资产分类不能为空")
    private Long zcfl;

    @Schema(description = "资产二级分类", required = true)
    @NotNull(message = "资产分类不能为空")
    private Long secondZcfl;

    @Schema(description = "资产编号", required = true)
    @NotNull(message = "资产编号不能为空")
    private String zcbh;

    @Schema(description = "财务编码", required = true)
    @NotNull(message = "财务编码不能为空")
    private String cwbh;

    @Schema(description = "资产名称", required = true)
    @NotNull(message = "资产名称不能为空")
    private String zcmc;

    @Schema(description = "规格型号", required = true)
    @NotNull(message = "规格型号不能为空")
    private String ggxh;

    @Schema(description = "资产部门", required = true)
    @NotNull(message = "资产部门不能为空")
    private Long zcbm;

    @Schema(description = "资产地点")
    private Long zcdd;

    @Schema(description = "安装位置")
    private String azwz;

    @Schema(description = "SRM系统供应商")
    private Long gys;

    @Schema(description = "维护商")
    private Long whs;

    @Schema(description = "合同编号")
    private String htbh;

    @Schema(description = "购入日期")
    private LocalDate grrq;

    @Schema(description = "数量")
    private BigDecimal zcsl;

    @Schema(description = "计量单位")
    private Long jldw;

    @Schema(description = "资产登记日期")
    private LocalDate zcdjrq;

    @Schema(description = "购入金额(含税)")
    private BigDecimal hsgrje;

    @Schema(description = "购入金额(不含税)")
    private BigDecimal bhsgrje;

    @Schema(description = "资产币种")
    private Long zcbz;

    @Schema(description = "质保期")
    private LocalDate zbq;

    @Schema(description = "残值率")
    private BigDecimal czl;

    @Schema(description = "残值")
    private BigDecimal czs;

    @Schema(description = "当期剩余价值")
    private BigDecimal dqsycz;

    @Schema(description = "使用年限")
    private Integer synxian;

    @Schema(description = "报废日期")
    private LocalDate bfrq;

    @Schema(description = "资产使用人")
    private Long zcsyr;

    @Schema(description = "资产状态")
    private Integer zczt;

    @Schema(description = "资产照片")
    private String zczp;

    @Schema(description = "备注")
    private String bz;

}
