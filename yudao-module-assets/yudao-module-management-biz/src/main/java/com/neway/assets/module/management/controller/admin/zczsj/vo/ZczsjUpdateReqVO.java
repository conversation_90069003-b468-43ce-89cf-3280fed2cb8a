package com.neway.assets.module.management.controller.admin.zczsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 资产主数据更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZczsjUpdateReqVO extends ZczsjBaseVO {

    @Schema(description = "自增长id", required = true, example = "18657")
    @NotNull(message = "自增长id不能为空")
    private Long id;

    @Schema(description = "RFID")
    private String rfid;

    @Schema(description = "RFID-到期/截止日期")
    private LocalDate jzrq;

}
