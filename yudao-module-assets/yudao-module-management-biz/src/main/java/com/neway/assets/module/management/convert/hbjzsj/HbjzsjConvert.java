package com.neway.assets.module.management.convert.hbjzsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.management.controller.admin.hbjzsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.hbjzsj.HbjzsjDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 耗材/备品备件主数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface HbjzsjConvert {

    HbjzsjConvert INSTANCE = Mappers.getMapper(HbjzsjConvert.class);

    HbjzsjDO convert(HbjzsjCreateReqVO bean);

    HbjzsjDO convert(HbjzsjUpdateReqVO bean);

    @Named("one")
    HbjzsjRespVO convert(HbjzsjDO bean);
    @Named("two")
    HbjzsjPageItemRespVO convert2(HbjzsjDO bean);

    @IterableMapping(qualifiedByName = "one")
    List<HbjzsjRespVO> convertList(List<HbjzsjDO> list);

    PageResult<HbjzsjRespVO> convertPage(PageResult<HbjzsjDO> page);

    List<HbjzsjExcelVO> convertList02(List<HbjzsjDO> list);

    List<HbjzsjSimpleRespVO> convertSimpleList(List<HbjzsjDO> list);

}
