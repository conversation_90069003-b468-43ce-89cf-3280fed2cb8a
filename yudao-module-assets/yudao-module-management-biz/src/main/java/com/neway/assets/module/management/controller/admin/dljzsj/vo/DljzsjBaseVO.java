package com.neway.assets.module.management.controller.admin.dljzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 刀/量具主数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DljzsjBaseVO {

    @Schema(description = "刀/量具分类（0：量具 1：刀具）", required = true)
    @NotNull(message = "刀/量具分类（0：量具 1：刀具）不能为空")
    private Integer dljfl;

    @Schema(description = "刀/量具料号", required = true)
    @NotNull(message = "刀/量具料号不能为空")
    private String dljlh;

    @Schema(description = "刀/量具名称", required = true)
    @NotNull(message = "刀/量具名称不能为空")
    private String dljmc;

    @Schema(description = "规格型号", required = true)
    @NotNull(message = "规格型号不能为空")
    private String dljgexh;

    @Schema(description = "校准周期")
    private Integer jzzq;

    @Schema(description = "使用状态（0：报废，1：使用  2：维修 3：校准）")
    private Integer syzt;

    @Schema(description = "管理部门（组织结构->id）")
    private Long glbm;

    @Schema(description = "管理人（人员管理->id）")
    private Long glren;

    @Schema(description = "备注")
    private String bz;

}
