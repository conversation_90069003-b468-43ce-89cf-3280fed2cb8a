package com.neway.assets.module.management.controller.admin.zczsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 资产主数据详细数据分页类
 * <AUTHOR>
 * @since 2023/5/8 14:15
 **/
@Schema(description = "管理后台 - 资产主数据详细数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZczsjPageItemRespVO extends ZczsjRespVO {

    @Schema(description = "资产分类描述", required = true)
    private String assetsType;
    @Schema(description = "资产二级分类描述", required = true)
    private String secondAssetsType;
    @Schema(description = "资产部门名称", required = true)
    private String dept;
    @Schema(description = "资产地点名称", required = true)
    private String location;
    @Schema(description = "资管员名称", required = true)
    private String admin;
    @Schema(description = "资产使用人名称", required = true)
    private String userName;
    @Schema(description = "计量单位名称", required = true)
    private String unit;
    @Schema(description = "币种名", required = true)
    private String currency;

}