package com.neway.assets.module.management.controller.admin.hbjzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 耗材/备品备件主数据 Excel 导出 Request VO，参数和 HbjzsjPageReqVO 是一致的")
@Data
public class HbjzsjExportReqVO {

    @Schema(description = "耗材/备品备件分类", example = "17547")
    private Long bjflid;

    @Schema(description = "物料编号")
    private String wlbh;

    @Schema(description = "物料描述")
    private String wlms;

    @Schema(description = "型号规格")
    private String xhge;

    @Schema(description = "计量单位")
    private Long jldw;

    @Schema(description = "库存领用原则")
    private Integer lyyz;

    @Schema(description = "安全库存数")
    private BigDecimal aqkcsl;

    @Schema(description = "最小采购批量")
    private BigDecimal zxcgpl;

    @Schema(description = "默认仓库")
    private Long mrck;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
