package com.neway.assets.module.management.dal.dataobject.zzcxx;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 子资产数据 DO
 *
 * <AUTHOR>
 */
@TableName(value = "mt_zzcxx", schema = "gdzc")
@KeySequence("mt_zzcxx_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZzcxxDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 资产主数据
     */
    private String zczsjid;
    /**
     * 子资产编号
     */
    private String zzcbh;
    /**
     * 子资产名称
     */
    private String zzcmc;
    /**
     * 规格型号
     */
    private String zzcge;
    /**
     * 质保期(天)
     */
    private Integer zzczbq;
    /**
     * 数量
     */
    private BigDecimal zzcsl;
    /**
     * 制造商
     */
    private Integer zzczzs;
    /**
     * 子资产状态
     *
     */
    private Integer zzczt;
    /**
     * 资产照片
     */
    private String zzczp;
    /**
     * 备注
     */
    private String bz;

}
