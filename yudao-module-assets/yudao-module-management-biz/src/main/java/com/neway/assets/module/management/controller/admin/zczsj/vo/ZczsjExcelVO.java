package com.neway.assets.module.management.controller.admin.zczsj.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 资产主数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZczsjExcelVO {

    @ExcelProperty("资产分类")
    private Long zcfl;

    @ExcelProperty("资产编号")
    private String zcbh;

    @ExcelProperty("财务编码")
    private String cwbh;

    @ExcelProperty("资产名称")
    private String zcmc;

    @ExcelProperty("规格型号")
    private String ggxh;

    @ExcelProperty("资产部门")
    private Long zcbm;

    @ExcelProperty("资产地点")
    private Long zcdd;

    @ExcelProperty("安装位置")
    private String azwz;

    // @ExcelProperty("SRM系统供应商")
    // private Integer gys;

    // @ExcelProperty("维护商")
    // private Integer whs;

    @ExcelProperty("合同编号")
    private String htbh;

    @ExcelProperty("购入日期")
    private LocalDate grrq;

    @ExcelProperty("数量")
    private BigDecimal zcsl;

    @ExcelProperty("计量单位")
    private Long jldw;

    @ExcelProperty("资产登记日期")
    private LocalDate zcdjrq;

    @ExcelProperty("购入金额(含税)")
    private BigDecimal hsgrje;

    @ExcelProperty("购入金额(不含税)")
    private BigDecimal bhsgrje;

    @ExcelProperty("资产币种")
    private Long zcbz;

    @ExcelProperty("质保期")
    private LocalDate zbq;

    @ExcelProperty("残值率")
    private BigDecimal czl;

    @ExcelProperty("残值")
    private BigDecimal czs;

    @ExcelProperty("当期剩余价值")
    private BigDecimal dqsycz;

    @ExcelProperty("使用年限")
    private Integer synxian;

    @ExcelProperty("报废日期")
    private LocalDate bfrq;

    @ExcelProperty(value = "资产状态", converter = DictConvert.class)
    @DictFormat("assets_status")
    private Integer zczt;

    @ExcelProperty("资产照片")
    private String zczp;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
