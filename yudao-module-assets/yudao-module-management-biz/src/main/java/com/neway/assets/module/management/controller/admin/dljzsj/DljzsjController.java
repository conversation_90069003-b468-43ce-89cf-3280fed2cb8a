package com.neway.assets.module.management.controller.admin.dljzsj;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.*;
import com.neway.assets.module.management.convert.dljzsj.DljzsjConvert;
import com.neway.assets.module.management.dal.dataobject.dljzsj.DljzsjDO;
import com.neway.assets.module.management.service.dljzsj.DljzsjService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 刀/量具主数据")
@RestController
@RequestMapping("/management/dljzsj")
@Validated
public class DljzsjController {

    @Resource
    private DljzsjService dljzsjService;

    @PostMapping("/create")
    @Operation(summary = "创建刀/量具主数据")
    @PreAuthorize("@ss.hasPermission('management:dljzsj:create')")
    public CommonResult<Long> createDljzsj(@Valid @RequestBody DljzsjCreateReqVO createReqVO) {
        return success(dljzsjService.createDljzsj(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新刀/量具主数据")
    @PreAuthorize("@ss.hasPermission('management:dljzsj:update')")
    public CommonResult<Boolean> updateDljzsj(@Valid @RequestBody DljzsjUpdateReqVO updateReqVO) {
        dljzsjService.updateDljzsj(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除刀/量具主数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('management:dljzsj:delete')")
    public CommonResult<Boolean> deleteDljzsj(@RequestParam("id") Long id) {
        dljzsjService.deleteDljzsj(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得刀/量具主数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:dljzsj:query')")
    public CommonResult<DljzsjRespVO> getDljzsj(@RequestParam("id") Long id) {
        DljzsjDO dljzsj = dljzsjService.getDljzsj(id);
        return success(DljzsjConvert.INSTANCE.convert(dljzsj));
    }

    @GetMapping("/list")
    @Operation(summary = "获得刀/量具主数据列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('management:dljzsj:query')")
    public CommonResult<List<DljzsjRespVO>> getDljzsjList(@RequestParam("ids") Collection<Long> ids) {
        List<DljzsjDO> list = dljzsjService.getDljzsjList(ids);
        return success(DljzsjConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得刀/量具主数据分页")
    @PreAuthorize("@ss.hasPermission('management:dljzsj:query')")
    public CommonResult<PageResult<DljzsjRespVO>> getDljzsjPage(@Valid DljzsjPageReqVO pageVO) {
        PageResult<DljzsjDO> pageResult = dljzsjService.getDljzsjPage(pageVO);
        return success(DljzsjConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出刀/量具主数据 Excel")
    @PreAuthorize("@ss.hasPermission('management:dljzsj:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDljzsjExcel(@Valid DljzsjExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<DljzsjDO> list = dljzsjService.getDljzsjList(exportReqVO);
        // 导出 Excel
        List<DljzsjExcelVO> datas = DljzsjConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "刀/量具主数据.xls", "数据", DljzsjExcelVO.class, datas);
    }

}
