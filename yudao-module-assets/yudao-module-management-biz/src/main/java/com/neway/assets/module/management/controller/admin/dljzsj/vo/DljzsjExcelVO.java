package com.neway.assets.module.management.controller.admin.dljzsj.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 刀/量具主数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DljzsjExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty(value = "刀/量具分类（0：量具 1：刀具）", converter = DictConvert.class)
    @DictFormat("dljfl") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer dljfl;

    @ExcelProperty("刀/量具料号")
    private String dljlh;

    @ExcelProperty("刀/量具名称")
    private String dljmc;

    @ExcelProperty("规格型号")
    private String dljgexh;

    @ExcelProperty("校准周期")
    private Integer jzzq;

    @ExcelProperty(value = "使用状态（0：报废，1：使用  2：维修 3：校准）", converter = DictConvert.class)
    @DictFormat("dljsyzt") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer syzt;

    @ExcelProperty("管理部门（组织结构->id）")
    private Long glbm;

    @ExcelProperty("管理人（人员管理->id）")
    private Long glren;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
