package com.neway.assets.module.management.controller.admin.zcrfid.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
* 资产RFID关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcrfidBaseVO {

    @Schema(description = "资产类别", required = true)
    @NotNull(message = "资产类别不能为空")
    private Integer zclb;

    @Schema(description = "资产ID", required = true, example = "24657")
    @NotNull(message = "资产ID不能为空")
    private Long zcid;

    @Schema(description = "开始日期")
    private LocalDate ksrq;

    @Schema(description = "截至日期")
    private LocalDate jzrq;

    @Schema(description = "RFID码")
    private String rfidh;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "发放原因")
    private Integer ffyy;

}
