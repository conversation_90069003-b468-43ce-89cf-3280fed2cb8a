package com.neway.assets.module.management.controller.admin.zzcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* 子资产数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZzcxxBaseVO {

    @Schema(description = "资产主数据", required = true, example = "24853")
    @NotNull(message = "资产主数据不能为空")
    private String zczsjid;

    @Schema(description = "子资产编号", required = true)
    @NotNull(message = "子资产编号不能为空")
    private String zzcbh;

    @Schema(description = "子资产名称", required = true)
    @NotNull(message = "子资产名称不能为空")
    private String zzcmc;

    @Schema(description = "规格型号", required = true)
    @NotNull(message = "规格型号不能为空")
    private String zzcge;

    @Schema(description = "质保期(天)")
    private Integer zzczbq;

    @Schema(description = "数量")
    private BigDecimal zzcsl;

    @Schema(description = "制造商")
    private Integer zzczzs;

    @Schema(description = "子资产状态")
    private Integer zzczt;

    @Schema(description = "资产照片")
    private String zzczp;

    @Schema(description = "备注")
    private String bz;

}
