package com.neway.assets.module.management.dal.dataobject.hbjzsj;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 耗材/备品备件主数据 DO
 *
 * <AUTHOR>
 */
@TableName(value = "mt_hbjzsj", schema = "gdzc")
@KeySequence("mt_hbjzsj_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HbjzsjDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 耗材/备品备件分类
     */
    private Long bjflid;
    /**
     * 物料编号
     */
    private String wlbh;
    /**
     * 物料描述
     */
    private String wlms;
    /**
     * 型号规格
     */
    private String xhge;
    /**
     * 计量单位
     */
    private Long jldw;
    /**
     * 库存领用原则
     */
    private Integer lyyz;
    /**
     * 安全库存数
     */
    private BigDecimal aqkcsl;
    /**
     * 最小采购批量
     */
    private BigDecimal zxcgpl;
    /**
     * 默认仓库
     */
    private Long mrck;
    /**
     * 备注
     */
    private String bz;

}
