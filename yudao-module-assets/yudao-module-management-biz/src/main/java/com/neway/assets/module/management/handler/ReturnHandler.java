package com.neway.assets.module.management.handler;

import com.neway.assets.module.info.enums.enable.AssetsAdjustTypeEnum;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.info.enums.enable.AssetsStatusEnum;
import com.neway.assets.module.management.controller.admin.zczsj.vo.ZczsjUpdateReqVO;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import com.neway.assets.module.management.service.zczsj.ZczsjService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 资产归还处理
 * <AUTHOR>
 * @since 2023/6/7 8:37
 **/
@Component
@RequiredArgsConstructor
public class ReturnHandler implements IAssetsAdjustHandler{

    private final ZczsjService zczsjService;

    @Override
    public AssetsAdjustTypeEnum getAdjustType() {
        return AssetsAdjustTypeEnum.RETURN;
    }

    @Override
    public void handle(ZcydjlDO record) {
        ZczsjUpdateReqVO updateReqVO = (ZczsjUpdateReqVO) new ZczsjUpdateReqVO().setId(record.getZcxxid())
                .setZczt(AssetsStatusEnum.NORMAL.getVal());
        if (Objects.equals(record.getZclb(), AssetsClassEnum.ENTIRE.getVal())) {
            // 更新主资产的使用人和资管部门
            updateReqVO.setZcbm(record.getMbzgbm()).setZcsyr(record.getMbzgy());
        }
        zczsjService.updateById(updateReqVO);
    }
}
