package com.neway.assets.module.management.service.dljzsj;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.dljzsj.DljzsjDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 刀/量具主数据 Service 接口
 *
 * <AUTHOR>
 */
public interface DljzsjService {

    /**
     * 创建刀/量具主数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDljzsj(@Valid DljzsjCreateReqVO createReqVO);

    /**
     * 更新刀/量具主数据
     *
     * @param updateReqVO 更新信息
     */
    void updateDljzsj(@Valid DljzsjUpdateReqVO updateReqVO);

    /**
     * 删除刀/量具主数据
     *
     * @param id 编号
     */
    void deleteDljzsj(Long id);

    /**
     * 获得刀/量具主数据
     *
     * @param id 编号
     * @return 刀/量具主数据
     */
    DljzsjDO getDljzsj(Long id);

    /**
     * 获得刀/量具主数据列表
     *
     * @param ids 编号
     * @return 刀/量具主数据列表
     */
    List<DljzsjDO> getDljzsjList(Collection<Long> ids);

    /**
     * 获得刀/量具主数据分页
     *
     * @param pageReqVO 分页查询
     * @return 刀/量具主数据分页
     */
    PageResult<DljzsjDO> getDljzsjPage(DljzsjPageReqVO pageReqVO);

    /**
     * 获得刀/量具主数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 刀/量具主数据列表
     */
    List<DljzsjDO> getDljzsjList(DljzsjExportReqVO exportReqVO);

}
