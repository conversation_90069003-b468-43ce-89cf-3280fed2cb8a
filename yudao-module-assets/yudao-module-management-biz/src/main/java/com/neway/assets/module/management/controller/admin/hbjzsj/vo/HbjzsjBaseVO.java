package com.neway.assets.module.management.controller.admin.hbjzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* 耗材/备品备件主数据 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HbjzsjBaseVO {

    @Schema(description = "耗材/备品备件分类", required = true, example = "17547")
    @NotNull(message = "耗材/备品备件分类不能为空")
    private Long bjflid;

    @Schema(description = "物料编号", required = true)
    @NotNull(message = "物料编号不能为空")
    private String wlbh;

    @Schema(description = "物料描述", required = true)
    @NotNull(message = "物料描述不能为空")
    private String wlms;

    @Schema(description = "型号规格", required = true)
    @NotNull(message = "型号规格不能为空")
    private String xhge;

    @Schema(description = "计量单位")
    private Long jldw;

    @Schema(description = "库存领用原则")
    private Integer lyyz;

    @Schema(description = "安全库存数")
    private BigDecimal aqkcsl;

    @Schema(description = "最小采购批量")
    private BigDecimal zxcgpl;

    @Schema(description = "默认仓库")
    private Long mrck;

    @Schema(description = "备注")
    private String bz;

}
