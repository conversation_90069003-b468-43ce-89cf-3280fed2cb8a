package com.neway.assets.module.management.dal.mysql.zcydjl;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.management.controller.admin.zcydjl.vo.*;

/**
 * 资产异动记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcydjlMapper extends BaseMapperX<ZcydjlDO> {

    default PageResult<ZcydjlDO> selectPage(ZcydjlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcydjlDO>()
                .eqIfPresent(ZcydjlDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcydjlDO::getZcxxid, reqVO.getZcxxid())
                .eqIfPresent(ZcydjlDO::getYdfs, reqVO.getYdfs())
                .eqIfPresent(ZcydjlDO::getYzgbm, reqVO.getYzgbm())
                .eqIfPresent(ZcydjlDO::getYzgy, reqVO.getYzgy())
                .eqIfPresent(ZcydjlDO::getMbzgbm, reqVO.getMbzgbm())
                .eqIfPresent(ZcydjlDO::getMbzgy, reqVO.getMbzgy())
                .eqIfPresent(ZcydjlDO::getKsrq, reqVO.getKsrq())
                .eqIfPresent(ZcydjlDO::getJzrq, reqVO.getJzrq())
                .eqIfPresent(ZcydjlDO::getYdzt, reqVO.getYdzt())
                .eqIfPresent(ZcydjlDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(ZcydjlDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcydjlDO::getId));
    }

    default List<ZcydjlDO> selectList(ZcydjlExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcydjlDO>()
                .eqIfPresent(ZcydjlDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcydjlDO::getZcxxid, reqVO.getZcxxid())
                .eqIfPresent(ZcydjlDO::getYdfs, reqVO.getYdfs())
                .eqIfPresent(ZcydjlDO::getYzgbm, reqVO.getYzgbm())
                .eqIfPresent(ZcydjlDO::getYzgy, reqVO.getYzgy())
                .eqIfPresent(ZcydjlDO::getMbzgbm, reqVO.getMbzgbm())
                .eqIfPresent(ZcydjlDO::getMbzgy, reqVO.getMbzgy())
                .eqIfPresent(ZcydjlDO::getKsrq, reqVO.getKsrq())
                .eqIfPresent(ZcydjlDO::getJzrq, reqVO.getJzrq())
                .eqIfPresent(ZcydjlDO::getYdzt, reqVO.getYdzt())
                .eqIfPresent(ZcydjlDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(ZcydjlDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcydjlDO::getId));
    }

}
