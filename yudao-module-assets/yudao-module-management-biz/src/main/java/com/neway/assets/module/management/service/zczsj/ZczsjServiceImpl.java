package com.neway.assets.module.management.service.zczsj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.neway.assets.module.info.api.bzxx.BzxxApi;
import com.neway.assets.module.info.api.bzxx.dto.BzxxRespDTO;
import com.neway.assets.module.info.api.gcxx.GcxxApi;
import com.neway.assets.module.info.api.gcxx.dto.GcxxRespDTO;
import com.neway.assets.module.info.api.jldw.JldwApi;
import com.neway.assets.module.info.api.jldw.dto.JldwRespDTO;
import com.neway.assets.module.info.api.secondzcfl.SecondZcflApi;
import com.neway.assets.module.info.api.secondzcfl.dto.SecondZcflRespDTO;
import com.neway.assets.module.info.api.zcfl.ZcflApi;
import com.neway.assets.module.info.api.zcfl.dto.ZcflRespDTO;
import com.neway.assets.module.info.api.zcflZgy.ZcflZgyApi;
import com.neway.assets.module.info.api.zcflZgy.dto.ZcflZgyRespDTO;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.maintenance.api.sbybjl.SbybjlApi;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlCreateReqDTO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidCreateReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidUpdateReqVO;
import com.neway.assets.module.management.controller.admin.zczsj.vo.*;
import com.neway.assets.module.management.convert.zczsj.ZczsjConvert;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import com.neway.assets.module.management.dal.mysql.zczsj.ZczsjMapper;
import com.neway.assets.module.management.service.zcrfid.ZcrfidService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCZSJ_NOT_EXISTS;

/**
 * 资产主数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class ZczsjServiceImpl implements ZczsjService {

    private final ZczsjMapper zczsjMapper;

    private final ZcrfidService zcrfidService;

    private final DeptApi deptApi;
    private final AdminUserApi adminUserApi;

    private final ZcflApi zcflApi;
    private final SecondZcflApi secondZcflApi;
    private final ZcflZgyApi zcflZgyApi;
    private final GcxxApi gcxxApi;
    private final JldwApi jldwApi;
    private final BzxxApi bzxxApi;
    private final SbybjlApi sbybjlApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createZczsj(ZczsjCreateReqVO createReqVO) {
        // 插入主数据
        ZczsjDO zczsj = ZczsjConvert.INSTANCE.convert(createReqVO);
        zczsjMapper.insert(zczsj);
        // RFID
        if (StrUtil.isNotBlank(createReqVO.getRfid())) {
            zcrfidService.createZcrfid((ZcrfidCreateReqVO) new ZcrfidCreateReqVO()
                    .setZcid(zczsj.getId())
                    .setZclb(AssetsClassEnum.ENTIRE.getVal())
                    .setRfidh(createReqVO.getRfid())
                    .setKsrq(LocalDate.now())
                    .setJzrq(createReqVO.getJzrq()));
        }
        // 插入一条质保记录
        SbybjlCreateReqDTO createReqDTO = ZczsjConvert.INSTANCE.convert3(zczsj);
        sbybjlApi.createSbybjl(createReqDTO);
        // 返回
        return zczsj.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateZczsj(ZczsjUpdateReqVO updateReqVO) {
        // 校验存在
        validateZczsjExists(updateReqVO.getId());
        // 更新
        ZczsjDO updateObj = ZczsjConvert.INSTANCE.convert(updateReqVO);
        zczsjMapper.updateById(updateObj);
        // RFID
        zcrfidService.updateRfidByOld(updateReqVO.getRfid(), (ZcrfidUpdateReqVO) new ZcrfidUpdateReqVO()
                .setZcid(updateReqVO.getId())
                .setZclb(AssetsClassEnum.ENTIRE.getVal())
                .setJzrq(updateReqVO.getJzrq()));
    }

    @Override
    public void updateById(ZczsjUpdateReqVO updateReqVO) {
        // 绕过参数校验
        updateZczsj(updateReqVO);
    }

    @Override
    public void deleteZczsj(Long id) {
        // 校验存在
        validateZczsjExists(id);
        // 删除
        zczsjMapper.deleteById(id);
    }

    private void validateZczsjExists(Long id) {
        if (zczsjMapper.selectById(id) == null) {
            throw exception(ZCZSJ_NOT_EXISTS);
        }
    }

    @Override
    public ZczsjDO getZczsj(Long id) {
        return zczsjMapper.selectById(id);
    }

    @Override
    public List<ZczsjDO> getZczsjList(Collection<Long> ids) {
        return zczsjMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZczsjPageItemRespVO> getZczsjPage(ZczsjPageReqVO pageReqVO) {
        PageResult<ZczsjDO> zczsjDOPageResult = zczsjMapper.selectPage(pageReqVO);
        return new PageResult<>(convertPageItemVO(zczsjDOPageResult.getList()), zczsjDOPageResult.getTotal());
    }

    @Override
    public List<ZczsjDO> getZczsjList(ZczsjExportReqVO exportReqVO) {
        return zczsjMapper.selectList(exportReqVO);
    }

    @Override
    public List<ZczsjDO> getZczsjListInCharge() {
        Set<Long> inChargeTypes = zcflZgyApi.getZcflZgyListByUserId(getLoginUserId())
                .stream().map(ZcflZgyRespDTO::getZcflId)
                .collect(Collectors.toSet());
        List<ZczsjDO> assetsList = zczsjMapper.selectList();
        return assetsList.stream().filter(assets -> inChargeTypes.contains(assets.getZcfl())).collect(Collectors.toList());
    }

    @Override
    public List<ZczsjDO> getZczsjListByTypeIds(Collection<Long> typeIds) {
        if (CollUtil.isEmpty(typeIds)) return Collections.emptyList();
        return zczsjMapper.selectList(Wrappers.lambdaQuery(ZczsjDO.class).in(ZczsjDO::getZcfl, typeIds));
    }

    @Override
    public List<ZczsjDO> getAssetsAdminZczsjListByDeptId(Long deptId) {
        if (deptId == null) return Collections.emptyList();
        return getZczsjListInCharge().stream().filter(assets -> Objects.equals(assets.getZcbm(), deptId))
                .collect(Collectors.toList());
    }

    /**
     * 转换DO列表成PageItemVO列表
     * @param source DO列表
     * @return 转换后的PageItemVO列表
     * <AUTHOR>
     * @since 2023/5/8 16:31
     */
    private List<ZczsjPageItemRespVO> convertPageItemVO(List<ZczsjDO> source) {
        if (CollUtil.isEmpty(source)) return Collections.emptyList();
        // 获取基础数据
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(convertSet(source, ZczsjDO::getZcbm));
        Set<Long> userIds = (convertSet(source, ZczsjDO::getZcsyr));
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
        Map<Long, ZcflRespDTO> assetsTypeMap = zcflApi.getZcflMap(convertSet(source, ZczsjDO::getZcfl));
        Map<Long, GcxxRespDTO> locationMap = gcxxApi.getGcxxMap(convertSet(source, ZczsjDO::getZcdd));
        Map<Long, JldwRespDTO> unitMap = jldwApi.getJldwMap(convertSet(source, ZczsjDO::getJldw));
        Map<Long, BzxxRespDTO> currencyMap = bzxxApi.getBzxxMap(convertSet(source, ZczsjDO::getZcbz));
        Map<Long, SecondZcflRespDTO> secondZcflMap = secondZcflApi.getSecondZcflMap(convertSet(source, ZczsjDO::getSecondZcfl));
        Map<Long, ZcrfidDO> rfidMap = zcrfidService.getRfidByZcids(convertSet(source, ZczsjDO::getId), AssetsClassEnum.ENTIRE);
        // 拼接数据
        List<ZczsjPageItemRespVO> result = new ArrayList<>(source.size());
        SecondZcflRespDTO emptySecondZcfl = new SecondZcflRespDTO();
        ZcrfidDO emptyRfid = new ZcrfidDO();
        AdminUserRespDTO emptyUser = new AdminUserRespDTO();
        source.forEach(o -> {
            ZczsjPageItemRespVO respVO = ZczsjConvert.INSTANCE.convert2(o);
            respVO.setDept(deptMap.get(respVO.getZcbm()).getName());
            // respVO.setAdmin(userMap.get(respVO.getZgy()).getNickname());
            respVO.setAssetsType(assetsTypeMap.get(respVO.getZcfl()).getZcflms());
            respVO.setLocation(locationMap.get(respVO.getZcdd()).getGcms());
            respVO.setUnit(unitMap.get(respVO.getJldw()).getJldwjc());
            respVO.setCurrency(currencyMap.get(respVO.getZcbz()).getBzjc());
            respVO.setSecondAssetsType(secondZcflMap.getOrDefault(respVO.getSecondZcfl(), emptySecondZcfl).getZcflms());
            respVO.setRfid(rfidMap.getOrDefault(o.getId(), emptyRfid).getRfidh());
            respVO.setJzrq(rfidMap.getOrDefault(o.getId(), emptyRfid).getJzrq());
            respVO.setUserName(userMap.getOrDefault(o.getZcsyr(), emptyUser).getNickname());
            result.add(respVO);
        });
        return result;
    }

}
