package com.neway.assets.module.management.controller.admin.dljzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 刀/量具主数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DljzsjRespVO extends DljzsjBaseVO {

    @Schema(description = "自增长id", required = true, example = "17541")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
