package com.neway.assets.module.management.dal.mysql.zcrfid;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.*;

/**
 * 资产RFID关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcrfidMapper extends BaseMapperX<ZcrfidDO> {

    default PageResult<ZcrfidDO> selectPage(ZcrfidPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcrfidDO>()
                .eqIfPresent(ZcrfidDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcrfidDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcrfidDO::getZcid, reqVO.getZcid())
                .eqIfPresent(ZcrfidDO::getKsrq, reqVO.getKsrq())
                .eqIfPresent(ZcrfidDO::getJzrq, reqVO.getJzrq())
                .eqIfPresent(ZcrfidDO::getRfidh, reqVO.getRfidh())
                .eqIfPresent(ZcrfidDO::getBz, reqVO.getBz())
                .eqIfPresent(ZcrfidDO::getFfyy, reqVO.getFfyy())
                .betweenIfPresent(ZcrfidDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcrfidDO::getId));
    }

    default List<ZcrfidDO> selectList(ZcrfidExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcrfidDO>()
                .eqIfPresent(ZcrfidDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcrfidDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcrfidDO::getZcid, reqVO.getZcid())
                .eqIfPresent(ZcrfidDO::getKsrq, reqVO.getKsrq())
                .eqIfPresent(ZcrfidDO::getJzrq, reqVO.getJzrq())
                .eqIfPresent(ZcrfidDO::getRfidh, reqVO.getRfidh())
                .eqIfPresent(ZcrfidDO::getBz, reqVO.getBz())
                .eqIfPresent(ZcrfidDO::getFfyy, reqVO.getFfyy())
                .betweenIfPresent(ZcrfidDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcrfidDO::getId));
    }

}
