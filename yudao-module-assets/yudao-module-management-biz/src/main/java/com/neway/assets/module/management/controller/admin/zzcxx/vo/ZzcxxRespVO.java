package com.neway.assets.module.management.controller.admin.zzcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 子资产数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZzcxxRespVO extends ZzcxxBaseVO {

    @Schema(description = "id", required = true, example = "16719")
    private Long id;

    @Schema(description = "RFID")
    private String rfid;

    @Schema(description = "RFID-到期/截止日期")
    private LocalDate jzrq;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
