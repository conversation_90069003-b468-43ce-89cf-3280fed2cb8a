package com.neway.assets.module.management.controller.admin.zcrfid.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产RFID关联 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcrfidRespVO extends ZcrfidBaseVO {

    @Schema(description = "自增长id", required = true, example = "29782")
    private Long id;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
