package com.neway.assets.module.management.convert.zzcxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.management.api.zzcxx.dto.ZzcxxRespDTO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.*;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 子资产数据 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZzcxxConvert {

    ZzcxxConvert INSTANCE = Mappers.getMapper(ZzcxxConvert.class);

    ZzcxxDO convert(ZzcxxCreateReqVO bean);

    ZzcxxDO convert(ZzcxxUpdateReqVO bean);

    ZzcxxRespVO convert(ZzcxxDO bean);

    List<ZzcxxRespVO> convertList(List<ZzcxxDO> list);

    PageResult<ZzcxxRespVO> convertPage(PageResult<ZzcxxDO> page);

    List<ZzcxxExcelVO> convertList02(List<ZzcxxDO> list);

    List<ZzcxxRespDTO> convertList03(List<ZzcxxDO> list);

    List<ZzcxxSimpleRespVO> convertSimpleList(List<ZzcxxDO> list);

}
