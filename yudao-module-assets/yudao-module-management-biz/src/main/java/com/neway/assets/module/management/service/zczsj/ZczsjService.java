package com.neway.assets.module.management.service.zczsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.management.controller.admin.zczsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 资产主数据 Service 接口
 *
 * <AUTHOR>
 */
public interface ZczsjService {

    /**
     * 创建资产主数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZczsj(@Valid ZczsjCreateReqVO createReqVO);

    /**
     * 更新资产主数据
     *
     * @param updateReqVO 更新信息
     */
    void updateZczsj(@Valid ZczsjUpdateReqVO updateReqVO);

    void updateById(ZczsjUpdateReqVO updateReqVO);

    /**
     * 删除资产主数据
     *
     * @param id 编号
     */
    void deleteZczsj(Long id);

    /**
     * 获得资产主数据
     *
     * @param id 编号
     * @return 资产主数据
     */
    ZczsjDO getZczsj(Long id);

    /**
     * 获得资产主数据列表
     *
     * @param ids 编号
     * @return 资产主数据列表
     */
    List<ZczsjDO> getZczsjList(Collection<Long> ids);

    /**
     * 获得资产主数据分页
     *
     * @param pageReqVO 分页查询
     * @return 资产主数据分页
     */
    PageResult<ZczsjPageItemRespVO> getZczsjPage(ZczsjPageReqVO pageReqVO);

    /**
     * 获得资产主数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产主数据列表
     */
    List<ZczsjDO> getZczsjList(ZczsjExportReqVO exportReqVO);

    List<ZczsjDO> getZczsjListInCharge();

    List<ZczsjDO> getZczsjListByTypeIds(Collection<Long> typeIds);

    List<ZczsjDO> getAssetsAdminZczsjListByDeptId(Long deptId);
}
