package com.neway.assets.module.management.service.hbjzsj;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.management.controller.admin.hbjzsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.hbjzsj.HbjzsjDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 耗材/备品备件主数据 Service 接口
 *
 * <AUTHOR>
 */
public interface HbjzsjService {

    /**
     * 创建耗材/备品备件主数据
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHbjzsj(@Valid HbjzsjCreateReqVO createReqVO);

    /**
     * 更新耗材/备品备件主数据
     *
     * @param updateReqVO 更新信息
     */
    void updateHbjzsj(@Valid HbjzsjUpdateReqVO updateReqVO);

    /**
     * 删除耗材/备品备件主数据
     *
     * @param id 编号
     */
    void deleteHbjzsj(Long id);

    /**
     * 获得耗材/备品备件主数据
     *
     * @param id 编号
     * @return 耗材/备品备件主数据
     */
    HbjzsjDO getHbjzsj(Long id);

    /**
     * 获得耗材/备品备件主数据列表
     *
     * @param ids 编号
     * @return 耗材/备品备件主数据列表
     */
    List<HbjzsjDO> getHbjzsjList(Collection<Long> ids);

    /**
     * 获得耗材/备品备件主数据分页
     *
     * @param pageReqVO 分页查询
     * @return 耗材/备品备件主数据分页
     */
    PageResult<HbjzsjPageItemRespVO> getHbjzsjPage(HbjzsjPageReqVO pageReqVO);

    /**
     * 获得耗材/备品备件主数据列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 耗材/备品备件主数据列表
     */
    List<HbjzsjDO> getHbjzsjList(HbjzsjExportReqVO exportReqVO);

}
