package com.neway.assets.module.management.controller.admin.zcydjl;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.management.controller.admin.zcydjl.vo.*;
import com.neway.assets.module.management.convert.zcydjl.ZcydjlConvert;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;
import com.neway.assets.module.management.service.zcydjl.ZcydjlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 资产异动记录")
@RestController
@RequestMapping("/management/zcydjl")
@Validated
public class ZcydjlController {

    @Resource
    private ZcydjlService zcydjlService;

    @PostMapping("/create")
    @Operation(summary = "创建资产异动记录")
    @PreAuthorize("@ss.hasPermission('assets:flow:adjust:create')")
    public CommonResult<Long> createZcydjl(@Valid @RequestBody ZcydjlCreateReqVO createReqVO) {
        return success(zcydjlService.createZcydjl(getLoginUserId(), createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产异动记录")
    @PreAuthorize("@ss.hasPermission('management:zcydjl:update')")
    public CommonResult<Boolean> updateZcydjl(@Valid @RequestBody ZcydjlUpdateReqVO updateReqVO) {
        zcydjlService.updateZcydjl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产异动记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('management:zcydjl:delete')")
    public CommonResult<Boolean> deleteZcydjl(@RequestParam("id") Long id) {
        zcydjlService.deleteZcydjl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产异动记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:zcydjl:query')")
    public CommonResult<ZcydjlRespVO> getZcydjl(@RequestParam("id") Long id) {
        ZcydjlDO zcydjl = zcydjlService.getZcydjl(id);
        return success(ZcydjlConvert.INSTANCE.convert(zcydjl));
    }

    @GetMapping("/get-detail")
    @Operation(summary = "获得资产异动记录详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:zcydjl:query')")
    public CommonResult<ZcydjlPageItemRespVO> getZcydjlDetail(@RequestParam("id") Long id) {
        ZcydjlPageItemRespVO zcydjl = zcydjlService.getZcydjlDetail(id);
        return success(zcydjl);
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产异动记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('management:zcydjl:query')")
    public CommonResult<List<ZcydjlRespVO>> getZcydjlList(@RequestParam("ids") Collection<Long> ids) {
        List<ZcydjlDO> list = zcydjlService.getZcydjlList(ids);
        return success(ZcydjlConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产异动记录分页")
    @PreAuthorize("@ss.hasPermission('management:zcydjl:query')")
    public CommonResult<PageResult<ZcydjlPageItemRespVO>> getZcydjlPage(@Valid ZcydjlPageReqVO pageVO) {
        PageResult<ZcydjlPageItemRespVO> pageResult = zcydjlService.getZcydjlPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产异动记录 Excel")
    @PreAuthorize("@ss.hasPermission('management:zcydjl:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcydjlExcel(@Valid ZcydjlExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcydjlDO> list = zcydjlService.getZcydjlList(exportReqVO);
        // 导出 Excel
        List<ZcydjlExcelVO> datas = ZcydjlConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产异动记录.xls", "数据", ZcydjlExcelVO.class, datas);
    }

}
