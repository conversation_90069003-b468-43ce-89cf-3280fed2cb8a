package com.neway.assets.module.management.controller.admin.dljzsj.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 刀/量具主数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DljzsjPageReqVO extends PageParam {

    @Schema(description = "刀/量具分类（0：量具 1：刀具）")
    private Integer dljfl;

    @Schema(description = "刀/量具料号")
    private String dljlh;

    @Schema(description = "刀/量具名称")
    private String dljmc;

    @Schema(description = "规格型号")
    private String dljgexh;

    @Schema(description = "校准周期")
    private Integer jzzq;

    @Schema(description = "使用状态（0：报废，1：使用  2：维修 3：校准）")
    private Integer syzt;

    @Schema(description = "管理部门（组织结构->id）")
    private Long glbm;

    @Schema(description = "管理人（人员管理->id）")
    private Long glren;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
