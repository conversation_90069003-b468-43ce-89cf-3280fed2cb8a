package com.neway.assets.module.management.service.zcrfid;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidCreateReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidExportReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidPageReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidUpdateReqVO;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 资产RFID关联 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcrfidService {

    /**
     * 创建资产RFID关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcrfid(@Valid ZcrfidCreateReqVO createReqVO);

    /**
     * 更新资产RFID关联
     *
     * @param updateReqVO 更新信息
     */
    void updateZcrfid(@Valid ZcrfidUpdateReqVO updateReqVO);

    /**
     * 通过资产信息更新rfid(如资产无旧RFID则新增)
     * @param newRfid 新rfid
     * @param updateReqVO 资产数据
     * <AUTHOR>
     * @since 2023/7/17 11:08
     */
    void updateRfidByOld(String newRfid, ZcrfidUpdateReqVO updateReqVO);

    /**
     * 删除资产RFID关联
     *
     * @param id 编号
     */
    void deleteZcrfid(Long id);

    /**
     * 获得资产RFID关联
     *
     * @param id 编号
     * @return 资产RFID关联
     */
    ZcrfidDO getZcrfid(Long id);

    /**
     * 获得资产RFID关联列表
     *
     * @param ids 编号
     * @return 资产RFID关联列表
     */
    List<ZcrfidDO> getZcrfidList(Collection<Long> ids);

    /**
     * 获得资产RFID关联分页
     *
     * @param pageReqVO 分页查询
     * @return 资产RFID关联分页
     */
    PageResult<ZcrfidDO> getZcrfidPage(ZcrfidPageReqVO pageReqVO);

    /**
     * 获得资产RFID关联列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产RFID关联列表
     */
    List<ZcrfidDO> getZcrfidList(ZcrfidExportReqVO exportReqVO);

    Map<Long, ZcrfidDO> getRfidByZcids(@Valid @NotNull Collection<Long> zcIds, @Valid @NotNull AssetsClassEnum classEnum);
}
