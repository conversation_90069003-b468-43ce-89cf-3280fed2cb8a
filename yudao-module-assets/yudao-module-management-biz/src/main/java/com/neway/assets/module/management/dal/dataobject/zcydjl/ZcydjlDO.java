package com.neway.assets.module.management.dal.dataobject.zcydjl;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neway.assets.module.info.enums.enable.AssetsAdjustTypeEnum;
import lombok.*;

import java.time.LocalDate;

/**
 * 资产异动记录 DO
 *
 * <AUTHOR>
 */
@TableName(value="op_zcydjl", schema = "gdzc")
@KeySequence("op_zcydjl_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcydjlDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 资产类别
     */
    private Integer zclb;
    /**
     * 资产id
     */
    private Long zcxxid;
    /**
     * 异动方式
     *
     * 枚举 {@link AssetsAdjustTypeEnum}
     */
    private String ydfs;
    /**
     * 原资管部门
     */
    private Long yzgbm;
    /**
     * 原资管员
     */
    private Long yzgy;
    /**
     * 目标资管部门
     */
    private Long mbzgbm;
    /**
     * 目标资管员
     */
    private Long mbzgy;
    /**
     * 开始日期
     */
    private LocalDate ksrq;
    /**
     * 截止日期
     */
    private LocalDate jzrq;
    /**
     * 异动状态
     *
     * 枚举 {@link BpmTaskStatusEnum}
     */
    private Integer ydzt;
    /**
     * 备注
     */
    private String bz;
    /**
     * 流程编号
     */
    private String processInstanceId;

}
