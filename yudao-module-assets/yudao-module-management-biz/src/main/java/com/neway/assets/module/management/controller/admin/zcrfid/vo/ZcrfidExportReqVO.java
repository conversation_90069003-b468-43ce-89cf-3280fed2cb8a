package com.neway.assets.module.management.controller.admin.zcrfid.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产RFID关联 Excel 导出 Request VO，参数和 ZcrfidPageReqVO 是一致的")
@Data
public class ZcrfidExportReqVO {

    @Schema(description = "资产类别")
    private Long zclb;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "资产ID", example = "24657")
    private Long zcid;

    @Schema(description = "开始日期")
    private LocalDate ksrq;

    @Schema(description = "截至日期")
    private LocalDate jzrq;

    @Schema(description = "RFID码")
    private String rfidh;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "发放原因")
    private Integer ffyy;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
