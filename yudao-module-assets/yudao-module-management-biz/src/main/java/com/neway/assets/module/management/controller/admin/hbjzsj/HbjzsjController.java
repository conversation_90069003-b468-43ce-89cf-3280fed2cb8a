package com.neway.assets.module.management.controller.admin.hbjzsj;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.management.controller.admin.hbjzsj.vo.*;
import com.neway.assets.module.management.convert.hbjzsj.HbjzsjConvert;
import com.neway.assets.module.management.dal.dataobject.hbjzsj.HbjzsjDO;
import com.neway.assets.module.management.service.hbjzsj.HbjzsjService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 耗材/备品备件主数据")
@RestController
@RequestMapping("/management/hbjzsj")
@Validated
public class HbjzsjController {

    @Resource
    private HbjzsjService hbjzsjService;

    @PostMapping("/create")
    @Operation(summary = "创建耗材/备品备件主数据")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:create')")
    public CommonResult<Long> createHbjzsj(@Valid @RequestBody HbjzsjCreateReqVO createReqVO) {
        return success(hbjzsjService.createHbjzsj(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新耗材/备品备件主数据")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:update')")
    public CommonResult<Boolean> updateHbjzsj(@Valid @RequestBody HbjzsjUpdateReqVO updateReqVO) {
        hbjzsjService.updateHbjzsj(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除耗材/备品备件主数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:delete')")
    public CommonResult<Boolean> deleteHbjzsj(@RequestParam("id") Long id) {
        hbjzsjService.deleteHbjzsj(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得耗材/备品备件主数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:query')")
    public CommonResult<HbjzsjRespVO> getHbjzsj(@RequestParam("id") Long id) {
        HbjzsjDO hbjzsj = hbjzsjService.getHbjzsj(id);
        return success(HbjzsjConvert.INSTANCE.convert(hbjzsj));
    }

    @GetMapping("/list")
    @Operation(summary = "获得耗材/备品备件主数据列表")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:query')")
    public CommonResult<List<HbjzsjRespVO>> getHbjzsjList(@Valid HbjzsjExportReqVO reqVO) {
        List<HbjzsjDO> list = hbjzsjService.getHbjzsjList(reqVO);
        return success(HbjzsjConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获得耗材/备品备件主数据精简列表")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:query')")
    public CommonResult<List<HbjzsjSimpleRespVO>> getHbjzsjListAllSimple(@Valid HbjzsjExportReqVO exportReqVO) {
        List<HbjzsjDO> list = hbjzsjService.getHbjzsjList(exportReqVO);
        return success(HbjzsjConvert.INSTANCE.convertSimpleList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得耗材/备品备件主数据分页")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:query')")
    public CommonResult<PageResult<HbjzsjPageItemRespVO>> getHbjzsjPage(@Valid HbjzsjPageReqVO pageVO) {
        PageResult<HbjzsjPageItemRespVO> pageResult = hbjzsjService.getHbjzsjPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出耗材/备品备件主数据 Excel")
    @PreAuthorize("@ss.hasPermission('management:hbjzsj:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHbjzsjExcel(@Valid HbjzsjExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HbjzsjDO> list = hbjzsjService.getHbjzsjList(exportReqVO);
        // 导出 Excel
        List<HbjzsjExcelVO> datas = HbjzsjConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "耗材/备品备件主数据.xls", "数据", HbjzsjExcelVO.class, datas);
    }

}
