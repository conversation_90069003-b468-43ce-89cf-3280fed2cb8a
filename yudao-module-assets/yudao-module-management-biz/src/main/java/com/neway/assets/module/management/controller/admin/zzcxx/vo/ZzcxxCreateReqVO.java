package com.neway.assets.module.management.controller.admin.zzcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

@Schema(description = "管理后台 - 子资产数据创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZzcxxCreateReqVO extends ZzcxxBaseVO {

    @Schema(description = "RFID")
    private String rfid;

    @Schema(description = "RFID-到期/截止日期")
    private LocalDate jzrq;

}
