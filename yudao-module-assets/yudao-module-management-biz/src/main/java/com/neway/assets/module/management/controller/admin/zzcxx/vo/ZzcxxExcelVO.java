package com.neway.assets.module.management.controller.admin.zzcxx.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 子资产数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZzcxxExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("资产主数据")
    private String zczsjid;

    @ExcelProperty("子资产编号")
    private String zzcbh;

    @ExcelProperty("子资产名称")
    private String zzcmc;

    @ExcelProperty("规格型号")
    private String zzcge;

    @ExcelProperty("质保期(天)")
    private Integer zzczbq;

    @ExcelProperty("数量")
    private BigDecimal zzcsl;

    @ExcelProperty("制造商")
    private Integer zzczzs;

    @ExcelProperty(value = "子资产状态", converter = DictConvert.class)
    @DictFormat("assets_status")
    private Integer zzczt;

    @ExcelProperty("资产照片")
    private String zzczp;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
