package com.neway.assets.module.management.service.zcydjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.management.controller.admin.zcydjl.vo.*;
import com.neway.assets.module.management.dal.dataobject.zcydjl.ZcydjlDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 资产异动记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcydjlService {

    /**
     * 创建资产异动记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcydjl(@Valid ZcydjlCreateReqVO createReqVO);

    Long createZcydjl(Long userId, @Valid ZcydjlCreateReqVO createReqVO);

    /**
     * 更新资产异动记录
     *
     * @param updateReqVO 更新信息
     */
    void updateZcydjl(@Valid ZcydjlUpdateReqVO updateReqVO);

    /**
     * 更新异动状态
     * @param id 记录ID
     * @param result 操作结果
     * <AUTHOR>
     * @since 2023/5/17 16:50
     */
    void updateZcydjlStatus(Long id, Integer result);

    /**
     * 删除资产异动记录
     *
     * @param id 编号
     */
    void deleteZcydjl(Long id);

    /**
     * 获得资产异动记录
     *
     * @param id 编号
     * @return 资产异动记录
     */
    ZcydjlDO getZcydjl(Long id);

    ZcydjlPageItemRespVO getZcydjlDetail(Long id);

    /**
     * 获得资产异动记录列表
     *
     * @param ids 编号
     * @return 资产异动记录列表
     */
    List<ZcydjlDO> getZcydjlList(Collection<Long> ids);

    /**
     * 获得资产异动记录分页
     *
     * @param pageReqVO 分页查询
     * @return 资产异动记录分页
     */
    PageResult<ZcydjlPageItemRespVO> getZcydjlPage(ZcydjlPageReqVO pageReqVO);

    /**
     * 获得资产异动记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产异动记录列表
     */
    List<ZcydjlDO> getZcydjlList(ZcydjlExportReqVO exportReqVO);

    void updateZcydjlResult(Long id, Integer result);

}
