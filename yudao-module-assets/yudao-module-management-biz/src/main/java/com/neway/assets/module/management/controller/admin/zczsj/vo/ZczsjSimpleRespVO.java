package com.neway.assets.module.management.controller.admin.zczsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/5/29 15:30
 **/
@Schema(description = "管理后台 - 资产主数据精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZczsjSimpleRespVO {

    private Long id;

    private String zcbh;

    private String zcmc;
}
