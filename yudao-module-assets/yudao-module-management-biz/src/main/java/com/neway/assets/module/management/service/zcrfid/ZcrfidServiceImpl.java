package com.neway.assets.module.management.service.zcrfid;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidCreateReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidExportReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidPageReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidUpdateReqVO;
import com.neway.assets.module.management.convert.zcrfid.ZcrfidConvert;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;
import com.neway.assets.module.management.dal.mysql.zcrfid.ZcrfidMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.NEW_RFID_EXISTS;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCRFID_NOT_EXISTS;

/**
 * 资产RFID关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZcrfidServiceImpl implements ZcrfidService {

    @Resource
    private ZcrfidMapper zcrfidMapper;

    @Override
    public Long createZcrfid(ZcrfidCreateReqVO createReqVO) {
        // 插入
        ZcrfidDO zcrfid = ZcrfidConvert.INSTANCE.convert(createReqVO);
        zcrfidMapper.insert(zcrfid);
        // 返回
        return zcrfid.getId();
    }

    @Override
    public void updateZcrfid(ZcrfidUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcrfidExists(updateReqVO.getId());
        // 更新
        ZcrfidDO updateObj = ZcrfidConvert.INSTANCE.convert(updateReqVO);
        zcrfidMapper.updateById(updateObj);
    }


    @Override
    public void updateRfidByOld(String newRfid, ZcrfidUpdateReqVO updateReqVO) {
        ZcrfidDO zcrfidDO = zcrfidMapper.selectOne(Wrappers.lambdaQuery(ZcrfidDO.class)
                .eq(ZcrfidDO::getZcid, updateReqVO.getZcid())
                .eq(ZcrfidDO::getZclb, updateReqVO.getZclb()));
        if (Objects.isNull(zcrfidDO) || !Objects.equals(newRfid, zcrfidDO.getRfidh())) {
            if (validateRfidExists(newRfid)) throw exception(NEW_RFID_EXISTS);
            if (Objects.nonNull(zcrfidDO)) {
                zcrfidDO.setRfidh(newRfid);
                zcrfidDO.setJzrq(updateReqVO.getJzrq());
                zcrfidMapper.updateById(zcrfidDO);
            } else {
                ZcrfidDO insert = ZcrfidConvert.INSTANCE.convert(updateReqVO);
                insert.setRfidh(newRfid).setKsrq(LocalDate.now());
                zcrfidMapper.insert(insert);
            }
        }
    }

    @Override
    public void deleteZcrfid(Long id) {
        // 校验存在
        validateZcrfidExists(id);
        // 删除
        zcrfidMapper.deleteById(id);
    }

    private void validateZcrfidExists(Long id) {
        if (zcrfidMapper.selectById(id) == null) {
            throw exception(ZCRFID_NOT_EXISTS);
        }
    }

    private boolean validateRfidExists(String rfid) {
        return !zcrfidMapper.selectList(new ZcrfidExportReqVO().setRfidh(rfid)).isEmpty();
    }

    @Override
    public ZcrfidDO getZcrfid(Long id) {
        return zcrfidMapper.selectById(id);
    }

    @Override
    public List<ZcrfidDO> getZcrfidList(Collection<Long> ids) {
        return zcrfidMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcrfidDO> getZcrfidPage(ZcrfidPageReqVO pageReqVO) {
        return zcrfidMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZcrfidDO> getZcrfidList(ZcrfidExportReqVO exportReqVO) {
        return zcrfidMapper.selectList(exportReqVO);
    }

    @Override
    public Map<Long, ZcrfidDO> getRfidByZcids(Collection<Long> zcIds, AssetsClassEnum classEnum) {
        List<ZcrfidDO> list = zcrfidMapper.selectList(Wrappers.lambdaQuery(ZcrfidDO.class)
                .eq(ZcrfidDO::getZclb, classEnum.getVal())
                .in(ZcrfidDO::getZcid, zcIds));
        return convertMap(list, ZcrfidDO::getZcid);

    }
}
