package com.neway.assets.module.management.controller.admin.zcydjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产异动记录 Excel 导出 Request VO，参数和 ZcydjlPageReqVO 是一致的")
@Data
public class ZcydjlExportReqVO {

    @Schema(description = "资产类别")
    private Integer zclb;

    @Schema(description = "资产id", example = "12245")
    private Long zcxxid;

    @Schema(description = "异动方式")
    private String ydfs;

    @Schema(description = "原资管部门")
    private Long yzgbm;

    @Schema(description = "原资管员")
    private Long yzgy;

    @Schema(description = "目标资管部门")
    private Long mbzgbm;

    @Schema(description = "目标资管员")
    private Long mbzgy;

    @Schema(description = "开始日期")
    private LocalDate ksrq;

    @Schema(description = "截止日期")
    private LocalDate jzrq;

    @Schema(description = "异动状态")
    private Integer ydzt;

    @Schema(description = "流程编号")
    private String processInstanceId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
