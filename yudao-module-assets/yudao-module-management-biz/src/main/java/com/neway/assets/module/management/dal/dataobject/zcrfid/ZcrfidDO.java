package com.neway.assets.module.management.dal.dataobject.zcrfid;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;

/**
 * 资产RFID关联 DO
 *
 * <AUTHOR>
 */
@TableName(value = "mt_zcrfid", schema = "gdzc")
@KeySequence("mt_zcrfid_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcrfidDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 资产类别
     */
    private Integer zclb;
    /**
     * 启用标识
     *
     * 枚举 {@link TODO assets_qybs_status 对应的类}
     */
    private Integer qybs;
    /**
     * 资产ID
     */
    private Long zcid;
    /**
     * 开始日期
     */
    private LocalDate ksrq;
    /**
     * 截至日期
     */
    private LocalDate jzrq;
    /**
     * RFID码
     */
    private String rfidh;
    /**
     * 备注
     */
    private String bz;
    /**
     * 发放原因
     *
     * 枚举 {@link TODO assets_rfid_reason 对应的类}
     */
    private Integer ffyy;

}
