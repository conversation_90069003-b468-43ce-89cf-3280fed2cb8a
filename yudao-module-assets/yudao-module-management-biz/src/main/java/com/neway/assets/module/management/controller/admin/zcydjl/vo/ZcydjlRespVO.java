package com.neway.assets.module.management.controller.admin.zcydjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产异动记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcydjlRespVO extends ZcydjlBaseVO {

    @Schema(description = "自增长id", required = true, example = "29988")
    private Long id;

    @Schema(description = "异动状态")
    private Integer ydzt;

    @Schema(description = "流程编号")
    private String processInstanceId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
