package com.neway.assets.module.management.controller.admin.zzcxx;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.*;
import com.neway.assets.module.management.convert.zzcxx.ZzcxxConvert;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import com.neway.assets.module.management.service.zcrfid.ZcrfidService;
import com.neway.assets.module.management.service.zzcxx.ZzcxxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.isAnyEmpty;

@Tag(name = "管理后台 - 子资产数据")
@RestController
@RequestMapping("/management/zzcxx")
@Validated
public class ZzcxxController {

    @Resource
    private ZzcxxService zzcxxService;

    @Resource
    private ZcrfidService zcrfidService;

    @PostMapping("/create")
    @Operation(summary = "创建子资产数据")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:create')")
    public CommonResult<Long> createZzcxx(@Valid @RequestBody ZzcxxCreateReqVO createReqVO) {
        return success(zzcxxService.createZzcxx(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新子资产数据")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:update')")
    public CommonResult<Boolean> updateZzcxx(@Valid @RequestBody ZzcxxUpdateReqVO updateReqVO) {
        zzcxxService.updateZzcxx(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除子资产数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('management:zzcxx:delete')")
    public CommonResult<Boolean> deleteZzcxx(@RequestParam("id") Long id) {
        zzcxxService.deleteZzcxx(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得子资产数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:query')")
    public CommonResult<ZzcxxRespVO> getZzcxx(@RequestParam("id") Long id) {
        ZzcxxDO zzcxx = zzcxxService.getZzcxx(id);
        return success(ZzcxxConvert.INSTANCE.convert(zzcxx));
    }

    @GetMapping("/list")
    @Operation(summary = "获得子资产数据列表")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:query')")
    public CommonResult<List<ZzcxxRespVO>> getZzcxxList(@Valid ZzcxxExportReqVO reqVO) {
        List<ZzcxxDO> list = zzcxxService.getZzcxxList(reqVO);
        return success(ZzcxxConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获得子资产精简数据列表")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:query')")
    public CommonResult<List<ZzcxxSimpleRespVO>> getZzcxxListAllSimple(@Valid ZzcxxExportReqVO reqVO) {
        List<ZzcxxDO> list = zzcxxService.getZzcxxList(reqVO);
        return success(ZzcxxConvert.INSTANCE.convertSimpleList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得子资产数据分页")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:query')")
    public CommonResult<PageResult<ZzcxxRespVO>> getZzcxxPage(@Valid ZzcxxPageReqVO pageVO) {
        PageResult<ZzcxxRespVO> pageResult = ZzcxxConvert.INSTANCE.convertPage(zzcxxService.getZzcxxPage(pageVO));
        List<ZzcxxRespVO> list = pageResult.getList();
        if (isAnyEmpty(list)) return success(pageResult);
        // 获取基础数据
        Map<Long, ZcrfidDO> rfidMap = zcrfidService.getRfidByZcids(convertSet(list, ZzcxxRespVO::getId), AssetsClassEnum.PART);
        // 补充RFID数据
        ZcrfidDO emptyRfid = new ZcrfidDO();
        list.forEach(o -> {
            ZcrfidDO rfid = rfidMap.getOrDefault(o.getId(), emptyRfid);
            o.setRfid(rfid.getRfidh());
            o.setJzrq(rfid.getJzrq());
        });
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出子资产数据 Excel")
    @PreAuthorize("@ss.hasPermission('management:zzcxx:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZzcxxExcel(@Valid ZzcxxExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<ZzcxxDO> list = zzcxxService.getZzcxxList(exportReqVO);
        // 导出 Excel
        List<ZzcxxExcelVO> datas = ZzcxxConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "子资产数据.xls", "数据", ZzcxxExcelVO.class, datas);
    }

}
