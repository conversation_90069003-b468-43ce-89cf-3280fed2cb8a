package com.neway.assets.module.management.service.dljzsj;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.management.controller.admin.dljzsj.vo.*;
import com.neway.assets.module.management.dal.dataobject.dljzsj.DljzsjDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.management.convert.dljzsj.DljzsjConvert;
import com.neway.assets.module.management.dal.mysql.dljzsj.DljzsjMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.*;

/**
 * 刀/量具主数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DljzsjServiceImpl implements DljzsjService {

    @Resource
    private DljzsjMapper dljzsjMapper;

    @Override
    public Long createDljzsj(DljzsjCreateReqVO createReqVO) {
        // 插入
        DljzsjDO dljzsj = DljzsjConvert.INSTANCE.convert(createReqVO);
        dljzsjMapper.insert(dljzsj);
        // 返回
        return dljzsj.getId();
    }

    @Override
    public void updateDljzsj(DljzsjUpdateReqVO updateReqVO) {
        // 校验存在
        validateDljzsjExists(updateReqVO.getId());
        // 更新
        DljzsjDO updateObj = DljzsjConvert.INSTANCE.convert(updateReqVO);
        dljzsjMapper.updateById(updateObj);
    }

    @Override
    public void deleteDljzsj(Long id) {
        // 校验存在
        validateDljzsjExists(id);
        // 删除
        dljzsjMapper.deleteById(id);
    }

    private void validateDljzsjExists(Long id) {
        if (dljzsjMapper.selectById(id) == null) {
            throw exception(DLJZSJ_NOT_EXISTS);
        }
    }

    @Override
    public DljzsjDO getDljzsj(Long id) {
        return dljzsjMapper.selectById(id);
    }

    @Override
    public List<DljzsjDO> getDljzsjList(Collection<Long> ids) {
        return dljzsjMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DljzsjDO> getDljzsjPage(DljzsjPageReqVO pageReqVO) {
        return dljzsjMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DljzsjDO> getDljzsjList(DljzsjExportReqVO exportReqVO) {
        return dljzsjMapper.selectList(exportReqVO);
    }

}
