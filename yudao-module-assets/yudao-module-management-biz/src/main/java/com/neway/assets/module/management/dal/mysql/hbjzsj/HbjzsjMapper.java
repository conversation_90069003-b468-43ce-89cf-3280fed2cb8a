package com.neway.assets.module.management.dal.mysql.hbjzsj;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.management.dal.dataobject.hbjzsj.HbjzsjDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.management.controller.admin.hbjzsj.vo.*;

/**
 * 耗材/备品备件主数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HbjzsjMapper extends BaseMapperX<HbjzsjDO> {

    default PageResult<HbjzsjDO> selectPage(HbjzsjPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HbjzsjDO>()
                .eqIfPresent(HbjzsjDO::getBjflid, reqVO.getBjflid())
                .eqIfPresent(HbjzsjDO::getWlbh, reqVO.getWlbh())
                .eqIfPresent(HbjzsjDO::getWlms, reqVO.getWlms())
                .eqIfPresent(HbjzsjDO::getXhge, reqVO.getXhge())
                .eqIfPresent(HbjzsjDO::getJldw, reqVO.getJldw())
                .eqIfPresent(HbjzsjDO::getLyyz, reqVO.getLyyz())
                .eqIfPresent(HbjzsjDO::getAqkcsl, reqVO.getAqkcsl())
                .eqIfPresent(HbjzsjDO::getZxcgpl, reqVO.getZxcgpl())
                .eqIfPresent(HbjzsjDO::getMrck, reqVO.getMrck())
                .eqIfPresent(HbjzsjDO::getBz, reqVO.getBz())
                .betweenIfPresent(HbjzsjDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HbjzsjDO::getId));
    }

    default List<HbjzsjDO> selectList(HbjzsjExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HbjzsjDO>()
                .eqIfPresent(HbjzsjDO::getBjflid, reqVO.getBjflid())
                .eqIfPresent(HbjzsjDO::getWlbh, reqVO.getWlbh())
                .eqIfPresent(HbjzsjDO::getWlms, reqVO.getWlms())
                .eqIfPresent(HbjzsjDO::getXhge, reqVO.getXhge())
                .eqIfPresent(HbjzsjDO::getJldw, reqVO.getJldw())
                .eqIfPresent(HbjzsjDO::getLyyz, reqVO.getLyyz())
                .eqIfPresent(HbjzsjDO::getAqkcsl, reqVO.getAqkcsl())
                .eqIfPresent(HbjzsjDO::getZxcgpl, reqVO.getZxcgpl())
                .eqIfPresent(HbjzsjDO::getMrck, reqVO.getMrck())
                .eqIfPresent(HbjzsjDO::getBz, reqVO.getBz())
                .betweenIfPresent(HbjzsjDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HbjzsjDO::getId));
    }

}
