package com.neway.assets.module.management.convert.zcrfid;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.*;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;

/**
 * 资产RFID关联 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcrfidConvert {

    ZcrfidConvert INSTANCE = Mappers.getMapper(ZcrfidConvert.class);

    ZcrfidDO convert(ZcrfidCreateReqVO bean);

    ZcrfidDO convert(ZcrfidUpdateReqVO bean);

    ZcrfidRespVO convert(ZcrfidDO bean);

    List<ZcrfidRespVO> convertList(List<ZcrfidDO> list);

    PageResult<ZcrfidRespVO> convertPage(PageResult<ZcrfidDO> page);

    List<ZcrfidExcelVO> convertList02(List<ZcrfidDO> list);

}
