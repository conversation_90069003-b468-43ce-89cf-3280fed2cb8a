package com.neway.assets.module.management.controller.admin.zczsj;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.management.controller.admin.zczsj.vo.*;
import com.neway.assets.module.management.convert.zczsj.ZczsjConvert;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import com.neway.assets.module.management.service.zczsj.ZczsjService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产主数据")
@RestController
@RequestMapping("/management/zczsj")
@Validated
public class ZczsjController {

    @Resource
    private ZczsjService zczsjService;

    @PostMapping("/create")
    @Operation(summary = "创建资产主数据")
    @PreAuthorize("@ss.hasPermission('management:zczsj:create')")
    public CommonResult<Long> createZczsj(@Valid @RequestBody ZczsjCreateReqVO createReqVO) {
        return success(zczsjService.createZczsj(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产主数据")
    @PreAuthorize("@ss.hasPermission('management:zczsj:update')")
    public CommonResult<Boolean> updateZczsj(@Valid @RequestBody ZczsjUpdateReqVO updateReqVO) {
        zczsjService.updateZczsj(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产主数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('management:zczsj:delete')")
    public CommonResult<Boolean> deleteZczsj(@RequestParam("id") Long id) {
        zczsjService.deleteZczsj(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产主数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:zczsj:query')")
    public CommonResult<ZczsjRespVO> getZczsj(@RequestParam("id") Long id) {
        ZczsjDO zczsj = zczsjService.getZczsj(id);
        return success(ZczsjConvert.INSTANCE.convert(zczsj));
    }

    @GetMapping("/list-dept")
    @Operation(summary = "获取资管员所管分类下的指定部门的资产列表")
    @PreAuthorize("@ss.hasPermission('management:zczsj:query')")
    public CommonResult<List<ZczsjRespVO>> getHandleZczsjListByDeptId(Long deptId) {
        List<ZczsjDO> list = zczsjService.getAssetsAdminZczsjListByDeptId(deptId);
        return success(ZczsjConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获得资产主数据精简信息列表")
    @PreAuthorize("@ss.hasPermission('management:zczsj:query')")
    public CommonResult<List<ZczsjSimpleRespVO>> getZczsjListAllSimple(@Valid ZczsjExportReqVO exportReqVO) {
        List<ZczsjDO> list = zczsjService.getZczsjList(exportReqVO);
        return success(ZczsjConvert.INSTANCE.convertSimpleList(list));
    }

    @GetMapping("/list-in-charge")
    @Operation(summary = "获得当前用户所负责的资产列表")
    @PreAuthorize("@ss.hasPermission('management:zczsj:query,inventory:zcpdjh:create')")
    public CommonResult<List<ZczsjRespVO>> getZczsjListInCharge() {
        List<ZczsjDO> list = zczsjService.getZczsjListInCharge();
        return success(ZczsjConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产主数据分页")
    @PreAuthorize("@ss.hasPermission('management:zczsj:query')")
    public CommonResult<PageResult<ZczsjPageItemRespVO>> getZczsjPage(@Valid ZczsjPageReqVO pageVO) {
        PageResult<ZczsjPageItemRespVO> pageResult = zczsjService.getZczsjPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产主数据 Excel")
    @PreAuthorize("@ss.hasPermission('management:zczsj:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZczsjExcel(@Valid ZczsjExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<ZczsjDO> list = zczsjService.getZczsjList(exportReqVO);
        // 导出 Excel
        List<ZczsjExcelVO> datas = ZczsjConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产主数据.xls", "数据", ZczsjExcelVO.class, datas);
    }

}
