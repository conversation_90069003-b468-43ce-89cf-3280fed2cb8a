package com.neway.assets.module.management.controller.admin.hbjzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2023/6/12 15:55
 **/
@Schema(description = "管理后台 - 耗材备品备件主数据精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HbjzsjSimpleRespVO {
    @Schema(description = "自增长id", example = "18967")
    private Long id;
    @Schema(description = "物料编号")
    private String wlbh;
    @Schema(description = "物料描述")
    private String wlms;
}
