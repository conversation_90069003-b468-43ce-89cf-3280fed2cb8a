package com.neway.assets.module.management.dal.mysql.zczsj;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.management.controller.admin.zczsj.vo.ZczsjExportReqVO;
import com.neway.assets.module.management.controller.admin.zczsj.vo.ZczsjPageReqVO;
import com.neway.assets.module.management.dal.dataobject.zczsj.ZczsjDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产主数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZczsjMapper extends BaseMapperX<ZczsjDO> {

    default PageResult<ZczsjDO> selectPage(ZczsjPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZczsjDO>()
                .eqIfPresent(ZczsjDO::getZcfl, reqVO.getZcfl())
                .likeIfPresent(ZczsjDO::getZcbh, reqVO.getZcbh())
                .eqIfPresent(ZczsjDO::getCwbh, reqVO.getCwbh())
                .likeIfPresent(ZczsjDO::getZcmc, reqVO.getZcmc())
                .eqIfPresent(ZczsjDO::getGgxh, reqVO.getGgxh())
                .eqIfPresent(ZczsjDO::getZcbm, reqVO.getZcbm())
                .eqIfPresent(ZczsjDO::getZcdd, reqVO.getZcdd())
                .eqIfPresent(ZczsjDO::getGys, reqVO.getGys())
                .eqIfPresent(ZczsjDO::getWhs, reqVO.getWhs())
                .eqIfPresent(ZczsjDO::getHtbh, reqVO.getHtbh())
                .betweenIfPresent(ZczsjDO::getGrrq, reqVO.getGrrq())
                .eqIfPresent(ZczsjDO::getZcsl, reqVO.getZcsl())
                .eqIfPresent(ZczsjDO::getJldw, reqVO.getJldw())
                .betweenIfPresent(ZczsjDO::getZcdjrq, reqVO.getZcdjrq())
                .eqIfPresent(ZczsjDO::getHsgrje, reqVO.getHsgrje())
                .eqIfPresent(ZczsjDO::getBhsgrje, reqVO.getBhsgrje())
                .eqIfPresent(ZczsjDO::getZcbz, reqVO.getZcbz())
                .eqIfPresent(ZczsjDO::getZbq, reqVO.getZbq())
                .eqIfPresent(ZczsjDO::getCzl, reqVO.getCzl())
                .eqIfPresent(ZczsjDO::getCzs, reqVO.getCzs())
                .eqIfPresent(ZczsjDO::getDqsycz, reqVO.getDqsycz())
                .eqIfPresent(ZczsjDO::getSynxian, reqVO.getSynxian())
                .eqIfPresent(ZczsjDO::getBfrq, reqVO.getBfrq())
                .eqIfPresent(ZczsjDO::getZczt, reqVO.getZczt())
                .eqIfPresent(ZczsjDO::getZczp, reqVO.getZczp())
                .eqIfPresent(ZczsjDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZczsjDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZczsjDO::getId));
    }

    default List<ZczsjDO> selectList(ZczsjExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZczsjDO>()
                .eqIfPresent(ZczsjDO::getZcfl, reqVO.getZcfl())
                .likeIfPresent(ZczsjDO::getZcbh, reqVO.getZcbh())
                .eqIfPresent(ZczsjDO::getCwbh, reqVO.getCwbh())
                .likeIfPresent(ZczsjDO::getZcmc, reqVO.getZcmc())
                .eqIfPresent(ZczsjDO::getGgxh, reqVO.getGgxh())
                .eqIfPresent(ZczsjDO::getZcbm, reqVO.getZcbm())
                .eqIfPresent(ZczsjDO::getZcdd, reqVO.getZcdd())
                .eqIfPresent(ZczsjDO::getGys, reqVO.getGys())
                .eqIfPresent(ZczsjDO::getWhs, reqVO.getWhs())
                .eqIfPresent(ZczsjDO::getHtbh, reqVO.getHtbh())
                .betweenIfPresent(ZczsjDO::getGrrq, reqVO.getGrrq())
                .eqIfPresent(ZczsjDO::getZcsl, reqVO.getZcsl())
                .eqIfPresent(ZczsjDO::getJldw, reqVO.getJldw())
                .betweenIfPresent(ZczsjDO::getZcdjrq, reqVO.getZcdjrq())
                .eqIfPresent(ZczsjDO::getHsgrje, reqVO.getHsgrje())
                .eqIfPresent(ZczsjDO::getBhsgrje, reqVO.getBhsgrje())
                .eqIfPresent(ZczsjDO::getZcbz, reqVO.getZcbz())
                .eqIfPresent(ZczsjDO::getZbq, reqVO.getZbq())
                .eqIfPresent(ZczsjDO::getCzl, reqVO.getCzl())
                .eqIfPresent(ZczsjDO::getCzs, reqVO.getCzs())
                .eqIfPresent(ZczsjDO::getDqsycz, reqVO.getDqsycz())
                .eqIfPresent(ZczsjDO::getSynxian, reqVO.getSynxian())
                .eqIfPresent(ZczsjDO::getBfrq, reqVO.getBfrq())
                .eqIfPresent(ZczsjDO::getZczt, reqVO.getZczt())
                .eqIfPresent(ZczsjDO::getZczp, reqVO.getZczp())
                .eqIfPresent(ZczsjDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZczsjDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZczsjDO::getId));
    }

}
