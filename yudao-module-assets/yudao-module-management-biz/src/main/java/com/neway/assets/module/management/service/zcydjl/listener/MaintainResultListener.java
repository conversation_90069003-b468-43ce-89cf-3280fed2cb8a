package com.neway.assets.module.management.service.zcydjl.listener;

import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEventListener;
import com.neway.assets.module.info.enums.enable.AssetsAdjustTypeEnum;
import com.neway.assets.module.management.service.zcydjl.ZcydjlService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 维修流程结果监听
 * <AUTHOR>
 * @since 2023/6/9 15:13
 **/
@Component
@RequiredArgsConstructor
public class MaintainResultListener extends BpmProcessInstanceStatusEventListener {

    private final ZcydjlService zcydjlService;

    @Override
    protected String getProcessDefinitionKey() {
        return AssetsAdjustTypeEnum.MAINTAIN.name();
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        zcydjlService.updateZcydjlResult(Long.parseLong(event.getBusinessKey()), event.getStatus());
    }
}
