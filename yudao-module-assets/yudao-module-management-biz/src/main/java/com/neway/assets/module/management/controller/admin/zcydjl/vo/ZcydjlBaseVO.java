package com.neway.assets.module.management.controller.admin.zcydjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
* 资产异动记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcydjlBaseVO {

    @Schema(description = "资产类别", required = true)
    @NotNull(message = "资产类别不能为空")
    private Integer zclb;

    @Schema(description = "资产id", required = true, example = "12245")
    @NotNull(message = "资产id不能为空")
    private Long zcxxid;

    @Schema(description = "异动方式")
    private String ydfs;

    @Schema(description = "原资管部门")
    private Long yzgbm;

    @Schema(description = "原资管员")
    private Long yzgy;

    @Schema(description = "目标资管部门")
    private Long mbzgbm;

    @Schema(description = "目标资管员")
    private Long mbzgy;

    @Schema(description = "开始日期")
    private LocalDate ksrq;

    @Schema(description = "截止日期")
    private LocalDate jzrq;

    @Schema(description = "备注")
    private String bz;

}
