package com.neway.assets.module.management.controller.admin.dljzsj.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 刀/量具主数据更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DljzsjUpdateReqVO extends DljzsjBaseVO {

    @Schema(description = "自增长id", required = true, example = "17541")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
