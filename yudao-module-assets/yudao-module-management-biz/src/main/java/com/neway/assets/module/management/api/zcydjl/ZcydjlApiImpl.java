package com.neway.assets.module.management.api.zcydjl;

import com.neway.assets.module.management.api.zcydjl.dto.ZcydjlReqDTO;
import com.neway.assets.module.management.convert.zcydjl.ZcydjlConvert;
import com.neway.assets.module.management.service.zcydjl.ZcydjlService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @since 2023/10/24 8:51
 **/
@RequiredArgsConstructor
@Service
public class ZcydjlApiImpl implements ZcydjlApi{

    private final ZcydjlService zcydjlService;

    @Override
    public Long createZcydjlProcessInstance(Long userId, ZcydjlReqDTO reqDTO) {
        return zcydjlService.createZcydjl(userId, ZcydjlConvert.INSTANCE.convert(reqDTO));

    }
}
