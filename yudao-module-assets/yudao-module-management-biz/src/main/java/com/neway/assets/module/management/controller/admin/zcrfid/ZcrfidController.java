package com.neway.assets.module.management.controller.admin.zcrfid;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidCreateReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidPageReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidRespVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidUpdateReqVO;
import com.neway.assets.module.management.convert.zcrfid.ZcrfidConvert;
import com.neway.assets.module.management.dal.dataobject.zcrfid.ZcrfidDO;
import com.neway.assets.module.management.service.zcrfid.ZcrfidService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产RFID关联")
@RestController
@RequestMapping("/management/zcrfid")
@Validated
public class ZcrfidController {

    @Resource
    private ZcrfidService zcrfidService;

    @PostMapping("/create")
    @Operation(summary = "创建资产RFID关联")
    @PreAuthorize("@ss.hasPermission('management:zcrfid:create')")
    public CommonResult<Long> createZcrfid(@Valid @RequestBody ZcrfidCreateReqVO createReqVO) {
        return success(zcrfidService.createZcrfid(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产RFID关联")
    @PreAuthorize("@ss.hasPermission('management:zcrfid:update')")
    public CommonResult<Boolean> updateZcrfid(@Valid @RequestBody ZcrfidUpdateReqVO updateReqVO) {
        zcrfidService.updateZcrfid(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产RFID关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('management:zcrfid:delete')")
    public CommonResult<Boolean> deleteZcrfid(@RequestParam("id") Long id) {
        zcrfidService.deleteZcrfid(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产RFID关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('management:zcrfid:query')")
    public CommonResult<ZcrfidRespVO> getZcrfid(@RequestParam("id") Long id) {
        ZcrfidDO zcrfid = zcrfidService.getZcrfid(id);
        return success(ZcrfidConvert.INSTANCE.convert(zcrfid));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产RFID关联列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('management:zcrfid:query')")
    public CommonResult<List<ZcrfidRespVO>> getZcrfidList(@RequestParam("ids") Collection<Long> ids) {
        List<ZcrfidDO> list = zcrfidService.getZcrfidList(ids);
        return success(ZcrfidConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产RFID关联分页")
    @PreAuthorize("@ss.hasPermission('management:zcrfid:query')")
    public CommonResult<PageResult<ZcrfidRespVO>> getZcrfidPage(@Valid ZcrfidPageReqVO pageVO) {
        PageResult<ZcrfidDO> pageResult = zcrfidService.getZcrfidPage(pageVO);
        return success(ZcrfidConvert.INSTANCE.convertPage(pageResult));
    }


}
