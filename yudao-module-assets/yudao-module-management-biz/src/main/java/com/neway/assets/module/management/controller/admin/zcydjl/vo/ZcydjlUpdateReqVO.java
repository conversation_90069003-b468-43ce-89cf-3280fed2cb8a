package com.neway.assets.module.management.controller.admin.zcydjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 资产异动记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcydjlUpdateReqVO extends ZcydjlBaseVO {

    @Schema(description = "自增长id", required = true, example = "29988")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
