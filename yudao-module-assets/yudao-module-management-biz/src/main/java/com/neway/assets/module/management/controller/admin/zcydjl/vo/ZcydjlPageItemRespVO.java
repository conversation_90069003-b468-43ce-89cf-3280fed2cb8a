package com.neway.assets.module.management.controller.admin.zcydjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 * <AUTHOR>
 * @since 2023/5/19 8:54
 **/
@Schema(description = "管理后台 - 资产异动分页详细数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcydjlPageItemRespVO extends ZcydjlRespVO{

    @Schema(description = "资产名称", required = true)
    private String assetsName;
    @Schema(description = "原部门名称", required = true)
    private String srcDept;
    @Schema(description = "目标部门名称", required = true)
    private String tarDept;
    @Schema(description = "原资管员名称", required = true)
    private String srcAdmin;
    @Schema(description = "目标资管员名称", required = true)
    private String tarAdmin;
}
