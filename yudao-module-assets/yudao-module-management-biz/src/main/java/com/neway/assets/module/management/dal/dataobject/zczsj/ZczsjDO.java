package com.neway.assets.module.management.dal.dataobject.zczsj;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neway.assets.module.info.enums.enable.AssetsStatusEnum;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 资产主数据 DO
 *
 * <AUTHOR>
 */
@TableName(value = "mt_zczsj", schema = "gdzc")
@KeySequence("mt_zczsj_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZczsjDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 资产分类
     */
    private Long zcfl;
    /**
     * 资产二级分类
     */
    private Long secondZcfl;
    /**
     * 资产编号
     */
    private String zcbh;
    /**
     * 财务编码
     */
    private String cwbh;
    /**
     * 资产名称
     */
    private String zcmc;
    /**
     * 规格型号
     */
    private String ggxh;
    /**
     * 资产部门
     */
    private Long zcbm;
    /**
     * 资产地点
     */
    private Long zcdd;
    /**
     * 安装位置
     */
    private String azwz;
    /**
     * SRM系统供应商
     */
    private Long gys;
    /**
     * 维护商
     */
    private Long whs;
    /**
     * 合同编号
     */
    private String htbh;
    /**
     * 购入日期
     */
    private LocalDate grrq;
    /**
     * 数量
     */
    private BigDecimal zcsl;
    /**
     * 计量单位
     */
    private Long jldw;
    /**
     * 资产登记日期
     */
    private LocalDate zcdjrq;
    /**
     * 购入金额(含税)
     */
    private BigDecimal hsgrje;
    /**
     * 购入金额(不含税)
     */
    private BigDecimal bhsgrje;
    /**
     * 资产币种
     */
    private Long zcbz;
    /**
     * 质保期
     */
    private LocalDate zbq;
    /**
     * 残值率
     */
    private BigDecimal czl;
    /**
     * 残值
     */
    private BigDecimal czs;
    /**
     * 当期剩余价值
     */
    private BigDecimal dqsycz;
    /**
     * 使用年限
     */
    private Integer synxian;
    /**
     * 报废日期
     */
    private LocalDate bfrq;
    /**
     * 资产使用人
     */
    private Long zcsyr;
    /**
     * 资产状态
     *
     * 枚举 {@link AssetsStatusEnum}
     */
    private Integer zczt;
    /**
     * 资产照片
     */
    private String zczp;
    /**
     * 备注
     */
    private String bz;

}
