package com.neway.assets.module.management.controller.admin.zcrfid.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 资产RFID关联 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcrfidExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("资产类别")
    private Long zclb;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("资产ID")
    private Long zcid;

    @ExcelProperty("开始日期")
    private LocalDate ksrq;

    @ExcelProperty("截至日期")
    private LocalDate jzrq;

    @ExcelProperty("RFID码")
    private String rfidh;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty(value = "发放原因", converter = DictConvert.class)
    @DictFormat("assets_rfid_reason") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer ffyy;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
