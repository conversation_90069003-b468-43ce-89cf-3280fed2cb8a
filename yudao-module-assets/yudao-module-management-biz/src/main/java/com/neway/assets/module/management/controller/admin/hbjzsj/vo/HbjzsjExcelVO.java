package com.neway.assets.module.management.controller.admin.hbjzsj.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 耗材/备品备件主数据 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HbjzsjExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("耗材/备品备件分类")
    private Long bjflid;

    @ExcelProperty("物料编号")
    private String wlbh;

    @ExcelProperty("物料描述")
    private String wlms;

    @ExcelProperty("型号规格")
    private String xhge;

    @ExcelProperty("计量单位")
    private Long jldw;

    @ExcelProperty("库存领用原则")
    private Integer lyyz;

    @ExcelProperty("安全库存数")
    private BigDecimal aqkcsl;

    @ExcelProperty("最小采购批量")
    private BigDecimal zxcgpl;

    @ExcelProperty("默认仓库")
    private Long mrck;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
