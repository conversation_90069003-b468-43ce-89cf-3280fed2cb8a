package com.neway.assets.module.management.service.zzcxx;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidCreateReqVO;
import com.neway.assets.module.management.controller.admin.zcrfid.vo.ZcrfidUpdateReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxCreateReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxExportReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxPageReqVO;
import com.neway.assets.module.management.controller.admin.zzcxx.vo.ZzcxxUpdateReqVO;
import com.neway.assets.module.management.convert.zzcxx.ZzcxxConvert;
import com.neway.assets.module.management.dal.dataobject.zzcxx.ZzcxxDO;
import com.neway.assets.module.management.dal.mysql.zzcxx.ZzcxxMapper;
import com.neway.assets.module.management.service.zcrfid.ZcrfidService;
import com.neway.assets.module.management.service.zczsj.ZczsjService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCZSJ_NOT_EXISTS;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZZCXX_NOT_EXISTS;

/**
 * 子资产数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZzcxxServiceImpl implements ZzcxxService {
    @Resource
    private ZczsjService zczsjService;
    @Resource
    private ZcrfidService zcrfidService;

    @Resource
    private ZzcxxMapper zzcxxMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createZzcxx(ZzcxxCreateReqVO createReqVO) {
        // 验证主数据存在
        if (zczsjService.getZczsj(Long.valueOf(createReqVO.getZczsjid())) == null) throw exception(ZCZSJ_NOT_EXISTS);
        // 插入
        ZzcxxDO zzcxx = ZzcxxConvert.INSTANCE.convert(createReqVO);
        zzcxxMapper.insert(zzcxx);
        // RFID
        if (StrUtil.isNotBlank(createReqVO.getRfid())) {
            zcrfidService.createZcrfid((ZcrfidCreateReqVO) new ZcrfidCreateReqVO()
                    .setRfidh(createReqVO.getRfid())
                    .setZclb(AssetsClassEnum.PART.getVal())
                    .setZcid(zzcxx.getId())
                    .setKsrq(LocalDate.now())
                    .setJzrq(createReqVO.getJzrq()));
        }
        // 返回
        return zzcxx.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateZzcxx(ZzcxxUpdateReqVO updateReqVO) {
        // 校验存在
        validateZzcxxExists(updateReqVO.getId());
        // 验证主数据存在
        if (zczsjService.getZczsj(Long.valueOf(updateReqVO.getZczsjid())) == null) throw exception(ZCZSJ_NOT_EXISTS);
        // 更新
        ZzcxxDO updateObj = ZzcxxConvert.INSTANCE.convert(updateReqVO);
        zzcxxMapper.updateById(updateObj);
        // RFID
        zcrfidService.updateRfidByOld(updateReqVO.getRfid(), (ZcrfidUpdateReqVO) new ZcrfidUpdateReqVO()
                .setZcid(updateReqVO.getId())
                .setZclb(AssetsClassEnum.PART.getVal())
                .setJzrq(updateReqVO.getJzrq()));
    }

    @Override
    public void deleteZzcxx(Long id) {
        // 校验存在
        validateZzcxxExists(id);
        // 删除
        zzcxxMapper.deleteById(id);
    }

    private void validateZzcxxExists(Long id) {
        if (zzcxxMapper.selectById(id) == null) {
            throw exception(ZZCXX_NOT_EXISTS);
        }
    }

    @Override
    public ZzcxxDO getZzcxx(Long id) {
        return zzcxxMapper.selectById(id);
    }

    @Override
    public List<ZzcxxDO> getZzcxxList(Collection<Long> ids) {
        return zzcxxMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZzcxxDO> getZzcxxPage(ZzcxxPageReqVO pageReqVO) {
        return zzcxxMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZzcxxDO> getZzcxxList(ZzcxxExportReqVO exportReqVO) {
        return zzcxxMapper.selectList(exportReqVO);
    }

}
