package com.neway.assets.module.inventory.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 资产管理 盘点模块错误码
 * <AUTHOR>
 * @since 2023/5/23 10:11
 **/
public interface ErrorCodeConstants {

    // ==========  资产盘点计划模块 1-102-001-000 ==========
    ErrorCode ZCPDJH_NOT_EXISTS = new ErrorCode(1102001000, "资产盘点计划不存在");
    ErrorCode ZCPDJH_DUPLICATE_PDJHH = new ErrorCode(1102001001, "盘点计划号{}已经存在");
    ErrorCode ZCPDJH_UNMODIFIABLE = new ErrorCode(1102001002, "盘点计划已发布，无法添加清单");

    // ==========  资产盘点清单模块 1-102-002-000 ==========
    ErrorCode ZCPDQD_NOT_EXISTS = new ErrorCode(1102002000, "资产盘点清单不存在");
    ErrorCode ZCPDQD_DUPLICATE_ASSETS = new ErrorCode(1102002001, "不允许添加重复的资产");

    ErrorCode PLAN_ERROR_STATUS = new ErrorCode(1102002002, "盘点计划状态异常，无法操作");
    ErrorCode NO_ZCFL_IN_CHARGE = new ErrorCode(1102002003, "当前用户没有分管的资产分类或分类下无资产");
    ErrorCode NO_UNCONFIRMED_IN_PLAN = new ErrorCode(1102002004, "当前盘点计划没有未确认的清单，请先取消确认再退回计划");

}
