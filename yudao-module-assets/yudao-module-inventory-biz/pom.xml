<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yudao-module-assets</artifactId>
        <groupId>com.neway.assets</groupId>
        <version>0.0.1-snapshot</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yudao-module-inventory-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        资产管理的盘点模块，主要实现各类资产的盘点功能
    </description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-common</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-bpm-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 资产管理的其他模块 -->
        <dependency>
            <groupId>com.neway.assets</groupId>
            <artifactId>yudao-module-management-api</artifactId>
            <version>${assets.revision}</version>
        </dependency>

        <dependency>
            <groupId>com.neway.assets</groupId>
            <artifactId>yudao-module-info-api</artifactId>
            <version>${assets.revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neway.assets</groupId>
            <artifactId>yudao-module-inventory-api</artifactId>
            <version>0.0.1-snapshot</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>