<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.neway.assets.module.inventory.dal.mysql.zcpdjh.ZcpdjhMapper">

    <select id="selectListByStocktakingUser"
            resultType="com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO">
        select
            jh.*
        from
            gdzc.op_zcpdjh jh
        left join gdzc.op_zcpdqd qd on jh.id = qd.pdjhhid
        where jh.status &gt; 0 and jh.status &lt;= 3
        and qd.spren = #{userId}
    </select>
</mapper>
