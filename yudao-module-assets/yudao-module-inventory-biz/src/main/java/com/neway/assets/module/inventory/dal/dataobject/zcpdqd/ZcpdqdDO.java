package com.neway.assets.module.inventory.dal.dataobject.zcpdqd;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 资产盘点清单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "op_zcpdqd", schema = "gdzc")
@KeySequence("op_zcpdqd_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcpdqdDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 盘点计划号id（op_zcpdjh->id）
     */
    private Long pdjhhid;
    /**
     * 资产类别（0：整机   1:零部件）
     */
    private Integer zclb;
    /**
     * 资产id（mt_zczsj->id ,mt_zzcxx->id）
     */
    private Long zcxxid;
    /**
     * 盘点数量
     */
    private BigDecimal pdsl;
    /**
     * 计量单位（bs_jldw-->id）
     */
    private Long jldw;
    /**
     * 实盘数量
     */
    private BigDecimal spsl;
    /**
     * 实盘计量单位（bs_jldw-->id）
     */
    private Long spjldw;
    /**
     * 盈亏码（-1：盘亏   1：盘盈）
     */
    private Integer ykm;
    /**
     * 确认码（0：未确认   1：确认   确认后不可再更改）
     */
    private Integer qrm;
    /**
     * 实盘日期
     */
    private LocalDate sprq;
    /**
     * 实际盘点人(人力资源-->id)
     */
    private Long spren;
    /**
     * 备注
     */
    private String bz;

}
