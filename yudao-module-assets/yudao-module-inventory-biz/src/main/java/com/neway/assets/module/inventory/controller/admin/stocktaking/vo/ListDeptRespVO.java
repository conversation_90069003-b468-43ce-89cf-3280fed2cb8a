package com.neway.assets.module.inventory.controller.admin.stocktaking.vo;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2023/10/17 8:42
 **/
@Schema(description = "管理后台 - 资管员所管资产部门列表 Response VO")
@Data
public class ListDeptRespVO {
    private Long id;
    private String name;
    private Long parentId;

    private List<AdminUserRespDTO> assetsAdminUsers;
}
