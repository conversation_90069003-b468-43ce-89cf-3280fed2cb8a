package com.neway.assets.module.inventory.controller.admin.stocktaking;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.permission.RoleApi;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.enums.permission.RoleCodeEnum;
import com.neway.assets.module.info.api.zcflZgy.ZcflZgyApi;
import com.neway.assets.module.info.api.zcflZgy.dto.ZcflZgyRespDTO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.ListDeptRespVO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.StockConfirmPageReqVO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.StocktakingReqVO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.TaskListRespVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhPageReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhRespVO;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.ZcpdqdPageItemRespVO;
import com.neway.assets.module.inventory.convert.stocktaking.StocktakingConvert;
import com.neway.assets.module.inventory.convert.zcpdjh.ZcpdjhConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.service.confirm.StockConfirmService;
import com.neway.assets.module.inventory.service.stocktaking.StocktakingService;
import com.neway.assets.module.management.api.zczsj.ZczsjApi;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.neway.assets.module.inventory.enums.ErrorCodeConstants.NO_ZCFL_IN_CHARGE;

/**
 * 盘点功能Controller接口类
 * <AUTHOR>
 * @since 2023/8/22 14:40
 **/
@Tag(name = "管理后台 - 资产盘点")
@RequiredArgsConstructor
@RestController
@RequestMapping("inventory")
public class StocktakingController {

    private final DeptApi deptApi;
    private final ZcflZgyApi zcflZgyApi;
    private final ZczsjApi zczsjApi;
    private final AdminUserApi adminUserApi;
    private final RoleApi roleApi;
    private final PermissionApi permissionApi;

    private final StocktakingService stocktakingService;
    private final StockConfirmService stockConfirmService;

    @GetMapping("task-list")
    @Operation(summary = "获取当前用户任务列表")
    @PreAuthorize("@ss.hasPermission('inventory:task:list')")
    public CommonResult<List<TaskListRespVO>> taskList() {
        return success(stocktakingService.getTaskList());
    }

    @PostMapping("stocktaking")
    @Operation(summary = "盘点功能")
    @PreAuthorize("@ss.hasPermission('inventory:task:stocktaking')")
    public CommonResult<Boolean> stocktaking(@RequestBody @Valid StocktakingReqVO stocktakingReqVO) {
        return success(stocktakingService.stocktaking(stocktakingReqVO));
    }

    /**
     * 获取可以确认的
     */
    @GetMapping("page-confirm-plan")
    @Operation(summary = "获取确认的盘点清单")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:list')")
    public CommonResult<PageResult<ZcpdjhRespVO>> pagePlanTobeConfirm(@Valid ZcpdjhPageReqVO pageVO) {
        PageResult<ZcpdjhDO> result = stockConfirmService.pagePlanTobeConfirm(pageVO);
        return success(ZcpdjhConvert.INSTANCE.convertPage(result));
    }

    /**
     * 获取可以确认清单列表（包含已确认的）
     * @param pageReqVO 盘点计划ID
     */
    @GetMapping("confirm-list")
    @Operation(summary = "获取确认的盘点清单")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:list')")
    public CommonResult<PageResult<ZcpdqdPageItemRespVO>> list(StockConfirmPageReqVO pageReqVO) {
        List<ZcpdqdPageItemRespVO> total = stockConfirmService.genConfirmList(pageReqVO.getPlanId());
        // 分页
        PageResult<ZcpdqdPageItemRespVO> result = new PageResult<>((long) total.size());
        result.setList(total.stream().skip((long) (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize())
                .limit(pageReqVO.getPageSize())
                .collect(Collectors.toList()));
        return success(result);
    }

    /**
     * 确认盘点清单记录
     * @param planDetailIds 盘点清单记录ID
     */
    @PostMapping("confirm")
    @Operation(summary = "确认盘点清单")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:confirm')")
    public CommonResult<Boolean> confirm(@RequestBody @Validated @NotEmpty List<Long> planDetailIds) {
        stockConfirmService.confirmStock(planDetailIds);
        return success(true);
    }

    /**
     * 撤销确认
     * @param planDetailIds 盘点清单记录ID
     */
    @PostMapping("confirm/cancel")
    @Operation(summary = "撤销确认盘点清单")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:confirm')")
    public CommonResult<Boolean> confirmCancel(@RequestBody @Validated @NotEmpty List<Long> planDetailIds) {
        stockConfirmService.confirmCancel(planDetailIds);
        return success(true);
    }

    /**
     * 部门负责人退回盘点清单
     * @param planIds 盘点清单记录ID
     */
    @PutMapping("confirm/return-back/dept")
    @Operation(summary = "部门负责人退回盘点计划")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:confirm')")
    public CommonResult<Boolean> confirmReturnBackByDept(@RequestBody @Validated @NotEmpty List<Long> planIds) {
        return success(stockConfirmService.deptReturnBackPlans(planIds));
    }

    /**
     * 计划资管员退回盘点清单
     * @param planIds 盘点清单记录ID
     */
    @PutMapping("confirm/return-back/admin")
    @Operation(summary = "计划资管员退回盘点计划")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:create')")
    public CommonResult<Boolean> confirmReturnBackByAdmin(@RequestBody @Validated @NotEmpty List<Long> planIds) {
        return success(stockConfirmService.adminReturnBackPlans(planIds));
    }

    /**
     * 计划资管员确认盘点清单
     * @param planIds 盘点清单记录ID
     */
    @PostMapping("confirm/admin")
    @Operation(summary = "计划资管员确认盘点计划")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:create')")
    public CommonResult<Boolean> confirmPlanByAdmin(@RequestBody @Validated @NotEmpty List<Long> planIds) {
        return success(stockConfirmService.adminConfirmPlans(planIds));
    }

    @GetMapping("list-dept")
    @Operation(summary = "获取资管员所管分类下的部门列表")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:create')")
    public CommonResult<List<ListDeptRespVO>> getHandleDeptList() {
        Set<Long> typeIds = zcflZgyApi.getZcflZgyListByUserId(getLoginUserId()).stream()
                .map(ZcflZgyRespDTO::getZcflId).collect(Collectors.toSet());
        Set<Long> deptIds = zczsjApi.getZczsjListByTypeId(typeIds).stream()
                .map(ZczsjRespDTO::getZcbm)
                .collect(Collectors.toSet());
        if (CollectionUtils.isAnyEmpty(deptIds)) throw exception(NO_ZCFL_IN_CHARGE);
        // 部门资管员角色ID
        Set<Long> adminRoleIds = roleApi.getRoleListByCode(RoleCodeEnum.DEPT_ASSETS_ADMIN).stream()
                .map(RoleRespDTO::getId).collect(Collectors.toSet());
        // 部门资管员角色下的用户ID
        Set<Long> assetsAdminUserIds = new HashSet<>(permissionApi.getUserRoleIdListByRoleIds(adminRoleIds));
        // 部门下的员工列表
        Map<Long, List<AdminUserRespDTO>> userMap = adminUserApi.getUserListByDeptIds(deptIds).stream()
                .collect(Collectors.groupingBy(AdminUserRespDTO::getDeptId));
        // 设置每个部门下的资管员用户列表
        List<ListDeptRespVO> deptList = StocktakingConvert.INSTANCE.convertDeptList(deptApi.getDeptList(deptIds));
        deptList.forEach(dept -> dept.setAssetsAdminUsers(userMap.getOrDefault(dept.getId(), Collections.emptyList())
                .stream().filter(user -> assetsAdminUserIds.contains(user.getId()))
                .collect(Collectors.toList())));
        return success(deptList);
    }

}
