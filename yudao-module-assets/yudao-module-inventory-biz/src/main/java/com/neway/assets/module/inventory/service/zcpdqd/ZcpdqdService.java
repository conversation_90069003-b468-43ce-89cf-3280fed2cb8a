package com.neway.assets.module.inventory.service.zcpdqd;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.*;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import com.neway.assets.module.inventory.enums.ZcpdqdConfirmCodeEnum;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 资产盘点清单 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcpdqdService {

    /**
     * 创建资产盘点清单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcpdqd(@Valid ZcpdqdCreateReqVO createReqVO);

    void createZcpdqdList(@Valid List<ZcpdqdCreateReqVO> createReqVOs);

    /**
     * 更新资产盘点清单
     *
     * @param updateReqVO 更新信息
     */
    void updateZcpdqd(@Valid ZcpdqdUpdateReqVO updateReqVO);

    void confirmZcpdqd(List<Long> ids, ZcpdqdConfirmCodeEnum codeEnum);

    void returnBackZcpdqd(Collection<Long> ids);

    /**
     * 删除资产盘点清单
     *
     * @param id 编号
     */
    void deleteZcpdqd(Long id);

    /**
     * 获得资产盘点清单
     *
     * @param id 编号
     * @return 资产盘点清单
     */
    ZcpdqdDO getZcpdqd(Long id);

    /**
     * 获得资产盘点清单列表
     *
     * @param ids 编号
     * @return 资产盘点清单列表
     */
    List<ZcpdqdDO> getZcpdqdList(Collection<Long> ids);

    /**
     * 获得资产盘点清单分页
     *
     * @param pageReqVO 分页查询
     * @return 资产盘点清单分页
     */
    PageResult<ZcpdqdPageItemRespVO> getZcpdqdPage(ZcpdqdPageReqVO pageReqVO);

    /**
     * 获得资产盘点清单列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产盘点清单列表
     */
    List<ZcpdqdDO> getZcpdqdList(ZcpdqdExportReqVO exportReqVO);

    /**
     * 根据盘点计划号查询清单列表
     * @param planCode 盘点计划号
     * @return 资产盘点清单列表
     */
    List<ZcpdqdDO> getListByPlanCode(String planCode);

    /**
     * 根据盘点计划号查询指定盘点人对应的清单列表
     * @param planCode 盘点计划号
     * @param spren 盘点人
     * @return 资产盘点清单列表
     */
    List<ZcpdqdDO> getListByPlanCode(String planCode, Long spren);

    List<ZcpdqdPageItemRespVO> getListByPlanIds(Long[] planIds);

}
