package com.neway.assets.module.inventory.controller.admin.zcpdqd.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 资产盘点清单 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcpdqdExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("盘点计划号id（op_zcpdjh->id）")
    private Long pdjhhid;

    @ExcelProperty("资产类别（0：整机   1:零部件）")
    private Integer zclb;

    @ExcelProperty("资产id（mt_zczsj->id ,mt_zzcxx->id）")
    private Long zcxxid;

    @ExcelProperty("盘点数量")
    private BigDecimal pdsl;

    @ExcelProperty("计量单位（bs_jldw-->id）")
    private Long jldw;

    @ExcelProperty("实盘数量")
    private BigDecimal spsl;

    @ExcelProperty("实盘计量单位（bs_jldw-->id）")
    private Long spjldw;

    @ExcelProperty("盈亏码（-1：盘亏   1：盘盈）")
    @DictFormat("inventory_ykm")
    private Integer ykm;

    @ExcelProperty("确认码（0：未确认   1：确认   确认后不可再更改）")
    private Integer qrm;

    @ExcelProperty("实盘日期")
    private LocalDate sprq;

    @ExcelProperty("实际盘点人(人力资源-->id)")
    private Long spren;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
