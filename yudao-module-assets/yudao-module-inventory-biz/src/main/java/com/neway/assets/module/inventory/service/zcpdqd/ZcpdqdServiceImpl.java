package com.neway.assets.module.inventory.service.zcpdqd;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.toolkit.ArrayUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.neway.assets.module.info.api.jldw.JldwApi;
import com.neway.assets.module.info.api.jldw.dto.JldwRespDTO;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhExportReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.*;
import com.neway.assets.module.inventory.convert.zcpdqd.ZcpdqdConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import com.neway.assets.module.inventory.dal.mysql.zcpdqd.ZcpdqdMapper;
import com.neway.assets.module.inventory.enums.ZcpdqdConfirmCodeEnum;
import com.neway.assets.module.inventory.service.zcpdjh.ZcpdjhService;
import com.neway.assets.module.management.api.zczsj.ZczsjApi;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;
import com.neway.assets.module.management.api.zzcxx.ZzcxxApi;
import com.neway.assets.module.management.api.zzcxx.dto.ZzcxxRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.ENTIRE;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.PART;
import static com.neway.assets.module.inventory.enums.ErrorCodeConstants.*;
import static com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums.DRAFT;
import static com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums.ERROR_CANCEL;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCLB_NOT_EXISTS;

/**
 * 资产盘点清单 Service 实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Validated
public class ZcpdqdServiceImpl implements ZcpdqdService {

    private final ZcpdqdMapper zcpdqdMapper;

    private final ZcpdjhService zcpdjhService;

    private final JldwApi jldwApi;
    private final ZczsjApi zczsjApi;
    private final ZzcxxApi zzcxxApi;

    @Override
    public Long createZcpdqd(ZcpdqdCreateReqVO createReqVO) {
        // 插入
        ZcpdqdDO zcpdqd = ZcpdqdConvert.INSTANCE.convert(createReqVO);
        zcpdqdMapper.insert(zcpdqd);
        // todo 发起盘点清单流程
        // 返回

        return zcpdqd.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createZcpdqdList(List<ZcpdqdCreateReqVO> createReqVOs) {
        if (CollUtil.isEmpty(createReqVOs)) return;
        List<ZcpdqdDO> insertList = ZcpdqdConvert.INSTANCE.convertList03(createReqVOs);

        Map<Long, List<ZcpdqdDO>> listMap = insertList.stream().collect(Collectors.groupingBy(ZcpdqdDO::getPdjhhid));
        // 验证计划状态
        Set<Long> planIds = listMap.keySet();
        zcpdjhService.getZcpdjhList(planIds).stream().map(ZcpdjhDO::getStatus).forEach(status -> {
            if (status != null && status > DRAFT.ordinal() && status < ERROR_CANCEL.ordinal())
                throw exception(ZCPDJH_UNMODIFIABLE);
        });
        // 验证重复性
        listMap.forEach((k, v) -> {
            Set<String> existSet = zcpdqdMapper.selectList(ZcpdqdDO::getPdjhhid, k)
                    .stream().map(o -> o.getZclb() + "-" + o.getZcxxid())
                    .collect(Collectors.toSet());
            if (v.stream().anyMatch(o -> existSet.contains(o.getZclb() + "-" + o.getZcxxid())))
                throw exception(ZCPDQD_DUPLICATE_ASSETS);
        });
        zcpdqdMapper.insertBatch(insertList);
    }

    @Override
    public void updateZcpdqd(ZcpdqdUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcpdqdExists(updateReqVO.getId());
        // 更新
        ZcpdqdDO updateObj = ZcpdqdConvert.INSTANCE.convert(updateReqVO);
        zcpdqdMapper.updateById(updateObj);
    }

    @Override
    public void confirmZcpdqd(List<Long> ids, ZcpdqdConfirmCodeEnum codeEnum) {
        zcpdqdMapper.update(null, Wrappers.lambdaUpdate(ZcpdqdDO.class)
                .in(ZcpdqdDO::getId, ids)
                .set(ZcpdqdDO::getQrm, codeEnum.getVal()));

    }

    @Override
    public void returnBackZcpdqd(Collection<Long> ids) {
        zcpdqdMapper.update(null, Wrappers.lambdaUpdate(ZcpdqdDO.class).in(ZcpdqdDO::getId, ids)
                .set(ZcpdqdDO::getSprq, null));
    }

    @Override
    public void deleteZcpdqd(Long id) {
        // 校验存在
        validateZcpdqdExists(id);
        // 校验计划状态
        validatePlanStatus(id);
        // 删除
        zcpdqdMapper.deleteById(id);
    }

    private void validateZcpdqdExists(Long id) {
        if (zcpdqdMapper.selectById(id) == null) {
            throw exception(ZCPDQD_NOT_EXISTS);
        }
    }

    private void validatePlanStatus(Long zcpdqdId) {
        ZcpdqdDO zcpdqdDO = zcpdqdMapper.selectById(zcpdqdId);
        ZcpdjhDO plan = zcpdjhService.getZcpdjh(zcpdqdDO.getPdjhhid());
        if (Objects.isNull(plan) || plan.getStatus() > DRAFT.ordinal())
            throw exception(PLAN_ERROR_STATUS);
    }

    @Override
    public ZcpdqdDO getZcpdqd(Long id) {
        return zcpdqdMapper.selectById(id);
    }

    @Override
    public List<ZcpdqdDO> getZcpdqdList(Collection<Long> ids) {
        return zcpdqdMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcpdqdPageItemRespVO> getZcpdqdPage(ZcpdqdPageReqVO pageReqVO) {
        PageResult<ZcpdqdDO> pageResult = zcpdqdMapper.selectPage(pageReqVO);
        return new PageResult<>(convertPageItemVO(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public List<ZcpdqdDO> getZcpdqdList(ZcpdqdExportReqVO exportReqVO) {
        return zcpdqdMapper.selectList(exportReqVO);
    }

    @Override
    public List<ZcpdqdDO> getListByPlanCode(String planCode) {
        return getListByPlanCode(planCode, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ZcpdqdDO> getListByPlanCode(String planCode, Long spren) {
        List<ZcpdjhDO> zcpdjhList = zcpdjhService.getZcpdjhList(new ZcpdjhExportReqVO().setPdjhh(planCode));
        if (CollUtil.isEmpty(zcpdjhList)) throw exception(ZCPDJH_NOT_EXISTS);
        return zcpdqdMapper.selectList(new LambdaQueryWrapperX<ZcpdqdDO>()
                .eq(ZcpdqdDO::getPdjhhid, zcpdjhList.get(0).getId())
                .eqIfPresent(ZcpdqdDO::getSpren, spren));
    }

    @Override
    public List<ZcpdqdPageItemRespVO> getListByPlanIds(Long[] planIds) {
        if (ArrayUtils.isEmpty(planIds)) return Collections.emptyList();
        return convertPageItemVO(zcpdqdMapper.selectList(Wrappers.lambdaQuery(ZcpdqdDO.class)
                .in(ZcpdqdDO::getPdjhhid, Arrays.asList(planIds))));
    }

    private List<ZcpdqdPageItemRespVO> convertPageItemVO(List<ZcpdqdDO> source) {
        Map<Long, ZcpdjhDO> planMap = convertMap(zcpdjhService.getZcpdjhList(convertSet(source, ZcpdqdDO::getPdjhhid)), ZcpdjhDO::getId);
        Map<Long, ZczsjRespDTO> assetsMap = zczsjApi.getZczsjMap(source.stream()
                .filter(o -> Objects.equals(o.getZclb(), ENTIRE.getVal()))
                .map(ZcpdqdDO::getZcxxid)
                .collect(Collectors.toSet()));
        Map<Long, ZzcxxRespDTO> assetsChildMap = zzcxxApi.getZzcxxjMap(source.stream()
                .filter(o -> Objects.equals(o.getZclb(), PART.getVal()))
                .map(ZcpdqdDO::getZcxxid)
                .collect(Collectors.toSet()));
        Set<Long> unitIds = convertSet(source, ZcpdqdDO::getJldw);
        unitIds.addAll(convertSet(source, ZcpdqdDO::getSpjldw));
        Map<Long, JldwRespDTO> unitMap = jldwApi.getJldwMap(unitIds);
        List<ZcpdqdPageItemRespVO> result = new ArrayList<>();
        source.forEach(o -> {
            ZcpdqdPageItemRespVO respVO = ZcpdqdConvert.INSTANCE.convertPageItem(o);
            respVO.setPlanCode(planMap.get(o.getPdjhhid()).getPdjhh());
            AssetsClassEnum classEnum = AssetsClassEnum.getByVal(o.getZclb());
            if (classEnum == null) throw exception(ZCLB_NOT_EXISTS);
            switch (classEnum) {
                case PART:
                    respVO.setAssetsName(assetsChildMap.get(o.getZcxxid()).getZzcmc());
                    break;
                case ENTIRE:
                    respVO.setAssetsName(assetsMap.get(o.getZcxxid()).getZcmc());
                    break;
            }
            respVO.setUnit(unitMap.get(o.getJldw()).getJldwmc());
            result.add(respVO);
        });
        return result;
    }

}
