package com.neway.assets.module.inventory.service.confirm;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhExportReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhPageReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.ZcpdqdExportReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.ZcpdqdPageItemRespVO;
import com.neway.assets.module.inventory.convert.zcpdjh.ZcpdjhConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import com.neway.assets.module.inventory.enums.ZcpdqdConfirmCodeEnum;
import com.neway.assets.module.inventory.service.zcpdjh.ZcpdjhService;
import com.neway.assets.module.inventory.service.zcpdqd.ZcpdqdService;
import com.neway.assets.module.management.api.zcydjl.ZcydjlApi;
import com.neway.assets.module.management.api.zcydjl.dto.ZcydjlReqDTO;
import com.neway.assets.module.management.api.zczsj.ZczsjApi;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.neway.assets.module.info.enums.enable.AssetsAdjustTypeEnum.INVENTORY_LOSS;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.ENTIRE;
import static com.neway.assets.module.inventory.enums.ErrorCodeConstants.*;
import static com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums.*;
import static com.neway.assets.module.inventory.enums.ZcpdqdConfirmCodeEnum.CONFIRMED;
import static com.neway.assets.module.inventory.enums.ZcpdqdConfirmCodeEnum.UNCONFIRMED;

/**
 * <AUTHOR>
 * @since 2023/10/8 8:59
 **/
@RequiredArgsConstructor
@Slf4j
@Service
public class StockConfirmServiceImpl implements StockConfirmService{

    private final DeptApi deptApi;
    private final ZczsjApi zczsjApi;
    private final ZcydjlApi zcydjlApi;

    private final ZcpdjhService zcpdjhService;
    private final ZcpdqdService zcpdqdService;

    @Override
    public List<ZcpdqdPageItemRespVO> genConfirmList(Long... planId) {
        // 已盘点的清单
        List<ZcpdqdPageItemRespVO> inventoryList = zcpdqdService.getListByPlanIds(planId)
                .stream().filter(o -> Objects.nonNull(o.getSprq()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(inventoryList)) return Collections.emptyList(); //防MP的空集报错
        // 查询资产主数据
        List<ZczsjRespDTO> assetsList = zczsjApi.getZczsjList(inventoryList.stream()
                .filter(o -> Objects.equals(ENTIRE.getVal(), o.getZclb()))
                .map(ZcpdqdPageItemRespVO::getZcxxid)
                .collect(Collectors.toList()));
        // 查询资产数据中的且负责人是当前用户的部门信息
        Long currUser = getLoginUserId();
        Set<Long> deptIds = deptApi.getDeptList(assetsList.stream().map(ZczsjRespDTO::getZcbm).collect(Collectors.toSet()))
                .stream().filter(dept -> Objects.equals(currUser, dept.getLeaderUserId()))
                .map(DeptRespDTO::getId).collect(Collectors.toSet());
        // 筛选属于当前部门的资产ID
        Set<Long> assetsIds = assetsList.stream().filter(assets -> deptIds.contains(assets.getZcbm())).map(ZczsjRespDTO::getId)
                .collect(Collectors.toSet());
        // 筛选最初的清单数据
        return inventoryList.stream().filter(o -> Objects.equals(ENTIRE.getVal(), o.getZclb()) && assetsIds.contains(o.getZcxxid()))
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<ZcpdjhDO> pagePlanTobeConfirm(ZcpdjhPageReqVO pageVO) {
        ZcpdjhExportReqVO reqVO = ZcpdjhConvert.INSTANCE.convert(pageVO);
        List<ZcpdjhDO> list = zcpdjhService.getZcpdjhList(reqVO)
                .stream().filter(o -> o.getStatus().compareTo(DEPT_RETURN_BACK.ordinal()) >= 0
                        && o.getStatus().compareTo(ADMIN_RETURN_BACK.ordinal()) <=0)
                .collect(Collectors.toList());
        Map<Long, List<ZcpdqdPageItemRespVO>> detailMap = genConfirmList(list.stream().map(ZcpdjhDO::getId).toArray(Long[]::new))
                .stream().collect(Collectors.groupingBy(ZcpdqdPageItemRespVO::getPdjhhid));
        PageResult<ZcpdjhDO> result = PageResult.empty();
        result.setList(list.stream().filter(plan -> CollUtil.isNotEmpty(detailMap.get(plan.getId())))
                .peek(plan -> result.setTotal(result.getTotal() + 1))
                .skip((long) (pageVO.getPageNo() - 1) * pageVO.getPageSize())
                .limit(pageVO.getPageSize())
                .collect(Collectors.toList()));
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmStock(List<Long> planDetailIds) {
        validatePlanStatus(planDetailIds.get(0));
        zcpdqdService.confirmZcpdqd(planDetailIds, ZcpdqdConfirmCodeEnum.CONFIRMED);
        // 若全部确认完，需要变更计划状态
        Long[] confirmedPlans = zcpdqdService.getZcpdqdList(planDetailIds).stream().map(ZcpdqdDO::getPdjhhid)
                .distinct().filter(planId -> {
                    List<ZcpdqdDO> detailList = zcpdqdService.getZcpdqdList(new ZcpdqdExportReqVO().setPdjhhid(planId));
                    return detailList.stream().allMatch(o -> Objects.equals(o.getQrm(), CONFIRMED.getVal()));
                }).toArray(Long[]::new);
        if (confirmedPlans.length > 0) zcpdjhService.updateStatusByIds(ADMIN_CONFIRMING, confirmedPlans);
    }

    @Override
    public void confirmCancel(List<Long> planDetailIds) {
        if (CollectionUtils.isAnyEmpty(planDetailIds)) return;
        validatePlanStatus(planDetailIds.get(0));
        zcpdqdService.confirmZcpdqd(planDetailIds, UNCONFIRMED);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deptReturnBackPlans(Collection<Long> planIds) {
        if (CollectionUtils.isAnyEmpty(planIds)) return true;
        List<ZcpdjhDO> planList = zcpdjhService.getZcpdjhList(planIds);
        // 排除计划状态异常
        if (planList.stream().anyMatch(o -> o.getStatus().compareTo(DEPT_RETURN_BACK.ordinal()) < 0
                || o.getStatus().compareTo(ADMIN_RETURN_BACK.ordinal()) > 0))
            throw exception(PLAN_ERROR_STATUS);
        // 清单未确认的实盘日期需要清空
        Set<Long> detailIds = genConfirmList(planIds.toArray(new Long[0])).stream()
                .filter(o -> Objects.equals(o.getQrm(), UNCONFIRMED.getVal()))
                .map(ZcpdqdPageItemRespVO::getId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isAnyEmpty(detailIds)) throw exception(NO_UNCONFIRMED_IN_PLAN);
        zcpdqdService.returnBackZcpdqd(detailIds);
        zcpdjhService.updateStatusByIds(DEPT_RETURN_BACK, planIds.toArray(new Long[0]));

        return true;
    }

    @Override
    public boolean adminReturnBackPlans(Collection<Long> planIds) {
        if (CollectionUtils.isAnyEmpty(planIds)) return true;
        List<ZcpdjhDO> planList = zcpdjhService.getZcpdjhList(planIds);
        // 排除计划状态异常
        if (planList.stream().anyMatch(o -> !Objects.equals(o.getStatus(), ADMIN_CONFIRMING.ordinal())))
            throw exception(PLAN_ERROR_STATUS);
        zcpdjhService.updateStatusByIds(ADMIN_RETURN_BACK, planIds.toArray(new Long[0]));
        return true;
    }

    @Override
    public boolean adminConfirmPlans(Collection<Long> planIds) {
        if (CollectionUtils.isAnyEmpty(planIds)) return true;
        List<ZcpdjhDO> planList = zcpdjhService.getZcpdjhList(planIds);
        // 排除计划状态异常
        if (planList.stream().anyMatch(o -> !Objects.equals(o.getStatus(), ADMIN_CONFIRMING.ordinal())))
            throw exception(PLAN_ERROR_STATUS);
        zcpdjhService.updateStatusByIds(FINISHED, planIds.toArray(new Long[0]));
        // 汇总盘亏去清单，发起盘亏流程 --> 逐个资产发起
        zcpdqdService.getListByPlanIds(planIds.toArray(new Long[0]))
                        .stream().filter(o -> Objects.equals(-1, o.getYkm()))
                        .forEach(detail -> {
                            ZcydjlReqDTO zcydjlReqDTO = new ZcydjlReqDTO().setZclb(detail.getZclb())
                                    .setZcxxid(detail.getZcxxid())
                                    .setYdfs(INVENTORY_LOSS.name())
                                    .setKsrq(LocalDate.now());
                            zcydjlApi.createZcydjlProcessInstance(getLoginUserId(), zcydjlReqDTO);
                        });
        return true;
    }

    /**
     * 验证盘点计划状态是否可以确认
     * @param planDetailId 清单ID
     */
    private void validatePlanStatus(Long planDetailId) {
        if (Objects.isNull(planDetailId)) throw exception(ZCPDQD_NOT_EXISTS);
        ZcpdqdDO zcpdqd = zcpdqdService.getZcpdqd(planDetailId);
        ZcpdjhDO plan = zcpdjhService.getZcpdjh(zcpdqd.getPdjhhid());
        if (Objects.isNull(plan)
                || plan.getStatus().compareTo(DEPT_RETURN_BACK.ordinal()) < 0
                || plan.getStatus().compareTo(ADMIN_RETURN_BACK.ordinal()) > 0)
            throw exception(PLAN_ERROR_STATUS);
    }
}
