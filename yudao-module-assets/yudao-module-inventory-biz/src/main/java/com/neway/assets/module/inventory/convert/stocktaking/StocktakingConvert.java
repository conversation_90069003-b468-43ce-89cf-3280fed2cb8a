package com.neway.assets.module.inventory.convert.stocktaking;

import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.ListDeptRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/17 9:32
 **/
@Mapper
public interface StocktakingConvert {
    StocktakingConvert INSTANCE = Mappers.getMapper(StocktakingConvert.class);

    List<ListDeptRespVO> convertDeptList(List<DeptRespDTO> deptList);
}
