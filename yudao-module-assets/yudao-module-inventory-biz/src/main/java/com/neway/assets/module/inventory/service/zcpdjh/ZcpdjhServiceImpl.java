package com.neway.assets.module.inventory.service.zcpdjh;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhCreateReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhExportReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhPageReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhUpdateReqVO;
import com.neway.assets.module.inventory.convert.zcpdjh.ZcpdjhConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.dal.mysql.zcpdjh.ZcpdjhMapper;
import com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.inventory.enums.ErrorCodeConstants.*;
import static com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums.DRAFT;

/**
 * 资产盘点计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZcpdjhServiceImpl implements ZcpdjhService {

    @Resource
    private ZcpdjhMapper zcpdjhMapper;

    @Override
    public Long createZcpdjh(ZcpdjhCreateReqVO createReqVO) {
        // 插入
        ZcpdjhDO zcpdjh = ZcpdjhConvert.INSTANCE.convert(createReqVO);
        validatePdjhh(zcpdjh.getPdjhh());
        zcpdjhMapper.insert(zcpdjh);
        // 返回
        return zcpdjh.getId();
    }

    @Override
    public void updateZcpdjh(ZcpdjhUpdateReqVO updateReqVO) {
        // 校验存在
        ZcpdjhDO plan = validateZcpdjhExists(updateReqVO.getId());
        // 更新状态前的校验
        if (Objects.nonNull(updateReqVO.getStatus())
                && plan.getStatus() > DRAFT.ordinal()
                && Objects.equals(plan.getStatus(), updateReqVO.getStatus())) {
                throw exception(PLAN_ERROR_STATUS);
        }
        // 更新
        ZcpdjhDO updateObj = ZcpdjhConvert.INSTANCE.convert(updateReqVO);
        zcpdjhMapper.updateById(updateObj);
    }

    @Override
    public void updateStatusByIds(ZcpdjhStatusEnums status, Long... ids) {
        zcpdjhMapper.update(null,
                Wrappers.lambdaUpdate(ZcpdjhDO.class).set(ZcpdjhDO::getStatus, status.ordinal())
                .in(ZcpdjhDO::getId, Arrays.asList(ids)));
    }

    @Override
    public void deleteZcpdjh(Long id) {
        // 校验存在
        validateZcpdjhExists(id);
        // 删除
        zcpdjhMapper.deleteById(id);
    }

    private ZcpdjhDO validateZcpdjhExists(Long id) {
        ZcpdjhDO zcpdjhDO = zcpdjhMapper.selectById(id);
        if (zcpdjhDO == null) {
            throw exception(ZCPDJH_NOT_EXISTS);
        }
        return zcpdjhDO;
    }

    private void validatePdjhh(String pdjhh) {
        List<ZcpdjhDO> list = zcpdjhMapper.selectList(new ZcpdjhExportReqVO().setPdjhh(pdjhh));
        if (CollUtil.isNotEmpty(list)) throw exception(ZCPDJH_DUPLICATE_PDJHH, pdjhh);
    }

    @Override
    public ZcpdjhDO getZcpdjh(Long id) {
        return zcpdjhMapper.selectById(id);
    }

    @Override
    public List<ZcpdjhDO> getZcpdjhList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        return zcpdjhMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcpdjhDO> getZcpdjhPage(ZcpdjhPageReqVO pageReqVO) {
        return zcpdjhMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZcpdjhDO> getZcpdjhList(ZcpdjhExportReqVO exportReqVO) {
        return zcpdjhMapper.selectList(exportReqVO);
    }

    @Override
    public List<ZcpdjhDO> getListByUserId(Long userId) {
        if (userId == null) return Collections.emptyList();
        return zcpdjhMapper.selectListByStocktakingUser(userId);
    }

    @Override
    public List<ZcpdjhDO> getZcpdjhList() {
        return zcpdjhMapper.selectList();
    }
}
