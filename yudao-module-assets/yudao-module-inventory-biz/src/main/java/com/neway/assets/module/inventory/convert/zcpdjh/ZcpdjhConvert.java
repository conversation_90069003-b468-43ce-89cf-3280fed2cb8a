package com.neway.assets.module.inventory.convert.zcpdjh;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.*;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资产盘点计划 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcpdjhConvert {

    ZcpdjhConvert INSTANCE = Mappers.getMapper(ZcpdjhConvert.class);

    ZcpdjhDO convert(ZcpdjhCreateReqVO bean);

    ZcpdjhDO convert(ZcpdjhUpdateReqVO bean);

    ZcpdjhRespVO convert(ZcpdjhDO bean);

    ZcpdjhExportReqVO convert(ZcpdjhPageReqVO pageReqVO);

    List<ZcpdjhRespVO> convertList(List<ZcpdjhDO> list);

    PageResult<ZcpdjhRespVO> convertPage(PageResult<ZcpdjhDO> page);

    List<ZcpdjhExcelVO> convertList02(List<ZcpdjhDO> list);

}
