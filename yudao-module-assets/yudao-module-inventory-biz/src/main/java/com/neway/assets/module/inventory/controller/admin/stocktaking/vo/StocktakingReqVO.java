package com.neway.assets.module.inventory.controller.admin.stocktaking.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 盘点参数Request VO类
 * <AUTHOR>
 * @since 2023/8/22 15:08
 **/
@Data
public class StocktakingReqVO extends StocktakingBaseVO {

    @Schema(description = "盘点计划号")
    @NotNull(message = "盘点计划号不能为空")
    private String planCode;

    @Schema(description = "盘点到的资产清单")
    @NotEmpty(message = "资产清单不能为空")
    private List<Assets> stocktakingList;

    @Data
    public static class Assets {
        private Integer zclb;

        private Long zcxxid;

        private Integer ykm;
    }
}
