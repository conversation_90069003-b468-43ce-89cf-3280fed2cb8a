package com.neway.assets.module.inventory.controller.admin.zcpdjh.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 资产盘点计划 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ZcpdjhBaseVO {

    @Schema(description = "盘点计划号", required = true)
    @NotNull(message = "盘点计划号不能为空")
    private String pdjhh;

    @Schema(description = "计划盘点日期")
    private LocalDate jhrq;

    @Schema(description = "盘点主导部门（组织结构-->id）")
    private Long zdbm;

    @Schema(description = "盘点主导人（人力资源-->id）")
    private Long zdren;

    @Schema(description = "盘点类型")
    private Integer pdlx;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "计划状态")
    private Integer status;
}
