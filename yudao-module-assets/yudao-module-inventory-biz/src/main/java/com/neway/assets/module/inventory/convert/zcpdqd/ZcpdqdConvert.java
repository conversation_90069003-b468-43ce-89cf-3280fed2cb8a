package com.neway.assets.module.inventory.convert.zcpdqd;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.*;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资产盘点清单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcpdqdConvert {

    ZcpdqdConvert INSTANCE = Mappers.getMapper(ZcpdqdConvert.class);

    ZcpdqdDO convert(ZcpdqdCreateReqVO bean);

    ZcpdqdDO convert(ZcpdqdUpdateReqVO bean);

    ZcpdqdUpdateReqVO convert2Update(ZcpdqdDO bean);

    @Named("one")
    ZcpdqdRespVO convert(ZcpdqdDO bean);
    @Named("two")
    ZcpdqdPageItemRespVO convertPageItem(ZcpdqdDO bean);

    @IterableMapping(qualifiedByName = "one")
    List<ZcpdqdRespVO> convertList(List<ZcpdqdDO> list);

    PageResult<ZcpdqdRespVO> convertPage(PageResult<ZcpdqdDO> page);

    List<ZcpdqdExcelVO> convertList02(List<ZcpdqdDO> list);

    List<ZcpdqdDO> convertList03(List<ZcpdqdCreateReqVO> list);

    List<ZcpdqdSimpleRespVO> convertSimpleList(List<ZcpdqdDO> list);

}
