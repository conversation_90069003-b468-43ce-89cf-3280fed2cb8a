package com.neway.assets.module.inventory.dal.mysql.zcpdjh;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhExportReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhPageReqVO;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资产盘点计划 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcpdjhMapper extends BaseMapperX<ZcpdjhDO> {

    default PageResult<ZcpdjhDO> selectPage(ZcpdjhPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcpdjhDO>()
                .likeIfPresent(ZcpdjhDO::getPdjhh, reqVO.getPdjhh())
                .betweenIfPresent(ZcpdjhDO::getJhrq, reqVO.getJhrq())
                .eqIfPresent(ZcpdjhDO::getZdbm, reqVO.getZdbm())
                .eqIfPresent(ZcpdjhDO::getZdren, reqVO.getZdren())
                .likeIfPresent(ZcpdjhDO::getBz, reqVO.getBz())
                .eqIfPresent(ZcpdjhDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ZcpdjhDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcpdjhDO::getId));
    }

    default List<ZcpdjhDO> selectList(ZcpdjhExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcpdjhDO>()
                .eqIfPresent(ZcpdjhDO::getPdjhh, reqVO.getPdjhh())
                .betweenIfPresent(ZcpdjhDO::getJhrq, reqVO.getJhrq())
                .eqIfPresent(ZcpdjhDO::getZdbm, reqVO.getZdbm())
                .eqIfPresent(ZcpdjhDO::getZdren, reqVO.getZdren())
                .likeIfPresent(ZcpdjhDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcpdjhDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcpdjhDO::getId));
    }


    List<ZcpdjhDO> selectListByStocktakingUser(@Param("userId") Long userId);
}
