package com.neway.assets.module.inventory.service.zcpdjh;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhCreateReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhExportReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhPageReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhUpdateReqVO;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 资产盘点计划 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcpdjhService {

    /**
     * 创建资产盘点计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcpdjh(@Valid ZcpdjhCreateReqVO createReqVO);

    /**
     * 更新资产盘点计划
     *
     * @param updateReqVO 更新信息
     */
    void updateZcpdjh(@Valid ZcpdjhUpdateReqVO updateReqVO);

    /**
     * 根据ID批量更新状态
     * @param status 目标状态
     * @param ids 计划主键
     */
    void updateStatusByIds(ZcpdjhStatusEnums status, Long... ids);

    /**
     * 删除资产盘点计划
     *
     * @param id 编号
     */
    void deleteZcpdjh(Long id);

    /**
     * 获得资产盘点计划
     *
     * @param id 编号
     * @return 资产盘点计划
     */
    ZcpdjhDO getZcpdjh(Long id);

    /**
     * 获得资产盘点计划列表
     *
     * @param ids 编号
     * @return 资产盘点计划列表
     */
    List<ZcpdjhDO> getZcpdjhList(Collection<Long> ids);

    /**
     * 获得资产盘点计划分页
     *
     * @param pageReqVO 分页查询
     * @return 资产盘点计划分页
     */
    PageResult<ZcpdjhDO> getZcpdjhPage(ZcpdjhPageReqVO pageReqVO);

    /**
     * 获得资产盘点计划列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产盘点计划列表
     */
    List<ZcpdjhDO> getZcpdjhList(ZcpdjhExportReqVO exportReqVO);

    /**
     * 查询用户ID对应的盘点计划列表
     * @param userId 用户ID
     * @return 资产盘点计划列表
     * <AUTHOR>
     * @since 2023/8/25 15:40
     */
    List<ZcpdjhDO> getListByUserId(Long userId);

    List<ZcpdjhDO> getZcpdjhList();
}
