package com.neway.assets.module.inventory.service.stocktaking;

import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.StocktakingReqVO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.TaskListRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/22 14:50
 **/
public interface StocktakingService {

    List<TaskListRespVO> getTaskList();

    /**
     * 盘点业务方法
     * @param stocktakingReqVO 盘点内容
     * @return boolean
     * <AUTHOR>
     * @since 2023/8/22 16:45
     */
    boolean stocktaking(StocktakingReqVO stocktakingReqVO);
}
