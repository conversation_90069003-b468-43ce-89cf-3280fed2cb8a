package com.neway.assets.module.inventory.controller.admin.stocktaking.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2023/10/9 11:33
 **/
@Schema(description = "管理后台 - 盘点确认列表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StockConfirmPageReqVO extends PageParam {
    private Long planId;

}
