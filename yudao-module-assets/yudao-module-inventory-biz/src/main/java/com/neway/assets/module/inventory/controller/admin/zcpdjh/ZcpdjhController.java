package com.neway.assets.module.inventory.controller.admin.zcpdjh;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.*;
import com.neway.assets.module.inventory.convert.zcpdjh.ZcpdjhConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.service.zcpdjh.ZcpdjhService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 资产盘点计划")
@RestController
@RequestMapping("/inventory/zcpdjh")
@Validated
public class ZcpdjhController {

    @Resource
    private ZcpdjhService zcpdjhService;

    @PostMapping("/create")
    @Operation(summary = "创建资产盘点计划")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:create')")
    public CommonResult<Long> createZcpdjh(@Valid @RequestBody ZcpdjhCreateReqVO createReqVO) {
        return success(zcpdjhService.createZcpdjh(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产盘点计划")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:update')")
    public CommonResult<Boolean> updateZcpdjh(@Valid @RequestBody ZcpdjhUpdateReqVO updateReqVO) {
        zcpdjhService.updateZcpdjh(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产盘点计划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:delete')")
    public CommonResult<Boolean> deleteZcpdjh(@RequestParam("id") Long id) {
        zcpdjhService.deleteZcpdjh(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产盘点计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:query')")
    public CommonResult<ZcpdjhRespVO> getZcpdjh(@RequestParam("id") Long id) {
        ZcpdjhDO zcpdjh = zcpdjhService.getZcpdjh(id);
        return success(ZcpdjhConvert.INSTANCE.convert(zcpdjh));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产盘点计划列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:query')")
    public CommonResult<List<ZcpdjhRespVO>> getZcpdjhList(@RequestParam("ids") Collection<Long> ids) {
        List<ZcpdjhDO> list = zcpdjhService.getZcpdjhList(ids);
        return success(ZcpdjhConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产盘点计划分页")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:query')")
    public CommonResult<PageResult<ZcpdjhRespVO>> getZcpdjhPage(@Valid ZcpdjhPageReqVO pageVO) {
        // 只查看自己主导的计划
        pageVO.setZdren(getLoginUserId());
        PageResult<ZcpdjhDO> pageResult = zcpdjhService.getZcpdjhPage(pageVO);
        return success(ZcpdjhConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产盘点计划 Excel")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcpdjhExcel(@Valid ZcpdjhExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcpdjhDO> list = zcpdjhService.getZcpdjhList(exportReqVO);
        // 导出 Excel
        List<ZcpdjhExcelVO> datas = ZcpdjhConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产盘点计划.xls", "数据", ZcpdjhExcelVO.class, datas);
    }

    @GetMapping("/all")
    @Operation(summary = "获得资产盘点计划列表")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdjh:all')")
    public CommonResult<List<ZcpdjhRespVO>> getZcpdjhList() {
        List<ZcpdjhDO> list = zcpdjhService.getZcpdjhList();
        return success(ZcpdjhConvert.INSTANCE.convertList(list));
    }
}
