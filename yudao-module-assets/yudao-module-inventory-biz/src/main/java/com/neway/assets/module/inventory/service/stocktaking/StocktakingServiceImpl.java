package com.neway.assets.module.inventory.service.stocktaking;

import cn.hutool.core.util.ObjectUtil;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.StocktakingReqVO;
import com.neway.assets.module.inventory.controller.admin.stocktaking.vo.TaskListRespVO;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.ZcpdqdPageItemRespVO;
import com.neway.assets.module.inventory.convert.zcpdjh.ZcpdjhConvert;
import com.neway.assets.module.inventory.convert.zcpdqd.ZcpdqdConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums;
import com.neway.assets.module.inventory.service.zcpdjh.ZcpdjhService;
import com.neway.assets.module.inventory.service.zcpdqd.ZcpdqdService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums.DEPT_CONFIRMING;
import static com.neway.assets.module.inventory.enums.ZcpdjhStatusEnums.IN_PROCESS;

/**
 * <AUTHOR>
 * @since 2023/8/22 14:51
 **/
@RequiredArgsConstructor
@Valid
@Service
public class StocktakingServiceImpl implements StocktakingService {

    private final ZcpdjhService zcpdjhService;
    private final ZcpdqdService zcpdqdService;

    @Override
    public List<TaskListRespVO> getTaskList() {
        Long userId = getLoginUserId();
        List<ZcpdjhDO> planList = zcpdjhService.getListByUserId(userId);
        // 按计划分组当前用户需要盘点的资产
        Map<Long, List<ZcpdqdPageItemRespVO>> assetsMap = zcpdqdService.getListByPlanIds(planList.stream()
                        .map(ZcpdjhDO::getId).toArray(Long[]::new))
                .stream().filter(assets -> Objects.equals(assets.getSpren(), userId))
                .collect(Collectors.groupingBy(ZcpdqdPageItemRespVO::getPdjhhid));
        List<TaskListRespVO> result = new ArrayList<>();
        planList.forEach(plan -> {
            TaskListRespVO taskListRespVO = new TaskListRespVO();
            taskListRespVO.setPlan(ZcpdjhConvert.INSTANCE.convert(plan));
            taskListRespVO.setAssetsList(assetsMap.get(plan.getId()));
            result.add(taskListRespVO);
        });
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean stocktaking(StocktakingReqVO stocktakingReqVO) {
        List<ZcpdqdDO> allInventoryList = zcpdqdService.getListByPlanCode(stocktakingReqVO.getPlanCode(), getLoginUserId());
        stocktakingReqVO.getStocktakingList().forEach(assets -> {
            Optional<ZcpdqdDO> origin = allInventoryList.stream().filter(o -> Objects.equals(assets.getZclb(), o.getZclb())
                            && Objects.equals(assets.getZcxxid(), o.getZcxxid()))
                    .findFirst();
            if (!origin.isPresent()) return;
            ZcpdqdDO zcpdqdDO = origin.get();
            if (Objects.nonNull(zcpdqdDO.getSprq())) return;
            zcpdqdDO.setYkm(assets.getYkm()).setSprq(LocalDate.now());
            // 更新盘点清单
            zcpdqdService.updateZcpdqd(ZcpdqdConvert.INSTANCE.convert2Update(zcpdqdDO));
        });
        // 判断盘点清单的整体情况，更新盘点计划状态
        List<ZcpdqdDO> assetsList = zcpdqdService.getListByPlanCode(stocktakingReqVO.getPlanCode());
        ZcpdjhStatusEnums newStatus = assetsList.stream().anyMatch(assets -> ObjectUtil.isEmpty(assets.getSprq()))
                ? IN_PROCESS : DEPT_CONFIRMING;
        zcpdjhService.updateStatusByIds(newStatus, assetsList.stream().map(ZcpdqdDO::getPdjhhid).distinct().toArray(Long[]::new));
        return true;
    }
}
