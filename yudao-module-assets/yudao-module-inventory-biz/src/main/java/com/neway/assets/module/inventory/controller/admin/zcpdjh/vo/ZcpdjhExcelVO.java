package com.neway.assets.module.inventory.controller.admin.zcpdjh.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 资产盘点计划 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcpdjhExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("盘点计划号")
    private String pdjhh;

    @ExcelProperty("计划盘点日期")
    private LocalDate jhrq;

    @ExcelProperty("盘点主导部门")
    private Long zdbm;

    @ExcelProperty("盘点主导人")
    private Long zdren;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("计划状态")
    private String status;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
