package com.neway.assets.module.inventory.controller.admin.zcpdqd.vo;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产盘点清单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcpdqdPageReqVO extends PageParam {

    @Schema(description = "盘点计划号id（op_zcpdjh->id）", example = "11562")
    private Long pdjhhid;

    @Schema(description = "资产类别（0：整机   1:零部件）")
    private Integer zclb;

    @Schema(description = "资产id（mt_zczsj->id ,mt_zzcxx->id）", example = "14725")
    private Long zcxxid;

    @Schema(description = "盘点数量")
    private BigDecimal pdsl;

    @Schema(description = "计量单位（bs_jldw-->id）")
    private Long jldw;

    @Schema(description = "实盘数量")
    private BigDecimal spsl;

    @Schema(description = "实盘计量单位（bs_jldw-->id）")
    private Long spjldw;

    @Schema(description = "盈亏码（-1：盘亏   1：盘盈）")
    private Integer ykm;

    @Schema(description = "确认码（0：未确认   1：确认   确认后不可再更改）")
    private Integer qrm;

    @Schema(description = "实盘日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] sprq;

    @Schema(description = "实际盘点人(人力资源-->id)")
    private Long spren;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
