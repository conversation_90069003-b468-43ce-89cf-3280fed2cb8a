package com.neway.assets.module.inventory.controller.admin.zcpdjh.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产盘点计划 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcpdjhRespVO extends ZcpdjhBaseVO {

    @Schema(description = "自增长id", required = true, example = "23994")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
