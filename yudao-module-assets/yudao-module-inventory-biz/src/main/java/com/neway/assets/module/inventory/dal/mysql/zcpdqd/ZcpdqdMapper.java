package com.neway.assets.module.inventory.dal.mysql.zcpdqd;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.*;

/**
 * 资产盘点清单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcpdqdMapper extends BaseMapperX<ZcpdqdDO> {

    default PageResult<ZcpdqdDO> selectPage(ZcpdqdPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcpdqdDO>()
                .eqIfPresent(ZcpdqdDO::getPdjhhid, reqVO.getPdjhhid())
                .eqIfPresent(ZcpdqdDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcpdqdDO::getZcxxid, reqVO.getZcxxid())
                .eqIfPresent(ZcpdqdDO::getPdsl, reqVO.getPdsl())
                .eqIfPresent(ZcpdqdDO::getJldw, reqVO.getJldw())
                .eqIfPresent(ZcpdqdDO::getSpsl, reqVO.getSpsl())
                .eqIfPresent(ZcpdqdDO::getSpjldw, reqVO.getSpjldw())
                .eqIfPresent(ZcpdqdDO::getYkm, reqVO.getYkm())
                .eqIfPresent(ZcpdqdDO::getQrm, reqVO.getQrm())
                .betweenIfPresent(ZcpdqdDO::getSprq, reqVO.getSprq())
                .eqIfPresent(ZcpdqdDO::getSpren, reqVO.getSpren())
                .likeIfPresent(ZcpdqdDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcpdqdDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcpdqdDO::getId));
    }

    default List<ZcpdqdDO> selectList(ZcpdqdExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcpdqdDO>()
                .eqIfPresent(ZcpdqdDO::getPdjhhid, reqVO.getPdjhhid())
                .eqIfPresent(ZcpdqdDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcpdqdDO::getZcxxid, reqVO.getZcxxid())
                .eqIfPresent(ZcpdqdDO::getPdsl, reqVO.getPdsl())
                .eqIfPresent(ZcpdqdDO::getJldw, reqVO.getJldw())
                .eqIfPresent(ZcpdqdDO::getSpsl, reqVO.getSpsl())
                .eqIfPresent(ZcpdqdDO::getSpjldw, reqVO.getSpjldw())
                .eqIfPresent(ZcpdqdDO::getYkm, reqVO.getYkm())
                .eqIfPresent(ZcpdqdDO::getQrm, reqVO.getQrm())
                .betweenIfPresent(ZcpdqdDO::getSprq, reqVO.getSprq())
                .eqIfPresent(ZcpdqdDO::getSpren, reqVO.getSpren())
                .likeIfPresent(ZcpdqdDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcpdqdDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcpdqdDO::getId));
    }

}
