package com.neway.assets.module.inventory.controller.admin.zcpdjh.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 资产盘点计划更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcpdjhUpdateReqVO extends ZcpdjhBaseVO {

    @Schema(description = "自增长id", required = true, example = "23994")
    @NotNull(message = "自增长id不能为空")
    private Long id;


}
