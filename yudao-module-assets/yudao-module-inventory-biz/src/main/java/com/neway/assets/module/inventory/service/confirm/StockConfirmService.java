package com.neway.assets.module.inventory.service.confirm;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.ZcpdjhPageReqVO;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.ZcpdqdPageItemRespVO;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;

import java.util.Collection;
import java.util.List;

/**
 * 盘点确认业务接口
 * <AUTHOR>
 * @since 2023/10/8 8:38
 **/
public interface StockConfirmService {

    /**
     * 获取确认清单
     * @param planId 盘点计划ID
     * @return 确认清单
     */
    List<ZcpdqdPageItemRespVO> genConfirmList(Long... planId);

    /**
     * 分页获取待确认的计划
     * @param pageVO 参数实体类
     * @return 分页列表
     */
    PageResult<ZcpdjhDO> pagePlanTobeConfirm(ZcpdjhPageReqVO pageVO);

    /**
     * 确认盘点清单
     * @param planDetailIds 盘点清单ID列表
     */
    void confirmStock(List<Long> planDetailIds);

    /**
     * 取消确认
     * @param planDetailIds 盘点清单ID集合
     */
    void confirmCancel(List<Long> planDetailIds);

    /**
     * 部门负责人退回盘点计划
     * @param planIds 盘点计划ID
     * @return 退回结果
     */
    boolean deptReturnBackPlans(Collection<Long> planIds);

    /**
     * 计划资管员退回盘点计划
     * @param planIds 盘点计划ID
     * @return 退回结果
     */
    boolean adminReturnBackPlans(Collection<Long> planIds);

    /**
     * 计划资管员确认盘点计划
     * @param planIds 盘点计划ID
     * @return 退回结果
     */
    boolean adminConfirmPlans(Collection<Long> planIds);
}
