package com.neway.assets.module.inventory.controller.admin.zcpdqd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
* 资产盘点清单 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcpdqdBaseVO {

    @Schema(description = "盘点计划号id", required = true, example = "11562")
    @NotNull(message = "盘点计划号不能为空")
    private Long pdjhhid;

    @Schema(description = "资产类别", required = true)
    @NotNull(message = "资产类别不能为空")
    private Integer zclb;

    @Schema(description = "资产id", required = true, example = "14725")
    @NotNull(message = "资产=不能为空")
    private Long zcxxid;

    @Schema(description = "盘点数量")
    private BigDecimal pdsl;

    @Schema(description = "计量单位")
    private Long jldw;

    @Schema(description = "实盘数量")
    private BigDecimal spsl;

    @Schema(description = "实盘计量单位")
    private Long spjldw;

    @Schema(description = "盈亏码")
    private Integer ykm;

    @Schema(description = "确认码")
    private Integer qrm;

    @Schema(description = "实盘日期")
    private LocalDate sprq;

    @Schema(description = "实际盘点人(人力资源-->id)")
    private Long spren;

    @Schema(description = "备注")
    private String bz;

}
