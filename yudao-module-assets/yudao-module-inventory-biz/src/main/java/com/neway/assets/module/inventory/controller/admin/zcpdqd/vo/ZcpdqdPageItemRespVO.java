package com.neway.assets.module.inventory.controller.admin.zcpdqd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2023/7/20 15:03
 **/
@Schema(description = "管理后台 - 资产盘点清单详细数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcpdqdPageItemRespVO extends ZcpdqdRespVO{
    @Schema(description = "盘点计划号")
    private String planCode;
    @Schema(description = "资产名称")
    private String assetsName;
    @Schema(description = "盘点单位")
    private String unit;


}
