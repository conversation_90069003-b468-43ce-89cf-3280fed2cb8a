package com.neway.assets.module.inventory.controller.admin.zcpdqd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 资产盘点清单更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcpdqdUpdateReqVO extends ZcpdqdBaseVO {

    @Schema(description = "自增长id", required = true, example = "1705")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
