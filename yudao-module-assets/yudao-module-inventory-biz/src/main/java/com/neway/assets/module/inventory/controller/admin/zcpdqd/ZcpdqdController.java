package com.neway.assets.module.inventory.controller.admin.zcpdqd;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.*;
import com.neway.assets.module.inventory.convert.zcpdqd.ZcpdqdConvert;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import com.neway.assets.module.inventory.service.zcpdqd.ZcpdqdService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产盘点清单")
@RestController
@RequestMapping("/inventory/zcpdqd")
@Validated
public class ZcpdqdController {

    @Resource
    private ZcpdqdService zcpdqdService;

    @PostMapping("/create")
    @Operation(summary = "创建资产盘点清单")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:create')")
    public CommonResult<Long> createZcpdqd(@Valid @RequestBody ZcpdqdCreateReqVO createReqVO) {
        return success(zcpdqdService.createZcpdqd(createReqVO));
    }

    @PostMapping("/create-list")
    @Operation(summary = "创建资产盘点清单列表")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:create')")
    public CommonResult<Boolean> createZcpdqdList(@Valid @RequestBody List<ZcpdqdCreateReqVO> createReqVOs) {
        zcpdqdService.createZcpdqdList(createReqVOs);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产盘点清单")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:update')")
    public CommonResult<Boolean> updateZcpdqd(@Valid @RequestBody ZcpdqdUpdateReqVO updateReqVO) {
        zcpdqdService.updateZcpdqd(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产盘点清单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:delete')")
    public CommonResult<Boolean> deleteZcpdqd(@RequestParam("id") Long id) {
        zcpdqdService.deleteZcpdqd(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产盘点清单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:query')")
    public CommonResult<ZcpdqdRespVO> getZcpdqd(@RequestParam("id") Long id) {
        ZcpdqdDO zcpdqd = zcpdqdService.getZcpdqd(id);
        return success(ZcpdqdConvert.INSTANCE.convert(zcpdqd));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产盘点清单列表")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:query')")
    public CommonResult<List<ZcpdqdRespVO>> getZcpdqdList(@Valid ZcpdqdExportReqVO reqVO) {
        List<ZcpdqdDO> list = zcpdqdService.getZcpdqdList(reqVO);
        return success(ZcpdqdConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获得资产盘点清单列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:query')")
    public CommonResult<List<ZcpdqdRespVO>> getListAllSimple(@Valid ZcpdqdExportReqVO reqVO) {
        List<ZcpdqdDO> list = zcpdqdService.getZcpdqdList(reqVO);

        return success(ZcpdqdConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产盘点清单分页")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:query')")
    public CommonResult<PageResult<ZcpdqdPageItemRespVO>> getZcpdqdPage(@Valid ZcpdqdPageReqVO pageVO) {
        PageResult<ZcpdqdPageItemRespVO> pageResult = zcpdqdService.getZcpdqdPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产盘点清单 Excel")
    @PreAuthorize("@ss.hasPermission('inventory:zcpdqd:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcpdqdExcel(@Valid ZcpdqdExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<ZcpdqdDO> list = zcpdqdService.getZcpdqdList(exportReqVO);
        // 导出 Excel
        List<ZcpdqdExcelVO> datas = ZcpdqdConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产盘点清单.xls", "数据", ZcpdqdExcelVO.class, datas);
    }

}
