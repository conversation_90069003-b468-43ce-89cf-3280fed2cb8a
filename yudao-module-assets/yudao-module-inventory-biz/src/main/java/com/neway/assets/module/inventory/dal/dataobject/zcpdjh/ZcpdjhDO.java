package com.neway.assets.module.inventory.dal.dataobject.zcpdjh;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;

/**
 * 资产盘点计划 DO
 *
 * <AUTHOR>
 */
@TableName(value = "op_zcpdjh", schema = "gdzc")
@KeySequence("op_zcpdjh_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcpdjhDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 盘点计划号
     */
    private String pdjhh;
    /**
     * 计划盘点日期
     */
    private LocalDate jhrq;
    /**
     * 盘点主导部门（组织结构-->id）
     */
    private Long zdbm;
    /**
     * 盘点主导人（人力资源-->id）
     */
    private Long zdren;
    /**
     * 盘点类型
     */
    private Integer pdlx;
    /**
     * 备注
     */
    private String bz;
    /**
     * 盘点状态
     */
    private Integer status;

}
