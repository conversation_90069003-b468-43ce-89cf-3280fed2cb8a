-- 将该建表 SQL 语句，添加到 yudao-module-inventory-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "op_zcpdjh" (
   "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
   "pdjhh" varchar NOT NULL,
   "jhrq" varchar,
   "zdbm" int,
   "zdren" int,
   "bz" varchar,
   "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
   "creator" varchar DEFAULT '',
   "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   "updater" varchar DEFAULT '',
   "deleted" bit NOT NULL DEFAULT FALSE,
   PRIMARY KEY ("id")
) COMMENT '资产盘点计划';

-- 将该建表 SQL 语句，添加到 yudao-module-inventory-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "op_zcpdqd" (
   "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
   "pdjhhid" bigint NOT NULL,
   "zclb" int NOT NULL,
   "zcxxid" bigint NOT NULL,
   "pdsl" varchar,
   "jldw" bigint,
   "spsl" varchar,
   "spjldw" bigint,
   "ykm" int,
   "qrm" int,
   "sprq" varchar,
   "spren" bigint,
   "bz" varchar,
   "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
   "creator" varchar DEFAULT '',
   "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   "updater" varchar DEFAULT '',
   "deleted" bit NOT NULL DEFAULT FALSE,
   PRIMARY KEY ("id")
) COMMENT '资产盘点清单';