package com.neway.assets.module.inventory.service.zcpdqd;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.inventory.controller.admin.zcpdqd.vo.*;
import com.neway.assets.module.inventory.dal.dataobject.zcpdqd.ZcpdqdDO;
import com.neway.assets.module.inventory.dal.mysql.zcpdqd.ZcpdqdMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenDate;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.inventory.enums.ErrorCodeConstants.ZCPDQD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcpdqdServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcpdqdServiceImpl.class)
public class ZcpdqdServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcpdqdServiceImpl zcpdqdService;

    @Resource
    private ZcpdqdMapper zcpdqdMapper;

    @Test
    public void testCreateZcpdqd_success() {
        // 准备参数
        ZcpdqdCreateReqVO reqVO = randomPojo(ZcpdqdCreateReqVO.class);

        // 调用
        Long zcpdqdId = zcpdqdService.createZcpdqd(reqVO);
        // 断言
        assertNotNull(zcpdqdId);
        // 校验记录的属性是否正确
        ZcpdqdDO zcpdqd = zcpdqdMapper.selectById(zcpdqdId);
        assertPojoEquals(reqVO, zcpdqd);
    }

    @Test
    public void testUpdateZcpdqd_success() {
        // mock 数据
        ZcpdqdDO dbZcpdqd = randomPojo(ZcpdqdDO.class);
        zcpdqdMapper.insert(dbZcpdqd);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcpdqdUpdateReqVO reqVO = randomPojo(ZcpdqdUpdateReqVO.class, o -> {
            o.setId(dbZcpdqd.getId()); // 设置更新的 ID
        });

        // 调用
        zcpdqdService.updateZcpdqd(reqVO);
        // 校验是否更新正确
        ZcpdqdDO zcpdqd = zcpdqdMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcpdqd);
    }

    @Test
    public void testUpdateZcpdqd_notExists() {
        // 准备参数
        ZcpdqdUpdateReqVO reqVO = randomPojo(ZcpdqdUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcpdqdService.updateZcpdqd(reqVO), ZCPDQD_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcpdqd_success() {
        // mock 数据
        ZcpdqdDO dbZcpdqd = randomPojo(ZcpdqdDO.class);
        zcpdqdMapper.insert(dbZcpdqd);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcpdqd.getId();

        // 调用
        zcpdqdService.deleteZcpdqd(id);
       // 校验数据不存在了
       assertNull(zcpdqdMapper.selectById(id));
    }

    @Test
    public void testDeleteZcpdqd_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcpdqdService.deleteZcpdqd(id), ZCPDQD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcpdqdPage() {
       // mock 数据
       ZcpdqdDO dbZcpdqd = randomPojo(ZcpdqdDO.class, o -> { // 等会查询到
           o.setPdjhhid(null);
           o.setZclb(null);
           o.setZcxxid(null);
           o.setPdsl(null);
           o.setJldw(null);
           o.setSpsl(null);
           o.setSpjldw(null);
           o.setYkm(null);
           o.setQrm(null);
           o.setSprq(null);
           o.setSpren(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcpdqdMapper.insert(dbZcpdqd);
       // 测试 pdjhhid 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setPdjhhid(null)));
       // 测试 zclb 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setZclb(null)));
       // 测试 zcxxid 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setZcxxid(null)));
       // 测试 pdsl 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setPdsl(null)));
       // 测试 jldw 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setJldw(null)));
       // 测试 spsl 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSpsl(null)));
       // 测试 spjldw 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSpjldw(null)));
       // 测试 ykm 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setYkm(null)));
       // 测试 qrm 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setQrm(null)));
       // 测试 sprq 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSprq(null)));
       // 测试 spren 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSpren(null)));
       // 测试 bz 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setCreateTime(null)));
       // 准备参数
       ZcpdqdPageReqVO reqVO = new ZcpdqdPageReqVO();
       reqVO.setPdjhhid(null);
       reqVO.setZclb(null);
       reqVO.setZcxxid(null);
       reqVO.setPdsl(null);
       reqVO.setJldw(null);
       reqVO.setSpsl(null);
       reqVO.setSpjldw(null);
       reqVO.setYkm(null);
       reqVO.setQrm(null);
       reqVO.setSprq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setSpren(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcpdqdPageItemRespVO> pageResult = zcpdqdService.getZcpdqdPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcpdqd, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcpdqdList() {
       // mock 数据
       ZcpdqdDO dbZcpdqd = randomPojo(ZcpdqdDO.class, o -> { // 等会查询到
           o.setPdjhhid(null);
           o.setZclb(null);
           o.setZcxxid(null);
           o.setPdsl(null);
           o.setJldw(null);
           o.setSpsl(null);
           o.setSpjldw(null);
           o.setYkm(null);
           o.setQrm(null);
           o.setSprq(null);
           o.setSpren(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcpdqdMapper.insert(dbZcpdqd);
       // 测试 pdjhhid 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setPdjhhid(null)));
       // 测试 zclb 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setZclb(null)));
       // 测试 zcxxid 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setZcxxid(null)));
       // 测试 pdsl 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setPdsl(null)));
       // 测试 jldw 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setJldw(null)));
       // 测试 spsl 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSpsl(null)));
       // 测试 spjldw 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSpjldw(null)));
       // 测试 ykm 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setYkm(null)));
       // 测试 qrm 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setQrm(null)));
       // 测试 sprq 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSprq(null)));
       // 测试 spren 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setSpren(null)));
       // 测试 bz 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcpdqdMapper.insert(cloneIgnoreId(dbZcpdqd, o -> o.setCreateTime(null)));
       // 准备参数
       ZcpdqdExportReqVO reqVO = new ZcpdqdExportReqVO();
       reqVO.setPdjhhid(null);
       reqVO.setZclb(null);
       reqVO.setZcxxid(null);
       reqVO.setPdsl(null);
       reqVO.setJldw(null);
       reqVO.setSpsl(null);
       reqVO.setSpjldw(null);
       reqVO.setYkm(null);
       reqVO.setQrm(null);
       reqVO.setSprq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setSpren(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcpdqdDO> list = zcpdqdService.getZcpdqdList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcpdqd, list.get(0));
    }

}
