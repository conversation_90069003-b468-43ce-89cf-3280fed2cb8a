package com.neway.assets.module.inventory.service.zcpdjh;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;

import com.neway.assets.module.inventory.controller.admin.zcpdjh.vo.*;
import com.neway.assets.module.inventory.dal.dataobject.zcpdjh.ZcpdjhDO;
import com.neway.assets.module.inventory.dal.mysql.zcpdjh.ZcpdjhMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.neway.assets.module.inventory.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.*;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link ZcpdjhServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcpdjhServiceImpl.class)
public class ZcpdjhServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcpdjhServiceImpl zcpdjhService;

    @Resource
    private ZcpdjhMapper zcpdjhMapper;

    @Test
    public void testCreateZcpdjh_success() {
        // 准备参数
        ZcpdjhCreateReqVO reqVO = randomPojo(ZcpdjhCreateReqVO.class);

        // 调用
        Long zcpdjhId = zcpdjhService.createZcpdjh(reqVO);
        // 断言
        assertNotNull(zcpdjhId);
        // 校验记录的属性是否正确
        ZcpdjhDO zcpdjh = zcpdjhMapper.selectById(zcpdjhId);
        assertPojoEquals(reqVO, zcpdjh);
    }

    @Test
    public void testUpdateZcpdjh_success() {
        // mock 数据
        ZcpdjhDO dbZcpdjh = randomPojo(ZcpdjhDO.class);
        zcpdjhMapper.insert(dbZcpdjh);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcpdjhUpdateReqVO reqVO = randomPojo(ZcpdjhUpdateReqVO.class, o -> {
            o.setId(dbZcpdjh.getId()); // 设置更新的 ID
        });

        // 调用
        zcpdjhService.updateZcpdjh(reqVO);
        // 校验是否更新正确
        ZcpdjhDO zcpdjh = zcpdjhMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcpdjh);
    }

    @Test
    public void testUpdateZcpdjh_notExists() {
        // 准备参数
        ZcpdjhUpdateReqVO reqVO = randomPojo(ZcpdjhUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcpdjhService.updateZcpdjh(reqVO), ZCPDJH_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcpdjh_success() {
        // mock 数据
        ZcpdjhDO dbZcpdjh = randomPojo(ZcpdjhDO.class);
        zcpdjhMapper.insert(dbZcpdjh);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcpdjh.getId();

        // 调用
        zcpdjhService.deleteZcpdjh(id);
       // 校验数据不存在了
       assertNull(zcpdjhMapper.selectById(id));
    }

    @Test
    public void testDeleteZcpdjh_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcpdjhService.deleteZcpdjh(id), ZCPDJH_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcpdjhPage() {
       // mock 数据
       ZcpdjhDO dbZcpdjh = randomPojo(ZcpdjhDO.class, o -> { // 等会查询到
           o.setPdjhh(null);
           o.setJhrq(null);
           o.setZdbm(null);
           o.setZdren(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcpdjhMapper.insert(dbZcpdjh);
       // 测试 pdjhh 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setPdjhh(null)));
       // 测试 jhrq 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setJhrq(null)));
       // 测试 zdbm 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setZdbm(null)));
       // 测试 zdren 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setZdren(null)));
       // 测试 bz 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setCreateTime(null)));
       // 准备参数
       ZcpdjhPageReqVO reqVO = new ZcpdjhPageReqVO();
       reqVO.setPdjhh(null);
       reqVO.setJhrq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setZdbm(null);
       reqVO.setZdren(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcpdjhDO> pageResult = zcpdjhService.getZcpdjhPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcpdjh, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcpdjhList() {
       // mock 数据
       ZcpdjhDO dbZcpdjh = randomPojo(ZcpdjhDO.class, o -> { // 等会查询到
           o.setPdjhh(null);
           o.setJhrq(null);
           o.setZdbm(null);
           o.setZdren(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcpdjhMapper.insert(dbZcpdjh);
       // 测试 pdjhh 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setPdjhh(null)));
       // 测试 jhrq 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setJhrq(null)));
       // 测试 zdbm 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setZdbm(null)));
       // 测试 zdren 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setZdren(null)));
       // 测试 bz 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcpdjhMapper.insert(cloneIgnoreId(dbZcpdjh, o -> o.setCreateTime(null)));
       // 准备参数
       ZcpdjhExportReqVO reqVO = new ZcpdjhExportReqVO();
       reqVO.setPdjhh(null);
       reqVO.setJhrq(buildBetweenDate(2023, 2, 1, 2023, 2, 28));
       reqVO.setZdbm(null);
       reqVO.setZdren(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcpdjhDO> list = zcpdjhService.getZcpdjhList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcpdjh, list.get(0));
    }

}
