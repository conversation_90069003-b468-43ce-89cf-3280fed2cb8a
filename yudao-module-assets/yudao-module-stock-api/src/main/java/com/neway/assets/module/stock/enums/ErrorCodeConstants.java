package com.neway.assets.module.stock.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 资产库存模块错误码枚举类
 * 使用 1-104-000-000 段
 * <AUTHOR>
 * @since 2023/5/5 11:53
 **/
public interface ErrorCodeConstants {

    // ========== 耗材验收记录 1-104-001-000 ==========
    ErrorCode BPYSRK_NOT_EXISTS = new ErrorCode(1104001000, "耗材/备品备件验收记录不存在");

    ErrorCode DLJKC_NOT_EXISTS = new ErrorCode(1104002000, "刀/量具库存不存在");

}
