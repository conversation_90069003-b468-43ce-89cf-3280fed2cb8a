package com.neway.assets.module.management.api.zczsj;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;

import java.util.*;

/**
 * 资产主数据API接口定义
 * <AUTHOR>
 * @since 2023/5/25 14:49
 **/
public interface ZczsjApi {

    List<ZczsjRespDTO> getZczsjList(Collection<Long> ids);

    default Map<Long, ZczsjRespDTO> getZczsjMap(Set<Long> ids) {
        if (Objects.isNull(ids) || ids.isEmpty()) return Collections.emptyMap();
        List<ZczsjRespDTO> bzxxList = getZczsjList(ids);
        return CollectionUtils.convertMap(bzxxList, ZczsjRespDTO::getId);
    }

    List<ZczsjRespDTO> getZczsjListByTypeId(Collection<Long> typeIds);
}
