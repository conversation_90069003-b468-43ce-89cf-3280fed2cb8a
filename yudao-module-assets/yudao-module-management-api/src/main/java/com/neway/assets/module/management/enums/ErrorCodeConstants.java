package com.neway.assets.module.management.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 资产信息管理模块错误码枚举类
 * 使用 1-101-000-000 段
 * <AUTHOR>
 * @since 2023/5/5 11:53
 **/
public interface ErrorCodeConstants {

    // ========== 资产主数据 1-101-001-000 ==========
    ErrorCode ZCZSJ_NOT_EXISTS = new ErrorCode(1101001000, "资产主数据不存在");

    // ========== 子资产数据 1-101-002-000 ==========
    ErrorCode ZZCXX_NOT_EXISTS = new ErrorCode(1101002000, "子资产不存在");

    // ========== 资产异动记录 1-101-003-000 ==========
    ErrorCode ZCYDJL_NOT_EXISTS = new ErrorCode(1101003000, "资产异动记录不存在");
    ErrorCode ZCLB_NOT_EXISTS = new ErrorCode(1101003001, "非法的资产类别");
    ErrorCode ADJUST_TYPE_NOT_EXISTS = new ErrorCode(1101003002, "资产异动类型不存在");

    // ========== 耗材/备品备件主数据 1-101-004-000 ==========
    ErrorCode HBJZSJ_NOT_EXISTS = new ErrorCode(1101004000, "耗材/备品备件主数据不存在");

    ErrorCode DLJZSJ_NOT_EXISTS = new ErrorCode(1101005000, "刀/量具主数据不存在");

    // ========== 资产RFID关联 1-101-006-000  ==========
    ErrorCode ZCRFID_NOT_EXISTS = new ErrorCode(1101006000, "资产RFID关联不存在");
    ErrorCode NEW_RFID_EXISTS = new ErrorCode(1101006001, "新RFID已被使用");

}
