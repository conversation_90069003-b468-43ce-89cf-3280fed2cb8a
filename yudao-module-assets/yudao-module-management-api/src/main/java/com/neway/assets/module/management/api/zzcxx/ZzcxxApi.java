package com.neway.assets.module.management.api.zzcxx;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.management.api.zzcxx.dto.ZzcxxRespDTO;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/5/25 17:10
 **/
public interface ZzcxxApi {

    List<ZzcxxRespDTO> getZzcxxList(Collection<Long> ids);

    default Map<Long, ZzcxxRespDTO> getZzcxxjMap(Set<Long> ids) {
        if (Objects.isNull(ids) || ids.isEmpty()) return Collections.emptyMap();
        List<ZzcxxRespDTO> bzxxList = getZzcxxList(ids);
        return CollectionUtils.convertMap(bzxxList, ZzcxxRespDTO::getId);
    }
}
