package com.neway.assets.module.info.service.secondzcfl;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.*;
import com.neway.assets.module.info.dal.dataobject.secondzcfl.SecondZcflDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产二级分类 Service 接口
 *
 * <AUTHOR>
 */
public interface SecondZcflService {

    /**
     * 创建资产二级分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSecondZcfl(@Valid SecondZcflCreateReqVO createReqVO);

    /**
     * 更新资产二级分类
     *
     * @param updateReqVO 更新信息
     */
    void updateSecondZcfl(@Valid SecondZcflUpdateReqVO updateReqVO);

    /**
     * 删除资产二级分类
     *
     * @param id 编号
     */
    void deleteSecondZcfl(Long id);

    /**
     * 获得资产二级分类
     *
     * @param id 编号
     * @return 资产二级分类
     */
    SecondZcflDO getSecondZcfl(Long id);

    /**
     * 获得资产二级分类列表
     *
     * @param ids 编号
     * @return 资产二级分类列表
     */
    List<SecondZcflDO> getSecondZcflList(Collection<Long> ids);

    /**
     * 获得资产二级分类分页
     *
     * @param pageReqVO 分页查询
     * @return 资产二级分类分页
     */
    PageResult<SecondZcflDO> getSecondZcflPage(SecondZcflPageReqVO pageReqVO);

    /**
     * 获得资产二级分类列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产二级分类列表
     */
    List<SecondZcflDO> getSecondZcflList(SecondZcflExportReqVO exportReqVO);

}
