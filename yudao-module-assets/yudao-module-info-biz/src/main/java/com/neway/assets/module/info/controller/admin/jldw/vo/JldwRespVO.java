package com.neway.assets.module.info.controller.admin.jldw.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 计量单位 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JldwRespVO extends JldwBaseVO {


    @Schema(description = "自增长id", required = true, example = "3778")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
