package com.neway.assets.module.info.controller.admin.jldwdz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* 计量单位转换对照 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class JldwdzBaseVO {

    @Schema(description = "成本中心")
    private String cbzx;

    @Schema(description = "源计量单位", required = true)
    @NotNull(message = "源计量单位不能为空")
    private Long yjldw;

    @Schema(description = "源数量", required = true)
    @NotNull(message = "源数量不能为空")
    private BigDecimal yuansl;

    @Schema(description = "转换计量单位", required = true)
    @NotNull(message = "转换计量单位不能为空")
    private Long zhdw;

    @Schema(description = "转换数量", required = true)
    @NotNull(message = "转换数量不能为空")
    private BigDecimal zhsl;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
