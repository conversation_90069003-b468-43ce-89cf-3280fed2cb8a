package com.neway.assets.module.info.service.zcfl;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.zcfl.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcfl.ZcflDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcflService {

    /**
     * 创建资产分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcfl(@Valid ZcflCreateReqVO createReqVO);

    /**
     * 更新资产分类
     *
     * @param updateReqVO 更新信息
     */
    void updateZcfl(@Valid ZcflUpdateReqVO updateReqVO);

    /**
     * 删除资产分类
     *
     * @param id 编号
     */
    void deleteZcfl(Long id);

    /**
     * 获得资产分类
     *
     * @param id 编号
     * @return 资产分类
     */
    ZcflDO getZcfl(Long id);

    /**
     * 获得资产分类列表
     *
     * @param ids 编号
     * @return 资产分类列表
     */
    List<ZcflDO> getZcflList(Collection<Long> ids);

    /**
     * 获得资产分类分页
     *
     * @param pageReqVO 分页查询
     * @return 资产分类分页
     */
    PageResult<ZcflDO> getZcflPage(ZcflPageReqVO pageReqVO);

    /**
     * 获得资产分类列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产分类列表
     */
    List<ZcflDO> getZcflList(ZcflExportReqVO exportReqVO);

}
