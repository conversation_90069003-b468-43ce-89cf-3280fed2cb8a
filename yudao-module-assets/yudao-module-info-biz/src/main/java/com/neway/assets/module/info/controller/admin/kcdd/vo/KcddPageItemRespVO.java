package com.neway.assets.module.info.controller.admin.kcdd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2023/4/25 15:39
 **/
@Schema(description = "管理后台 - 库存地点分页时的信息 Response VO,多了工厂信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class KcddPageItemRespVO extends KcddRespVO {

    private Factory factory;

    @Schema(description = "工厂")
    @Data
    public static class Factory {
        @Schema(description = "工厂编号", example = "1")
        private Long id;

        @Schema(description = "工厂名称", example = "深圳总公司")
        private String name;

        @Schema(description = "公司编号", example = "1")
        private Long companyId;

        @Schema(description = "公司名称", example = "深圳总公司")
        private String companyName;

    }

}
