package com.neway.assets.module.info.dal.dataobject.jldwdz;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 计量单位转换对照 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_jldwdz", schema = "gdzc")
@KeySequence("bs_jldwdz_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JldwdzDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 成本中心
     */
    private String cbzx;
    /**
     * 源计量单位
     */
    private Long yjldw;
    /**
     * 源数量
     */
    private BigDecimal yuansl;
    /**
     * 转换计量单位
     */
    private Long zhdw;
    /**
     * 转换数量
     */
    private BigDecimal zhsl;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
