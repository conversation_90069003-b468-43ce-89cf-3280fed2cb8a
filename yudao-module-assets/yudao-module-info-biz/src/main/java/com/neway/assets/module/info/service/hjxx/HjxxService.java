package com.neway.assets.module.info.service.hjxx;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.hjxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.hjxx.HjxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 货架 Service 接口
 *
 * <AUTHOR>
 */
public interface HjxxService {

    /**
     * 创建货架
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHjxx(@Valid HjxxCreateReqVO createReqVO);

    /**
     * 更新货架
     *
     * @param updateReqVO 更新信息
     */
    void updateHjxx(@Valid HjxxUpdateReqVO updateReqVO);

    /**
     * 删除货架
     *
     * @param id 编号
     */
    void deleteHjxx(Long id);

    /**
     * 获得货架
     *
     * @param id 编号
     * @return 货架
     */
    HjxxDO getHjxx(Long id);

    /**
     * 获得货架列表
     *
     * @param ids 编号
     * @return 货架列表
     */
    List<HjxxDO> getHjxxList(Collection<Long> ids);

    /**
     * 获得货架分页
     *
     * @param pageReqVO 分页查询
     * @return 货架分页
     */
    PageResult<HjxxDO> getHjxxPage(HjxxPageReqVO pageReqVO);

    /**
     * 获得货架列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 货架列表
     */
    List<HjxxDO> getHjxxList(HjxxExportReqVO exportReqVO);

}
