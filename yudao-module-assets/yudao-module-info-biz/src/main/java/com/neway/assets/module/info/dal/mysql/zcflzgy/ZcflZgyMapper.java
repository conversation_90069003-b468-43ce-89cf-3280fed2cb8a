package com.neway.assets.module.info.dal.mysql.zcflzgy;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.zcflzgy.ZcflZgyDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.*;

/**
 * 资产分类资管员关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcflZgyMapper extends BaseMapperX<ZcflZgyDO> {

    default PageResult<ZcflZgyDO> selectPage(ZcflZgyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcflZgyDO>()
                .eqIfPresent(ZcflZgyDO::getGs, reqVO.getGs())
                .eqIfPresent(ZcflZgyDO::getZcflId, reqVO.getZcflId())
                .eqIfPresent(ZcflZgyDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ZcflZgyDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcflZgyDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcflZgyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcflZgyDO::getId));
    }

    default List<ZcflZgyDO> selectList(ZcflZgyExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcflZgyDO>()
                .eqIfPresent(ZcflZgyDO::getGs, reqVO.getGs())
                .eqIfPresent(ZcflZgyDO::getZcflId, reqVO.getZcflId())
                .eqIfPresent(ZcflZgyDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ZcflZgyDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcflZgyDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcflZgyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcflZgyDO::getId));
    }

}
