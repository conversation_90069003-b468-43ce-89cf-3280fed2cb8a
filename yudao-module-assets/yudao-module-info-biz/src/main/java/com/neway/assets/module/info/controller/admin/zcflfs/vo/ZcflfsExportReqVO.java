package com.neway.assets.module.info.controller.admin.zcflfs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产分类方式 Excel 导出 Request VO，参数和 ZcflfsPageReqVO 是一致的")
@Data
public class ZcflfsExportReqVO {

    @Schema(description = "分类方式说明", required = true)
    private String flfssm;

    @Schema(description = "启用标识（1:启用 ；0:未启用）")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
