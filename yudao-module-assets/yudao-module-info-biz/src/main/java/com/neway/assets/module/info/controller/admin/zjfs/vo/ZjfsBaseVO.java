package com.neway.assets.module.info.controller.admin.zjfs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* 资产折旧方式 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZjfsBaseVO {

    @Schema(description = "资产分类", required = true)
    @NotNull(message = "资产分类不能为空")
    private Long zcfl;

    @Schema(description = "折旧年限")
    private BigDecimal zjnx;

    @Schema(description = "残值率")
    private BigDecimal czl;

    @Schema(description = "是否当月折旧")
    private String sfdyzj;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
