package com.neway.assets.module.info.controller.admin.gcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/4/24 15:07
 **/
@Schema(description = "管理后台 - 工厂分页时的信息 Response VO,多了公司信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GcxxPageItemRespVO extends GcxxRespVO{

    /**
     * 所在公司
     */
    private Company company;

    @Schema(description = "公司")
    @Data
    public static class Company {

        @Schema(description = "公司编号", example = "1")
        private Long id;

        @Schema(description = "公司名称", example = "深圳总公司")
        private String name;

    }
}
