package com.neway.assets.module.info.service.zcflfs;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcflfs.ZcflfsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.zcflfs.ZcflfsConvert;
import com.neway.assets.module.info.dal.mysql.zcflfs.ZcflfsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 资产分类方式 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZcflfsServiceImpl implements ZcflfsService {

    @Resource
    private ZcflfsMapper zcflfsMapper;

    @Override
    public Integer createZcflfs(ZcflfsCreateReqVO createReqVO) {
        // 插入
        ZcflfsDO zcflfs = ZcflfsConvert.INSTANCE.convert(createReqVO);
        zcflfsMapper.insert(zcflfs);
        // 返回
        return zcflfs.getId();
    }

    @Override
    public void updateZcflfs(ZcflfsUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcflfsExists(updateReqVO.getId());
        // 更新
        ZcflfsDO updateObj = ZcflfsConvert.INSTANCE.convert(updateReqVO);
        zcflfsMapper.updateById(updateObj);
    }

    @Override
    public void deleteZcflfs(Integer id) {
        // 校验存在
        validateZcflfsExists(id);
        // 删除
        zcflfsMapper.deleteById(id);
    }

    private void validateZcflfsExists(Integer id) {
        if (zcflfsMapper.selectById(id) == null) {
            throw exception(ZCFLFS_NOT_EXISTS);
        }
    }

    @Override
    public ZcflfsDO getZcflfs(Integer id) {
        return zcflfsMapper.selectById(id);
    }

    @Override
    public List<ZcflfsDO> getZcflfsList(Collection<Integer> ids) {
        return zcflfsMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcflfsDO> getZcflfsPage(ZcflfsPageReqVO pageReqVO) {
        return zcflfsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZcflfsDO> getZcflfsList(ZcflfsExportReqVO exportReqVO) {
        return zcflfsMapper.selectList(exportReqVO);
    }

}
