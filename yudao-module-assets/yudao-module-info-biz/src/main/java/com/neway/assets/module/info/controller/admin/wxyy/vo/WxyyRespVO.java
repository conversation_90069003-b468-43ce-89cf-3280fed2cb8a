package com.neway.assets.module.info.controller.admin.wxyy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 维护/维修原因 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WxyyRespVO extends WxyyBaseVO {

    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
