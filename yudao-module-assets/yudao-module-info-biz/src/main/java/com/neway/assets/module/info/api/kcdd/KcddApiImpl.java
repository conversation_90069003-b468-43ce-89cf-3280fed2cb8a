package com.neway.assets.module.info.api.kcdd;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.kcdd.dto.KcddRespDTO;
import com.neway.assets.module.info.convert.kcdd.KcddConvert;
import com.neway.assets.module.info.dal.dataobject.kcdd.KcddDO;
import com.neway.assets.module.info.service.kcdd.KcddService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/6/13 15:13
 **/
@Service
@RequiredArgsConstructor
public class KcddApiImpl implements KcddApi{

    private final KcddService kcddService;

    @Override
    public List<KcddRespDTO> getKcddList(Set<Long> ids) {
        if (CollectionUtils.isAnyEmpty(ids)) return Collections.emptyList();
        List<KcddDO> kcddList = kcddService.getKcddList(ids);
        return KcddConvert.INSTANCE.converList03(kcddList);
    }
}
