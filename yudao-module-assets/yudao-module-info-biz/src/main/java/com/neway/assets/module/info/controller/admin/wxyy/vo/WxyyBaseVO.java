package com.neway.assets.module.info.controller.admin.wxyy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 维护/维修原因 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class WxyyBaseVO {

    @Schema(description = "维修维护原因描述", required = true)
    @NotNull(message = "维修维护原因描述不能为空")
    private String wxyyms;

    @Schema(description = "启用标识（1:启用 ；0:未启用）")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
