package com.neway.assets.module.info.dal.mysql.gcxx;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.gcxx.vo.*;

/**
 * 工厂信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GcxxMapper extends BaseMapperX<GcxxDO> {

    default PageResult<GcxxDO> selectPage(GcxxPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GcxxDO>()
                .eqIfPresent(GcxxDO::getGs, reqVO.getGs())
                .likeIfPresent(GcxxDO::getGcms, reqVO.getGcms())
                .eqIfPresent(GcxxDO::getDzxx, reqVO.getDzxx())
                .eqIfPresent(GcxxDO::getJwdxx, reqVO.getJwdxx())
                .eqIfPresent(GcxxDO::getQybs, reqVO.getQybs())
                .eqIfPresent(GcxxDO::getBz, reqVO.getBz())
                .betweenIfPresent(GcxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(GcxxDO::getId));
    }

    default List<GcxxDO> selectList(GcxxExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<GcxxDO>()
                .eqIfPresent(GcxxDO::getGs, reqVO.getGs())
                .likeIfPresent(GcxxDO::getGcms, reqVO.getGcms())
                .eqIfPresent(GcxxDO::getDzxx, reqVO.getDzxx())
                .eqIfPresent(GcxxDO::getJwdxx, reqVO.getJwdxx())
                .eqIfPresent(GcxxDO::getQybs, reqVO.getQybs())
                .eqIfPresent(GcxxDO::getBz, reqVO.getBz())
                .betweenIfPresent(GcxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(GcxxDO::getId));
    }

}
