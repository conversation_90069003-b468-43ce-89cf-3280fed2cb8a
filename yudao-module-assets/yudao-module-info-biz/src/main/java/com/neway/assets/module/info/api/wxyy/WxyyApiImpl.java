package com.neway.assets.module.info.api.wxyy;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.wxyy.dto.WxyyRespDTO;
import com.neway.assets.module.info.convert.wxyy.WxyyConvert;
import com.neway.assets.module.info.dal.dataobject.wxyy.WxyyDO;
import com.neway.assets.module.info.service.wxyy.WxyyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 维修原因API接口实现类
 * <AUTHOR>
 * @since 2023/5/25 15:34
 **/
@Service
@RequiredArgsConstructor
public class WxyyApiImpl implements WxyyApi{

    private final WxyyService wxyyService;

    @Override
    public List<WxyyRespDTO> getWxyyList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<WxyyDO> list = wxyyService.getWxyyList(ids);
        return WxyyConvert.INSTANCE.convertList03(list);
    }
}
