package com.neway.assets.module.info.service.hbjfl;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.*;
import com.neway.assets.module.info.dal.dataobject.hbjfl.HbjflDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 耗材/备品备件分类 Service 接口
 *
 * <AUTHOR>
 */
public interface HbjflService {

    /**
     * 创建耗材/备品备件分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHbjfl(@Valid HbjflCreateReqVO createReqVO);

    /**
     * 更新耗材/备品备件分类
     *
     * @param updateReqVO 更新信息
     */
    void updateHbjfl(@Valid HbjflUpdateReqVO updateReqVO);

    /**
     * 删除耗材/备品备件分类
     *
     * @param id 编号
     */
    void deleteHbjfl(Long id);

    /**
     * 获得耗材/备品备件分类
     *
     * @param id 编号
     * @return 耗材/备品备件分类
     */
    HbjflDO getHbjfl(Long id);

    /**
     * 获得耗材/备品备件分类列表
     *
     * @param ids 编号
     * @return 耗材/备品备件分类列表
     */
    List<HbjflDO> getHbjflList(Collection<Long> ids);

    /**
     * 获得耗材/备品备件分类分页
     *
     * @param pageReqVO 分页查询
     * @return 耗材/备品备件分类分页
     */
    PageResult<HbjflDO> getHbjflPage(HbjflPageReqVO pageReqVO);

    /**
     * 获得耗材/备品备件分类列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 耗材/备品备件分类列表
     */
    List<HbjflDO> getHbjflList(HbjflExportReqVO exportReqVO);

}
