package com.neway.assets.module.info.controller.admin.zcflzgy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 资产分类资管员关联更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcflZgyUpdateReqVO extends ZcflZgyBaseVO {

    @Schema(description = "自增长id", required = true, example = "29916")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
