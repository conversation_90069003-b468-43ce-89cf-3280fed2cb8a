package com.neway.assets.module.info.controller.admin.bzxx;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.bzxx.vo.*;
import com.neway.assets.module.info.convert.bzxx.BzxxConvert;
import com.neway.assets.module.info.dal.dataobject.bzxx.BzxxDO;
import com.neway.assets.module.info.service.bzxx.BzxxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 币种信息")
@RestController
@RequestMapping("/info/bzxx")
@Validated
public class BzxxController {

    @Resource
    private BzxxService bzxxService;

    @PostMapping("/create")
    @Operation(summary = "创建币种信息")
    @PreAuthorize("@ss.hasPermission('info:bzxx:create')")
    public CommonResult<Long> createBzxx(@Valid @RequestBody BzxxCreateReqVO createReqVO) {
        return success(bzxxService.createBzxx(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新币种信息")
    @PreAuthorize("@ss.hasPermission('info:bzxx:update')")
    public CommonResult<Boolean> updateBzxx(@Valid @RequestBody BzxxUpdateReqVO updateReqVO) {
        bzxxService.updateBzxx(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除币种信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:bzxx:delete')")
    public CommonResult<Boolean> deleteBzxx(@RequestParam("id") Long id) {
        bzxxService.deleteBzxx(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得币种信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:bzxx:query')")
    public CommonResult<BzxxRespVO> getBzxx(@RequestParam("id") Long id) {
        BzxxDO bzxx = bzxxService.getBzxx(id);
        return success(BzxxConvert.INSTANCE.convert(bzxx));
    }

    @GetMapping("/list")
    @Operation(summary = "获得币种信息列表")
    @PreAuthorize("@ss.hasPermission('info:bzxx:query')")
    public CommonResult<List<BzxxRespVO>> getBzxxList(@Valid BzxxExportReqVO reqVO) {
        List<BzxxDO> list = bzxxService.getBzxxList(reqVO);
        return success(BzxxConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得币种信息分页")
    @PreAuthorize("@ss.hasPermission('info:bzxx:query')")
    public CommonResult<PageResult<BzxxRespVO>> getBzxxPage(@Valid BzxxPageReqVO pageVO) {
        PageResult<BzxxDO> pageResult = bzxxService.getBzxxPage(pageVO);
        return success(BzxxConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出币种信息 Excel")
    @PreAuthorize("@ss.hasPermission('info:bzxx:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBzxxExcel(@Valid BzxxExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<BzxxDO> list = bzxxService.getBzxxList(exportReqVO);
        // 导出 Excel
        List<BzxxExcelVO> datas = BzxxConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "币种信息.xls", "数据", BzxxExcelVO.class, datas);
    }

}
