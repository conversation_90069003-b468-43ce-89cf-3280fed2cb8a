package com.neway.assets.module.info.controller.admin.jldwdz.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 计量单位转换对照分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JldwdzPageReqVO extends PageParam {

    @Schema(description = "成本中心")
    private String cbzx;

    @Schema(description = "源计量单位")
    private Long yjldw;

    @Schema(description = "源数量")
    private BigDecimal yuansl;

    @Schema(description = "转换计量单位")
    private Long zhdw;

    @Schema(description = "转换数量")
    private BigDecimal zhsl;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
