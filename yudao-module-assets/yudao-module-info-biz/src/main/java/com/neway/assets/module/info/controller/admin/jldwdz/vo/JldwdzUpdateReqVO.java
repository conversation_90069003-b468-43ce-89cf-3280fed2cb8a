package com.neway.assets.module.info.controller.admin.jldwdz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 计量单位转换对照更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JldwdzUpdateReqVO extends JldwdzBaseVO {

    @Schema(description = "自增长id", required = true, example = "14804")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
