package com.neway.assets.module.info.service.gcxx;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.gcxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 工厂信息 Service 接口
 *
 * <AUTHOR>
 */
public interface GcxxService {

    /**
     * 创建工厂信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGcxx(@Valid GcxxCreateReqVO createReqVO);

    /**
     * 更新工厂信息
     *
     * @param updateReqVO 更新信息
     */
    void updateGcxx(@Valid GcxxUpdateReqVO updateReqVO);

    /**
     * 删除工厂信息
     *
     * @param id 编号
     */
    void deleteGcxx(Long id);

    /**
     * 获得工厂信息
     *
     * @param id 编号
     * @return 工厂信息
     */
    GcxxDO getGcxx(Long id);

    /**
     * 获得工厂信息列表
     *
     * @param ids 编号
     * @return 工厂信息列表
     */
    List<GcxxDO> getGcxxList(Collection<Long> ids);

    /**
     * 获得工厂信息分页
     *
     * @param pageReqVO 分页查询
     * @return 工厂信息分页
     */
    PageResult<GcxxDO> getGcxxPage(GcxxPageReqVO pageReqVO);

    /**
     * 获得工厂信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 工厂信息列表
     */
    List<GcxxDO> getGcxxList(GcxxExportReqVO exportReqVO);

}
