package com.neway.assets.module.info.service.kcdd;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.neway.assets.module.info.controller.admin.kcdd.vo.*;
import com.neway.assets.module.info.convert.gcxx.GcxxConvert;
import com.neway.assets.module.info.convert.kcdd.KcddConvert;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import com.neway.assets.module.info.dal.dataobject.kcdd.KcddDO;
import com.neway.assets.module.info.dal.mysql.kcdd.KcddMapper;
import com.neway.assets.module.info.service.gcxx.GcxxService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 库存地点 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class KcddServiceImpl implements KcddService {

    @Resource
    private KcddMapper kcddMapper;

    @Resource
    private GcxxService gcxxService;

    @Resource
    private DeptApi deptApi;

    @Override
    public Long createKcdd(KcddCreateReqVO createReqVO) {
        // 插入
        KcddDO kcdd = KcddConvert.INSTANCE.convert(createReqVO);
        validateKcddFactory(kcdd);
        kcddMapper.insert(kcdd);
        // 返回
        return kcdd.getId();
    }

    @Override
    public void updateKcdd(KcddUpdateReqVO updateReqVO) {
        // 校验存在
        validateKcddExists(updateReqVO.getId());
        // 更新
        KcddDO updateObj = KcddConvert.INSTANCE.convert(updateReqVO);
        // 校验工厂与公司
        validateKcddFactory(updateObj);
        kcddMapper.updateById(updateObj);
    }

    @Override
    public void deleteKcdd(Long id) {
        // 校验存在
        validateKcddExists(id);
        // 删除
        kcddMapper.deleteById(id);
    }

    private void validateKcddExists(Long id) {
        if (kcddMapper.selectById(id) == null) {
            throw exception(KCDD_NOT_EXISTS);
        }
    }

    private  void validateKcddFactory(KcddDO kcdd) {
        GcxxDO gcxx = gcxxService.getGcxx(kcdd.getGc());
        if (Objects.isNull(gcxx)) throw exception(GCXX_NOT_EXISTS);
        if (!Objects.equals(gcxx.getGs(), kcdd.getGs())) throw exception(KCDD_NOT_MATCH_FACTORY_COMPANY);
    }

    @Override
    public KcddDO getKcdd(Long id) {
        return kcddMapper.selectById(id);
    }

    @Override
    public List<KcddDO> getKcddList(Collection<Long> ids) {
        return kcddMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<KcddDO> getKcddPage(KcddPageReqVO pageReqVO) {
        return kcddMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<KcddPageItemRespVO> getKcddPageDetial(KcddPageReqVO pageReqVO) {
        PageResult<KcddDO> kcddDOPageResult = kcddMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(kcddDOPageResult.getList())) return PageResult.empty();
        // 需要拼接的工厂信息
        Set<Long> factoryIds = convertSet(kcddDOPageResult.getList(), KcddDO::getGc);
        Map<Long, GcxxDO> factoryMap = CollectionUtils.convertMap(gcxxService.getGcxxList(factoryIds), GcxxDO::getId);
        // 需要拼接的公司信息
        Set<Long> companyIds = convertSet(kcddDOPageResult.getList(), KcddDO::getGs);
        Map<Long, DeptRespDTO> companyMap = deptApi.getDeptMap(companyIds);
        // 拼接信息
        List<KcddPageItemRespVO> result = new ArrayList<>(kcddDOPageResult.getList().size());
        kcddDOPageResult.getList().forEach(kcddDO -> {
            KcddPageItemRespVO item = KcddConvert.INSTANCE.convert2(kcddDO);
            KcddPageItemRespVO.Factory factory = KcddConvert.INSTANCE.convert(GcxxConvert.INSTANCE.convert(factoryMap.get(kcddDO.getGc())));
            factory.setCompanyName(companyMap.get(factory.getCompanyId()).getName());
            item.setFactory(factory);
            result.add(item);
        });
        return new PageResult<>(result, kcddDOPageResult.getTotal());
    }

    @Override
    public List<KcddDO> getKcddList(KcddExportReqVO exportReqVO) {
        return kcddMapper.selectList(exportReqVO);
    }

}
