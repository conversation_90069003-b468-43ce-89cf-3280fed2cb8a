package com.neway.assets.module.info.dal.dataobject.zcfl;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neway.assets.module.info.enums.enable.QybsStatusEnum;
import lombok.*;

/**
 * 资产分类 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_zcfl", schema = "gdzc")
@KeySequence("bs_zcfl_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcflDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 资产分类方式
     */
    private Long zcflfs;
    /**
     * 资产分类描述
     */
    private String zcflms;
    /**
     * 启用标识
     *
     * 枚举 {@link QybsStatusEnum}
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
