package com.neway.assets.module.info.controller.admin.zcxz;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.zcxz.vo.*;
import com.neway.assets.module.info.convert.zcxz.ZcxzConvert;
import com.neway.assets.module.info.dal.dataobject.zcxz.ZcxzDO;
import com.neway.assets.module.info.service.zcxz.ZcxzService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产性质")
@RestController
@RequestMapping("/info/zcxz")
@Validated
public class ZcxzController {

    @Resource
    private ZcxzService zcxzService;

    @PostMapping("/create")
    @Operation(summary = "创建资产性质")
    @PreAuthorize("@ss.hasPermission('info:zcxz:create')")
    public CommonResult<Integer> createZcxz(@Valid @RequestBody ZcxzCreateReqVO createReqVO) {
        return success(zcxzService.createZcxz(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产性质")
    @PreAuthorize("@ss.hasPermission('info:zcxz:update')")
    public CommonResult<Boolean> updateZcxz(@Valid @RequestBody ZcxzUpdateReqVO updateReqVO) {
        zcxzService.updateZcxz(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产性质")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:zcxz:delete')")
    public CommonResult<Boolean> deleteZcxz(@RequestParam("id") Integer id) {
        zcxzService.deleteZcxz(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产性质")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:zcxz:query')")
    public CommonResult<ZcxzRespVO> getZcxz(@RequestParam("id") Integer id) {
        ZcxzDO zcxz = zcxzService.getZcxz(id);
        return success(ZcxzConvert.INSTANCE.convert(zcxz));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产性质列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('info:zcxz:query')")
    public CommonResult<List<ZcxzRespVO>> getZcxzList(@RequestParam("ids") Collection<Integer> ids) {
        List<ZcxzDO> list = zcxzService.getZcxzList(ids);
        return success(ZcxzConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产性质分页")
    @PreAuthorize("@ss.hasPermission('info:zcxz:query')")
    public CommonResult<PageResult<ZcxzRespVO>> getZcxzPage(@Valid ZcxzPageReqVO pageVO) {
        PageResult<ZcxzDO> pageResult = zcxzService.getZcxzPage(pageVO);
        return success(ZcxzConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产性质 Excel")
    @PreAuthorize("@ss.hasPermission('info:zcxz:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcxzExcel(@Valid ZcxzExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcxzDO> list = zcxzService.getZcxzList(exportReqVO);
        // 导出 Excel
        List<ZcxzExcelVO> datas = ZcxzConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产性质.xls", "数据", ZcxzExcelVO.class, datas);
    }

}
