package com.neway.assets.module.info.api.kwxx;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.kwxx.dto.KwxxRespDTO;
import com.neway.assets.module.info.convert.kwxx.KwxxConvert;
import com.neway.assets.module.info.dal.dataobject.kwxx.KwxxDO;
import com.neway.assets.module.info.service.kwxx.KwxxService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @since 2023/6/7 10:51
 **/
@Service
@RequiredArgsConstructor
public class KwxxApiImpl implements KwxxApi{

    private final KwxxService kwxxService;

    @Override
    public List<KwxxRespDTO> getKwxxList(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<KwxxDO> list = kwxxService.getKwxxList(ids);
        return KwxxConvert.INSTANCE.convertList03(list);
    }
}
