package com.neway.assets.module.info.controller.admin.jldw.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 计量单位 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class JldwBaseVO {

    @Schema(description = "计量单位简称", required = true)
    @NotNull(message = "计量单位简称不能为空")
    private String jldwjc;

    @Schema(description = "计量单位名称", required = true)
    @NotNull(message = "计量单位名称不能为空")
    private String jldwmc;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
