package com.neway.assets.module.info.controller.admin.gcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 工厂信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class GcxxBaseVO {

    @Schema(description = "隶属公司", required = true)
    @NotNull(message = "隶属公司不能为空")
    private Long gs;

    @Schema(description = "工厂描述", required = true)
    @NotNull(message = "工厂描述不能为空")
    private String gcms;

    @Schema(description = "工厂地址信息")
    private String dzxx;

    @Schema(description = "工厂地址经纬度")
    private String jwdxx;

    @Schema(description = "工厂照片链接")
    private String gczp;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
