package com.neway.assets.module.info.service.hbjfl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.*;
import com.neway.assets.module.info.dal.dataobject.hbjfl.HbjflDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.hbjfl.HbjflConvert;
import com.neway.assets.module.info.dal.mysql.hbjfl.HbjflMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 耗材/备品备件分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HbjflServiceImpl implements HbjflService {

    @Resource
    private HbjflMapper hbjflMapper;

    @Override
    public Long createHbjfl(HbjflCreateReqVO createReqVO) {
        // 插入
        HbjflDO hbjfl = HbjflConvert.INSTANCE.convert(createReqVO);
        hbjflMapper.insert(hbjfl);
        // 返回
        return hbjfl.getId();
    }

    @Override
    public void updateHbjfl(HbjflUpdateReqVO updateReqVO) {
        // 校验存在
        validateHbjflExists(updateReqVO.getId());
        // 更新
        HbjflDO updateObj = HbjflConvert.INSTANCE.convert(updateReqVO);
        hbjflMapper.updateById(updateObj);
    }

    @Override
    public void deleteHbjfl(Long id) {
        // 校验存在
        validateHbjflExists(id);
        // 删除
        hbjflMapper.deleteById(id);
    }

    private void validateHbjflExists(Long id) {
        if (hbjflMapper.selectById(id) == null) {
            throw exception(HBJFL_NOT_EXISTS);
        }
    }

    @Override
    public HbjflDO getHbjfl(Long id) {
        return hbjflMapper.selectById(id);
    }

    @Override
    public List<HbjflDO> getHbjflList(Collection<Long> ids) {
        return hbjflMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HbjflDO> getHbjflPage(HbjflPageReqVO pageReqVO) {
        return hbjflMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HbjflDO> getHbjflList(HbjflExportReqVO exportReqVO) {
        return hbjflMapper.selectList(exportReqVO);
    }

}
