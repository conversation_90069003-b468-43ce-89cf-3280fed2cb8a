package com.neway.assets.module.info.controller.admin.jldw.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 计量单位更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JldwUpdateReqVO extends JldwBaseVO {

    @Schema(description = "自增长id", required = true, example = "3778")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
