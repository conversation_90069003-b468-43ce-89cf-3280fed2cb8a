package com.neway.assets.module.info.service.gcxx;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.gcxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.gcxx.GcxxConvert;
import com.neway.assets.module.info.dal.mysql.gcxx.GcxxMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 工厂信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GcxxServiceImpl implements GcxxService {

    @Resource
    private GcxxMapper gcxxMapper;

    @Override
    public Long createGcxx(GcxxCreateReqVO createReqVO) {
        // 插入
        GcxxDO gcxx = GcxxConvert.INSTANCE.convert(createReqVO);
        gcxxMapper.insert(gcxx);
        // 返回
        return gcxx.getId();
    }

    @Override
    public void updateGcxx(GcxxUpdateReqVO updateReqVO) {
        // 校验存在
        validateGcxxExists(updateReqVO.getId());
        // 更新
        GcxxDO updateObj = GcxxConvert.INSTANCE.convert(updateReqVO);
        gcxxMapper.updateById(updateObj);
    }

    @Override
    public void deleteGcxx(Long id) {
        // 校验存在
        validateGcxxExists(id);
        // 删除
        gcxxMapper.deleteById(id);
    }

    private void validateGcxxExists(Long id) {
        if (gcxxMapper.selectById(id) == null) {
            throw exception(GCXX_NOT_EXISTS);
        }
    }

    @Override
    public GcxxDO getGcxx(Long id) {
        return gcxxMapper.selectById(id);
    }

    @Override
    public List<GcxxDO> getGcxxList(Collection<Long> ids) {
        return gcxxMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<GcxxDO> getGcxxPage(GcxxPageReqVO pageReqVO) {
        return gcxxMapper.selectPage(pageReqVO);
    }

    @Override
    public List<GcxxDO> getGcxxList(GcxxExportReqVO exportReqVO) {
        return gcxxMapper.selectList(exportReqVO);
    }

}
