package com.neway.assets.module.info.controller.admin.wxyy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 维护/维修原因 Excel 导出 Request VO，参数和 WxyyPageReqVO 是一致的")
@Data
public class WxyyExportReqVO {

    @Schema(description = "维修维护原因描述")
    private String wxyyms;

    @Schema(description = "启用标识（1:启用 ；0:未启用）")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
