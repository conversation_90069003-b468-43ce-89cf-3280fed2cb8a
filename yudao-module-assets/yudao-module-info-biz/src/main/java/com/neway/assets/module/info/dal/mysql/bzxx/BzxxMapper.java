package com.neway.assets.module.info.dal.mysql.bzxx;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.bzxx.BzxxDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.bzxx.vo.*;

/**
 * 币种信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BzxxMapper extends BaseMapperX<BzxxDO> {

    default PageResult<BzxxDO> selectPage(BzxxPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BzxxDO>()
                .eqIfPresent(BzxxDO::getBzjc, reqVO.getBzjc())
                .likeIfPresent(BzxxDO::getBzmc, reqVO.getBzmc())
                .eqIfPresent(BzxxDO::getQybs, reqVO.getQybs())
                .eqIfPresent(BzxxDO::getBz, reqVO.getBz())
                .betweenIfPresent(BzxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BzxxDO::getId));
    }

    default List<BzxxDO> selectList(BzxxExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BzxxDO>()
                .eqIfPresent(BzxxDO::getBzjc, reqVO.getBzjc())
                .likeIfPresent(BzxxDO::getBzmc, reqVO.getBzmc())
                .eqIfPresent(BzxxDO::getQybs, reqVO.getQybs())
                .eqIfPresent(BzxxDO::getBz, reqVO.getBz())
                .betweenIfPresent(BzxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BzxxDO::getId));
    }

}
