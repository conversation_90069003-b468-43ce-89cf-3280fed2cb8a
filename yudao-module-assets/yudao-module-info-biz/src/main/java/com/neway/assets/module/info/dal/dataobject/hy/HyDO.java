package com.neway.assets.module.info.dal.dataobject.hy;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 行业 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_hy", schema = "gdzc")
@KeySequence("bs_hy_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HyDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 行业名称
     */
    private String hyms;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
