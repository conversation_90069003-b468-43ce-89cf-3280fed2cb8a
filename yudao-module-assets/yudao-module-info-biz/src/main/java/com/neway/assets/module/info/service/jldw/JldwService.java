package com.neway.assets.module.info.service.jldw;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.jldw.vo.*;
import com.neway.assets.module.info.dal.dataobject.jldw.JldwDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 计量单位 Service 接口
 *
 * <AUTHOR>
 */
public interface JldwService {

    /**
     * 创建计量单位
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJldw(@Valid JldwCreateReqVO createReqVO);

    /**
     * 更新计量单位
     *
     * @param updateReqVO 更新信息
     */
    void updateJldw(@Valid JldwUpdateReqVO updateReqVO);

    /**
     * 删除计量单位
     *
     * @param id 编号
     */
    void deleteJldw(Long id);

    /**
     * 获得计量单位
     *
     * @param id 编号
     * @return 计量单位
     */
    JldwDO getJldw(Long id);

    /**
     * 获得计量单位列表
     *
     * @param ids 编号
     * @return 计量单位列表
     */
    List<JldwDO> getJldwList(Collection<Long> ids);

    /**
     * 获得计量单位分页
     *
     * @param pageReqVO 分页查询
     * @return 计量单位分页
     */
    PageResult<JldwDO> getJldwPage(JldwPageReqVO pageReqVO);

    /**
     * 获得计量单位列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 计量单位列表
     */
    List<JldwDO> getJldwList(JldwExportReqVO exportReqVO);

}
