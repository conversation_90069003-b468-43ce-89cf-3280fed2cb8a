package com.neway.assets.module.info.api.jldw;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.jldw.dto.JldwRespDTO;
import com.neway.assets.module.info.convert.jldw.JldwConvert;
import com.neway.assets.module.info.dal.dataobject.jldw.JldwDO;
import com.neway.assets.module.info.service.jldw.JldwService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 10:12
 **/
@Service
public class JldwApiImpl implements JldwApi{

    @Resource
    private JldwService jldwService;

    @Override
    public List<JldwRespDTO> getJldwList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<JldwDO> jldwList = jldwService.getJldwList(ids);
        return JldwConvert.INSTANCE.convertList03(jldwList);
    }
}
