package com.neway.assets.module.info.controller.admin.wxyy;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.wxyy.vo.*;
import com.neway.assets.module.info.convert.wxyy.WxyyConvert;
import com.neway.assets.module.info.dal.dataobject.wxyy.WxyyDO;
import com.neway.assets.module.info.service.wxyy.WxyyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 维护/维修原因")
@RestController
@RequestMapping("/info/wxyy")
@Validated
public class WxyyController {

    @Resource
    private WxyyService wxyyService;

    @PostMapping("/create")
    @Operation(summary = "创建维护/维修原因")
    @PreAuthorize("@ss.hasPermission('info:wxyy:create')")
    public CommonResult<Long> createWxyy(@Valid @RequestBody WxyyCreateReqVO createReqVO) {
        return success(wxyyService.createWxyy(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新维护/维修原因")
    @PreAuthorize("@ss.hasPermission('info:wxyy:update')")
    public CommonResult<Boolean> updateWxyy(@Valid @RequestBody WxyyUpdateReqVO updateReqVO) {
        wxyyService.updateWxyy(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除维护/维修原因")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:wxyy:delete')")
    public CommonResult<Boolean> deleteWxyy(@RequestParam("id") Long id) {
        wxyyService.deleteWxyy(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得维护/维修原因")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:wxyy:query')")
    public CommonResult<WxyyRespVO> getWxyy(@RequestParam("id") Long id) {
        WxyyDO wxyy = wxyyService.getWxyy(id);
        return success(WxyyConvert.INSTANCE.convert(wxyy));
    }

    @GetMapping("/list")
    @Operation(summary = "获得维护/维修原因列表")
    @PreAuthorize("@ss.hasPermission('info:wxyy:query')")
    public CommonResult<List<WxyyRespVO>> getWxyyList(@Valid WxyyExportReqVO reqVO) {
        List<WxyyDO> list = wxyyService.getWxyyList(reqVO);
        return success(WxyyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得维护/维修原因分页")
    @PreAuthorize("@ss.hasPermission('info:wxyy:query')")
    public CommonResult<PageResult<WxyyRespVO>> getWxyyPage(@Valid WxyyPageReqVO pageVO) {
        PageResult<WxyyDO> pageResult = wxyyService.getWxyyPage(pageVO);
        return success(WxyyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出维护/维修原因 Excel")
    @PreAuthorize("@ss.hasPermission('info:wxyy:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWxyyExcel(@Valid WxyyExportReqVO exportReqVO,
                                HttpServletResponse response) throws IOException {
        List<WxyyDO> list = wxyyService.getWxyyList(exportReqVO);
        // 导出 Excel
        List<WxyyExcelVO> datas = WxyyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "维护/维修原因.xls", "数据", WxyyExcelVO.class, datas);
    }

}
