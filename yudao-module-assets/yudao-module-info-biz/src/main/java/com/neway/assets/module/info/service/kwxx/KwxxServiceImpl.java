package com.neway.assets.module.info.service.kwxx;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.kwxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.kwxx.KwxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.kwxx.KwxxConvert;
import com.neway.assets.module.info.dal.mysql.kwxx.KwxxMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 库位信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class KwxxServiceImpl implements KwxxService {

    @Resource
    private KwxxMapper kwxxMapper;

    @Override
    public Long createKwxx(KwxxCreateReqVO createReqVO) {
        // 插入
        KwxxDO kwxx = KwxxConvert.INSTANCE.convert(createReqVO);
        kwxxMapper.insert(kwxx);
        // 返回
        return kwxx.getId();
    }

    @Override
    public void updateKwxx(KwxxUpdateReqVO updateReqVO) {
        // 校验存在
        validateKwxxExists(updateReqVO.getId());
        // 更新
        KwxxDO updateObj = KwxxConvert.INSTANCE.convert(updateReqVO);
        kwxxMapper.updateById(updateObj);
    }

    @Override
    public void deleteKwxx(Long id) {
        // 校验存在
        validateKwxxExists(id);
        // 删除
        kwxxMapper.deleteById(id);
    }

    private void validateKwxxExists(Long id) {
        if (kwxxMapper.selectById(id) == null) {
            throw exception(KWXX_NOT_EXISTS);
        }
    }

    @Override
    public KwxxDO getKwxx(Long id) {
        return kwxxMapper.selectById(id);
    }

    @Override
    public List<KwxxDO> getKwxxList(Collection<Long> ids) {
        return kwxxMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<KwxxDO> getKwxxPage(KwxxPageReqVO pageReqVO) {
        return kwxxMapper.selectPage(pageReqVO);
    }

    @Override
    public List<KwxxDO> getKwxxList(KwxxExportReqVO exportReqVO) {
        return kwxxMapper.selectList(exportReqVO);
    }

}
