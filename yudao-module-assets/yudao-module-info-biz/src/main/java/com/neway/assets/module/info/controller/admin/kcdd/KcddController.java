package com.neway.assets.module.info.controller.admin.kcdd;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.kcdd.vo.*;
import com.neway.assets.module.info.convert.kcdd.KcddConvert;
import com.neway.assets.module.info.dal.dataobject.kcdd.KcddDO;
import com.neway.assets.module.info.service.kcdd.KcddService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 库存地点")
@RestController
@RequestMapping("/info/kcdd")
@Validated
public class KcddController {

    @Resource
    private KcddService kcddService;

    @PostMapping("/create")
    @Operation(summary = "创建库存地点")
    @PreAuthorize("@ss.hasPermission('info:kcdd:create')")
    public CommonResult<Long> createKcdd(@Valid @RequestBody KcddCreateReqVO createReqVO) {
        return success(kcddService.createKcdd(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库存地点")
    @PreAuthorize("@ss.hasPermission('info:kcdd:update')")
    public CommonResult<Boolean> updateKcdd(@Valid @RequestBody KcddUpdateReqVO updateReqVO) {
        kcddService.updateKcdd(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库存地点")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:kcdd:delete')")
    public CommonResult<Boolean> deleteKcdd(@RequestParam("id") Long id) {
        kcddService.deleteKcdd(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库存地点")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:kcdd:query')")
    public CommonResult<KcddRespVO> getKcdd(@RequestParam("id") Long id) {
        KcddDO kcdd = kcddService.getKcdd(id);
        return success(KcddConvert.INSTANCE.convert(kcdd));
    }

    @GetMapping("/list")
    @Operation(summary = "获得库存地点列表")
    @PreAuthorize("@ss.hasPermission('info:kcdd:query')")
    public CommonResult<List<KcddRespVO>> getKcddList(@Valid KcddExportReqVO reqVO) {
        List<KcddDO> list = kcddService.getKcddList(reqVO);
        return success(KcddConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库存地点分页")
    @PreAuthorize("@ss.hasPermission('info:kcdd:query')")
    public CommonResult<PageResult<KcddPageItemRespVO>> getKcddPage(@Valid KcddPageReqVO pageVO) {
        PageResult<KcddPageItemRespVO> pageResult = kcddService.getKcddPageDetial(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库存地点 Excel")
    @PreAuthorize("@ss.hasPermission('info:kcdd:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportKcddExcel(@Valid KcddExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<KcddDO> list = kcddService.getKcddList(exportReqVO);
        // 导出 Excel
        List<KcddExcelVO> datas = KcddConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "库存地点.xls", "数据", KcddExcelVO.class, datas);
    }

}
