package com.neway.assets.module.info.controller.admin.gcxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工厂信息 Excel 导出 Request VO，参数和 GcxxPageReqVO 是一致的")
@Data
public class GcxxExportReqVO {

    @Schema(description = "隶属公司")
    private Long gs;

    @Schema(description = "工厂描述")
    private String gcms;

    @Schema(description = "工厂地址信息")
    private String dzxx;

    @Schema(description = "工厂地址经纬度")
    private String jwdxx;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
