package com.neway.assets.module.info.service.zcflfs;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcflfs.ZcflfsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产分类方式 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcflfsService {

    /**
     * 创建资产分类方式
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createZcflfs(@Valid ZcflfsCreateReqVO createReqVO);

    /**
     * 更新资产分类方式
     *
     * @param updateReqVO 更新信息
     */
    void updateZcflfs(@Valid ZcflfsUpdateReqVO updateReqVO);

    /**
     * 删除资产分类方式
     *
     * @param id 编号
     */
    void deleteZcflfs(Integer id);

    /**
     * 获得资产分类方式
     *
     * @param id 编号
     * @return 资产分类方式
     */
    ZcflfsDO getZcflfs(Integer id);

    /**
     * 获得资产分类方式列表
     *
     * @param ids 编号
     * @return 资产分类方式列表
     */
    List<ZcflfsDO> getZcflfsList(Collection<Integer> ids);

    /**
     * 获得资产分类方式分页
     *
     * @param pageReqVO 分页查询
     * @return 资产分类方式分页
     */
    PageResult<ZcflfsDO> getZcflfsPage(ZcflfsPageReqVO pageReqVO);

    /**
     * 获得资产分类方式列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产分类方式列表
     */
    List<ZcflfsDO> getZcflfsList(ZcflfsExportReqVO exportReqVO);

}
