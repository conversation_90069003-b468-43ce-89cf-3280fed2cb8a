package com.neway.assets.module.info.service.jldw;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.jldw.vo.*;
import com.neway.assets.module.info.dal.dataobject.jldw.JldwDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.jldw.JldwConvert;
import com.neway.assets.module.info.dal.mysql.jldw.JldwMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 计量单位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JldwServiceImpl implements JldwService {

    @Resource
    private JldwMapper jldwMapper;

    @Override
    public Long createJldw(JldwCreateReqVO createReqVO) {
        // 插入
        JldwDO jldw = JldwConvert.INSTANCE.convert(createReqVO);
        jldwMapper.insert(jldw);
        // 返回
        return jldw.getId();
    }

    @Override
    public void updateJldw(JldwUpdateReqVO updateReqVO) {
        // 校验存在
        validateJldwExists(updateReqVO.getId());
        // 更新
        JldwDO updateObj = JldwConvert.INSTANCE.convert(updateReqVO);
        jldwMapper.updateById(updateObj);
    }

    @Override
    public void deleteJldw(Long id) {
        // 校验存在
        validateJldwExists(id);
        // 删除
        jldwMapper.deleteById(id);
    }

    private void validateJldwExists(Long id) {
        if (jldwMapper.selectById(id) == null) {
            throw exception(JLDW_NOT_EXISTS);
        }
    }

    @Override
    public JldwDO getJldw(Long id) {
        return jldwMapper.selectById(id);
    }

    @Override
    public List<JldwDO> getJldwList(Collection<Long> ids) {
        return jldwMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<JldwDO> getJldwPage(JldwPageReqVO pageReqVO) {
        return jldwMapper.selectPage(pageReqVO);
    }

    @Override
    public List<JldwDO> getJldwList(JldwExportReqVO exportReqVO) {
        return jldwMapper.selectList(exportReqVO);
    }

}
