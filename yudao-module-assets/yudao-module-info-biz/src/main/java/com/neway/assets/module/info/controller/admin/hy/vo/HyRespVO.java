package com.neway.assets.module.info.controller.admin.hy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 行业 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HyRespVO extends HyBaseVO {

    @Schema(description = "自增长id", required = true, example = "4769")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
