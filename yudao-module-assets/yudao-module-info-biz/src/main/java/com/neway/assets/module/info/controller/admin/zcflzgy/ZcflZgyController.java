package com.neway.assets.module.info.controller.admin.zcflzgy;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.*;
import com.neway.assets.module.info.convert.zcflzgy.ZcflZgyConvert;
import com.neway.assets.module.info.dal.dataobject.zcflzgy.ZcflZgyDO;
import com.neway.assets.module.info.service.zcflzgy.ZcflZgyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产分类资管员关联")
@RestController
@RequestMapping("/info/zcfl-zgy")
@Validated
public class ZcflZgyController {

    @Resource
    private ZcflZgyService zcflZgyService;

    @PostMapping("/create")
    @Operation(summary = "创建资产分类资管员关联")
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:create')")
    public CommonResult<Long> createZcflZgy(@Valid @RequestBody ZcflZgyCreateReqVO createReqVO) {
        return success(zcflZgyService.createZcflZgy(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产分类资管员关联")
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:update')")
    public CommonResult<Boolean> updateZcflZgy(@Valid @RequestBody ZcflZgyUpdateReqVO updateReqVO) {
        zcflZgyService.updateZcflZgy(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产分类资管员关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:delete')")
    public CommonResult<Boolean> deleteZcflZgy(@RequestParam("id") Long id) {
        zcflZgyService.deleteZcflZgy(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产分类资管员关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:query')")
    public CommonResult<ZcflZgyRespVO> getZcflZgy(@RequestParam("id") Long id) {
        ZcflZgyDO zcflZgy = zcflZgyService.getZcflZgy(id);
        return success(ZcflZgyConvert.INSTANCE.convert(zcflZgy));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产分类资管员关联列表")
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:query')")
    public CommonResult<List<ZcflZgyRespVO>> getZcflZgyList(@Valid ZcflZgyExportReqVO reqVO) {
        List<ZcflZgyDO> list = zcflZgyService.getZcflZgyList(reqVO);
        return success(ZcflZgyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产分类资管员关联分页")
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:query')")
    public CommonResult<PageResult<ZcflZgyRespVO>> getZcflZgyPage(@Valid ZcflZgyPageReqVO pageVO) {
        PageResult<ZcflZgyDO> pageResult = zcflZgyService.getZcflZgyPage(pageVO);
        return success(ZcflZgyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产分类资管员关联 Excel")
    @PreAuthorize("@ss.hasPermission('info:zcfl-zgy:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcflZgyExcel(@Valid ZcflZgyExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcflZgyDO> list = zcflZgyService.getZcflZgyList(exportReqVO);
        // 导出 Excel
        List<ZcflZgyExcelVO> datas = ZcflZgyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产分类资管员关联.xls", "数据", ZcflZgyExcelVO.class, datas);
    }

}
