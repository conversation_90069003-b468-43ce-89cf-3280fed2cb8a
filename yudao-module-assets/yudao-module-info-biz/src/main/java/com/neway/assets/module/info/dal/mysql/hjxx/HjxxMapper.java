package com.neway.assets.module.info.dal.mysql.hjxx;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.hjxx.HjxxDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.hjxx.vo.*;

/**
 * 货架 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HjxxMapper extends BaseMapperX<HjxxDO> {

    default PageResult<HjxxDO> selectPage(HjxxPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HjxxDO>()
                .eqIfPresent(HjxxDO::getKw, reqVO.getKw())
                .eqIfPresent(HjxxDO::getHjbh, reqVO.getHjbh())
                .eqIfPresent(HjxxDO::getHjms, reqVO.getHjms())
                .eqIfPresent(HjxxDO::getHang, reqVO.getHang())
                .eqIfPresent(HjxxDO::getLie, reqVO.getLie())
                .eqIfPresent(HjxxDO::getCeng, reqVO.getCeng())
                .eqIfPresent(HjxxDO::getDany, reqVO.getDany())
                .eqIfPresent(HjxxDO::getQybs, reqVO.getQybs())
                .betweenIfPresent(HjxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HjxxDO::getId));
    }

    default List<HjxxDO> selectList(HjxxExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HjxxDO>()
                .eqIfPresent(HjxxDO::getKw, reqVO.getKw())
                .eqIfPresent(HjxxDO::getHjbh, reqVO.getHjbh())
                .eqIfPresent(HjxxDO::getHjms, reqVO.getHjms())
                .eqIfPresent(HjxxDO::getHang, reqVO.getHang())
                .eqIfPresent(HjxxDO::getLie, reqVO.getLie())
                .eqIfPresent(HjxxDO::getCeng, reqVO.getCeng())
                .eqIfPresent(HjxxDO::getDany, reqVO.getDany())
                .eqIfPresent(HjxxDO::getQybs, reqVO.getQybs())
                .betweenIfPresent(HjxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HjxxDO::getId));
    }

}
