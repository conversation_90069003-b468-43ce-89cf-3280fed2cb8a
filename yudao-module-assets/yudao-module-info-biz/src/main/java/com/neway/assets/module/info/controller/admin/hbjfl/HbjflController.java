package com.neway.assets.module.info.controller.admin.hbjfl;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.*;
import com.neway.assets.module.info.convert.hbjfl.HbjflConvert;
import com.neway.assets.module.info.dal.dataobject.hbjfl.HbjflDO;
import com.neway.assets.module.info.enums.enable.QybsStatusEnum;
import com.neway.assets.module.info.service.hbjfl.HbjflService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 耗材/备品备件分类")
@RestController
@RequestMapping("/info/hbjfl")
@Validated
public class HbjflController {

    @Resource
    private HbjflService hbjflService;

    @PostMapping("/create")
    @Operation(summary = "创建耗材/备品备件分类")
    @PreAuthorize("@ss.hasPermission('info:hbjfl:create')")
    public CommonResult<Long> createHbjfl(@Valid @RequestBody HbjflCreateReqVO createReqVO) {
        return success(hbjflService.createHbjfl(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新耗材/备品备件分类")
    @PreAuthorize("@ss.hasPermission('info:hbjfl:update')")
    public CommonResult<Boolean> updateHbjfl(@Valid @RequestBody HbjflUpdateReqVO updateReqVO) {
        hbjflService.updateHbjfl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除耗材/备品备件分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:hbjfl:delete')")
    public CommonResult<Boolean> deleteHbjfl(@RequestParam("id") Long id) {
        hbjflService.deleteHbjfl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得耗材/备品备件分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:hbjfl:query')")
    public CommonResult<HbjflRespVO> getHbjfl(@RequestParam("id") Long id) {
        HbjflDO hbjfl = hbjflService.getHbjfl(id);
        return success(HbjflConvert.INSTANCE.convert(hbjfl));
    }

    @GetMapping("/list")
    @Operation(summary = "获得耗材/备品备件分类列表")
    @PreAuthorize("@ss.hasPermission('info:hbjfl:query')")
    public CommonResult<List<HbjflRespVO>> getHbjflList(@Valid HbjflExportReqVO reqVO) {
        List<HbjflDO> list = hbjflService.getHbjflList(reqVO).stream()
                .filter(o -> Objects.equals(QybsStatusEnum.ENABLED.getVal(), o.getQybs()))
                .collect(Collectors.toList());
        return success(HbjflConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得耗材/备品备件分类分页")
    @PreAuthorize("@ss.hasPermission('info:hbjfl:query')")
    public CommonResult<PageResult<HbjflRespVO>> getHbjflPage(@Valid HbjflPageReqVO pageVO) {
        PageResult<HbjflDO> pageResult = hbjflService.getHbjflPage(pageVO);
        return success(HbjflConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出耗材/备品备件分类 Excel")
    @PreAuthorize("@ss.hasPermission('info:hbjfl:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHbjflExcel(@Valid HbjflExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HbjflDO> list = hbjflService.getHbjflList(exportReqVO);
        // 导出 Excel
        List<HbjflExcelVO> datas = HbjflConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "耗材/备品备件分类.xls", "数据", HbjflExcelVO.class, datas);
    }

}
