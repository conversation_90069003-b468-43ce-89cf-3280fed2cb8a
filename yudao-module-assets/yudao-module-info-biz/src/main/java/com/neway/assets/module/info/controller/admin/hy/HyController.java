package com.neway.assets.module.info.controller.admin.hy;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.hy.vo.*;
import com.neway.assets.module.info.convert.hy.HyConvert;
import com.neway.assets.module.info.dal.dataobject.hy.HyDO;
import com.neway.assets.module.info.service.hy.HyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 行业")
@RestController
@RequestMapping("/info/hy")
@Validated
public class HyController {

    @Resource
    private HyService hyService;

    @PostMapping("/create")
    @Operation(summary = "创建行业")
    @PreAuthorize("@ss.hasPermission('info:hy:create')")
    public CommonResult<Long> createHy(@Valid @RequestBody HyCreateReqVO createReqVO) {
        return success(hyService.createHy(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新行业")
    @PreAuthorize("@ss.hasPermission('info:hy:update')")
    public CommonResult<Boolean> updateHy(@Valid @RequestBody HyUpdateReqVO updateReqVO) {
        hyService.updateHy(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除行业")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:hy:delete')")
    public CommonResult<Boolean> deleteHy(@RequestParam("id") Long id) {
        hyService.deleteHy(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得行业")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:hy:query')")
    public CommonResult<HyRespVO> getHy(@RequestParam("id") Long id) {
        HyDO hy = hyService.getHy(id);
        return success(HyConvert.INSTANCE.convert(hy));
    }

    @GetMapping("/list")
    @Operation(summary = "获得行业列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('info:hy:query')")
    public CommonResult<List<HyRespVO>> getHyList(@RequestParam("ids") Collection<Long> ids) {
        List<HyDO> list = hyService.getHyList(ids);
        return success(HyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得行业分页")
    @PreAuthorize("@ss.hasPermission('info:hy:query')")
    public CommonResult<PageResult<HyRespVO>> getHyPage(@Valid HyPageReqVO pageVO) {
        PageResult<HyDO> pageResult = hyService.getHyPage(pageVO);
        return success(HyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出行业 Excel")
    @PreAuthorize("@ss.hasPermission('info:hy:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHyExcel(@Valid HyExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HyDO> list = hyService.getHyList(exportReqVO);
        // 导出 Excel
        List<HyExcelVO> datas = HyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "行业.xls", "数据", HyExcelVO.class, datas);
    }

}
