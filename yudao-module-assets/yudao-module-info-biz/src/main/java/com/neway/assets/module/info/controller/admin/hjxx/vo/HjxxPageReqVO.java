package com.neway.assets.module.info.controller.admin.hjxx.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 货架分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HjxxPageReqVO extends PageParam {

    @Schema(description = "库位（bs_kwxx-->id）")
    private Long kw;

    @Schema(description = "货架编号(行+列+层+单元)")
    private String hjbh;

    @Schema(description = "货架描述")
    private String hjms;

    @Schema(description = "货架所在行")
    private Integer hang;

    @Schema(description = "货架所在列")
    private Integer lie;

    @Schema(description = "货架所在列")
    private Integer ceng;

    @Schema(description = "货架所在层的单元格")
    private Integer dany;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
