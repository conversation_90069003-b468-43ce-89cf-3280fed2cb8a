package com.neway.assets.module.info.controller.admin.secondzcfl.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 资产二级分类 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SecondZcflExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("资产一级分类")
    private Long zcflfsId;

    @ExcelProperty("二级分类描述")
    private String zcflms;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status")
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
