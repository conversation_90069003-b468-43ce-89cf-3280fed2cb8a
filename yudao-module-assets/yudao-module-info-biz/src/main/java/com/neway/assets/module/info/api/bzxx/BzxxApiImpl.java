package com.neway.assets.module.info.api.bzxx;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.bzxx.dto.BzxxRespDTO;
import com.neway.assets.module.info.convert.bzxx.BzxxConvert;
import com.neway.assets.module.info.dal.dataobject.bzxx.BzxxDO;
import com.neway.assets.module.info.service.bzxx.BzxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 10:35
 **/
@Service
public class BzxxApiImpl implements BzxxApi{

    @Resource
    private BzxxService bzxxService;

    @Override
    public List<BzxxRespDTO> getBzxxList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<BzxxDO> bzxxList = bzxxService.getBzxxList(ids);
        return BzxxConvert.INSTANCE.convertList03(bzxxList);
    }
}
