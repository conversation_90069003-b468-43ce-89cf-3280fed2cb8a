package com.neway.assets.module.info.dal.dataobject.hjxx;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 货架 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_hjxx", schema = "gdzc")
@KeySequence("bs_hjxx_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HjxxDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 库位（bs_kwxx-->id）
     */
    private Long kw;
    /**
     * 货架编号(行+列+层+单元)
     */
    private String hjbh;
    /**
     * 货架描述
     */
    private String hjms;
    /**
     * 货架所在行
     */
    private Integer hang;
    /**
     * 货架所在列
     */
    private Integer lie;
    /**
     * 货架所在列
     */
    private Integer ceng;
    /**
     * 货架所在层的单元格
     */
    private Integer dany;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
