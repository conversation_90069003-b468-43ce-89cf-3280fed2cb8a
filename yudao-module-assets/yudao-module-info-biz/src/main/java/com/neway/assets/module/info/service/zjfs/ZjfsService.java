package com.neway.assets.module.info.service.zjfs;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.zjfs.vo.*;
import com.neway.assets.module.info.dal.dataobject.zjfs.ZjfsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产折旧方式 Service 接口
 *
 * <AUTHOR>
 */
public interface ZjfsService {

    /**
     * 创建资产折旧方式
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZjfs(@Valid ZjfsCreateReqVO createReqVO);

    /**
     * 更新资产折旧方式
     *
     * @param updateReqVO 更新信息
     */
    void updateZjfs(@Valid ZjfsUpdateReqVO updateReqVO);

    /**
     * 删除资产折旧方式
     *
     * @param id 编号
     */
    void deleteZjfs(Long id);

    /**
     * 获得资产折旧方式
     *
     * @param id 编号
     * @return 资产折旧方式
     */
    ZjfsDO getZjfs(Long id);

    /**
     * 获得资产折旧方式列表
     *
     * @param ids 编号
     * @return 资产折旧方式列表
     */
    List<ZjfsDO> getZjfsList(Collection<Long> ids);

    /**
     * 获得资产折旧方式分页
     *
     * @param pageReqVO 分页查询
     * @return 资产折旧方式分页
     */
    PageResult<ZjfsDO> getZjfsPage(ZjfsPageReqVO pageReqVO);

    /**
     * 获得资产折旧方式列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产折旧方式列表
     */
    List<ZjfsDO> getZjfsList(ZjfsExportReqVO exportReqVO);

}
