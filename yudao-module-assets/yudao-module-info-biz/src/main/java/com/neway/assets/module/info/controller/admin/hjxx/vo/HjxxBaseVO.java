package com.neway.assets.module.info.controller.admin.hjxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 货架 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HjxxBaseVO {

    @Schema(description = "库位（bs_kwxx-->id）", required = true)
    @NotNull(message = "库位（bs_kwxx-->id）不能为空")
    private Long kw;

    @Schema(description = "货架编号(行+列+层+单元)", required = true)
    @NotNull(message = "货架编号(行+列+层+单元)不能为空")
    private String hjbh;

    @Schema(description = "货架描述", required = true)
    @NotNull(message = "货架描述不能为空")
    private String hjms;

    @Schema(description = "货架所在行")
    private Integer hang;

    @Schema(description = "货架所在列")
    private Integer lie;

    @Schema(description = "货架所在列")
    private Integer ceng;

    @Schema(description = "货架所在层的单元格")
    private Integer dany;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
