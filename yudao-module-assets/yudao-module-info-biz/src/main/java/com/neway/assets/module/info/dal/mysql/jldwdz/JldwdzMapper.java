package com.neway.assets.module.info.dal.mysql.jldwdz;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.jldwdz.JldwdzDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.*;

/**
 * 计量单位转换对照 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JldwdzMapper extends BaseMapperX<JldwdzDO> {

    default PageResult<JldwdzDO> selectPage(JldwdzPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JldwdzDO>()
                .eqIfPresent(JldwdzDO::getCbzx, reqVO.getCbzx())
                .eqIfPresent(JldwdzDO::getYjldw, reqVO.getYjldw())
                .eqIfPresent(JldwdzDO::getYuansl, reqVO.getYuansl())
                .eqIfPresent(JldwdzDO::getZhdw, reqVO.getZhdw())
                .eqIfPresent(JldwdzDO::getZhsl, reqVO.getZhsl())
                .eqIfPresent(JldwdzDO::getQybs, reqVO.getQybs())
                .eqIfPresent(JldwdzDO::getBz, reqVO.getBz())
                .betweenIfPresent(JldwdzDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JldwdzDO::getId));
    }

    default List<JldwdzDO> selectList(JldwdzExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<JldwdzDO>()
                .eqIfPresent(JldwdzDO::getCbzx, reqVO.getCbzx())
                .eqIfPresent(JldwdzDO::getYjldw, reqVO.getYjldw())
                .eqIfPresent(JldwdzDO::getYuansl, reqVO.getYuansl())
                .eqIfPresent(JldwdzDO::getZhdw, reqVO.getZhdw())
                .eqIfPresent(JldwdzDO::getZhsl, reqVO.getZhsl())
                .eqIfPresent(JldwdzDO::getQybs, reqVO.getQybs())
                .eqIfPresent(JldwdzDO::getBz, reqVO.getBz())
                .betweenIfPresent(JldwdzDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JldwdzDO::getId));
    }

}
