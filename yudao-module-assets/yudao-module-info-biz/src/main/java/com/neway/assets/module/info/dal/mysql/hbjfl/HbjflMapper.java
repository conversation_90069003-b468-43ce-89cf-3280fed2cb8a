package com.neway.assets.module.info.dal.mysql.hbjfl;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.hbjfl.HbjflDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.*;

/**
 * 耗材/备品备件分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HbjflMapper extends BaseMapperX<HbjflDO> {

    default PageResult<HbjflDO> selectPage(HbjflPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HbjflDO>()
                .eqIfPresent(HbjflDO::getBflmc, reqVO.getBflmc())
                .eqIfPresent(HbjflDO::getCbjsfs, reqVO.getCbjsfs())
                .eqIfPresent(HbjflDO::getQybs, reqVO.getQybs())
                .eqIfPresent(HbjflDO::getBz, reqVO.getBz())
                .betweenIfPresent(HbjflDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HbjflDO::getId));
    }

    default List<HbjflDO> selectList(HbjflExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HbjflDO>()
                .eqIfPresent(HbjflDO::getBflmc, reqVO.getBflmc())
                .eqIfPresent(HbjflDO::getCbjsfs, reqVO.getCbjsfs())
                .eqIfPresent(HbjflDO::getQybs, reqVO.getQybs())
                .eqIfPresent(HbjflDO::getBz, reqVO.getBz())
                .betweenIfPresent(HbjflDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HbjflDO::getId));
    }

}
