package com.neway.assets.module.info.dal.mysql.kcdd;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.kcdd.KcddDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.kcdd.vo.*;

/**
 * 库存地点 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KcddMapper extends BaseMapperX<KcddDO> {

    default PageResult<KcddDO> selectPage(KcddPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<KcddDO>()
                .eqIfPresent(KcddDO::getGs, reqVO.getGs())
                .eqIfPresent(KcddDO::getGc, reqVO.getGc())
                .likeIfPresent(KcddDO::getKcddms, reqVO.getKcddms())
                .eqIfPresent(KcddDO::getKcddbh, reqVO.getKcddbh())
                .eqIfPresent(KcddDO::getQybs, reqVO.getQybs())
                .eqIfPresent(KcddDO::getBz, reqVO.getBz())
                .betweenIfPresent(KcddDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(KcddDO::getId));
    }

    default List<KcddDO> selectList(KcddExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<KcddDO>()
                .eqIfPresent(KcddDO::getGs, reqVO.getGs())
                .eqIfPresent(KcddDO::getGc, reqVO.getGc())
                .likeIfPresent(KcddDO::getKcddms, reqVO.getKcddms())
                .eqIfPresent(KcddDO::getKcddbh, reqVO.getKcddbh())
                .eqIfPresent(KcddDO::getQybs, reqVO.getQybs())
                .eqIfPresent(KcddDO::getBz, reqVO.getBz())
                .betweenIfPresent(KcddDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(KcddDO::getId));
    }

}
