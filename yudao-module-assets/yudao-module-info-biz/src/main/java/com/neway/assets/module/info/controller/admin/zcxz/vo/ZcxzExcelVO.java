package com.neway.assets.module.info.controller.admin.zcxz.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 资产性质 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcxzExcelVO {

    @ExcelProperty("自增长id")
    private Integer id;

    @ExcelProperty("资产性质描述")
    private String zcxzms;

    @ExcelProperty(value = "启用标识（1:启用 ；0:未启用）", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
