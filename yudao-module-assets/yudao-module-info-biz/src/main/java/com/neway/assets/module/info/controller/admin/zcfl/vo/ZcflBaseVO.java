package com.neway.assets.module.info.controller.admin.zcfl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 资产分类 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcflBaseVO {

    @Schema(description = "资产分类方式", required = true)
    @NotNull(message = "资产分类方式不能为空")
    private Long zcflfs;

    @Schema(description = "资产分类描述", required = true)
    @NotNull(message = "资产分类描述不能为空")
    private String zcflms;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
