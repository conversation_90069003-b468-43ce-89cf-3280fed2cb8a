package com.neway.assets.module.info.controller.admin.zcflfs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 资产分类方式更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcflfsUpdateReqVO extends ZcflfsBaseVO {

    @Schema(description = "自增长id", required = true, example = "16016")
    @NotNull(message = "自增长id不能为空")
    private Integer id;

}
