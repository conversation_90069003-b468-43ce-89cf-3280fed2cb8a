package com.neway.assets.module.info.controller.admin.hjxx.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 货架 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class HjxxExcelVO {

    @ExcelProperty("库位（bs_kwxx-->id）")
    private Long kw;

    @ExcelProperty("货架编号(行+列+层+单元)")
    private String hjbh;

    @ExcelProperty("货架描述")
    private String hjms;

    @ExcelProperty("货架所在行")
    private Integer hang;

    @ExcelProperty("货架所在列")
    private Integer lie;

    @ExcelProperty("货架所在列")
    private Integer ceng;

    @ExcelProperty("货架所在层的单元格")
    private Integer dany;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
