package com.neway.assets.module.info.service.jldwdz;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.*;
import com.neway.assets.module.info.dal.dataobject.jldwdz.JldwdzDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.jldwdz.JldwdzConvert;
import com.neway.assets.module.info.dal.mysql.jldwdz.JldwdzMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 计量单位转换对照 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JldwdzServiceImpl implements JldwdzService {

    @Resource
    private JldwdzMapper jldwdzMapper;

    @Override
    public Long createJldwdz(JldwdzCreateReqVO createReqVO) {
        // 插入
        JldwdzDO jldwdz = JldwdzConvert.INSTANCE.convert(createReqVO);
        jldwdzMapper.insert(jldwdz);
        // 返回
        return jldwdz.getId();
    }

    @Override
    public void updateJldwdz(JldwdzUpdateReqVO updateReqVO) {
        // 校验存在
        validateJldwdzExists(updateReqVO.getId());
        // 更新
        JldwdzDO updateObj = JldwdzConvert.INSTANCE.convert(updateReqVO);
        jldwdzMapper.updateById(updateObj);
    }

    @Override
    public void deleteJldwdz(Long id) {
        // 校验存在
        validateJldwdzExists(id);
        // 删除
        jldwdzMapper.deleteById(id);
    }

    private void validateJldwdzExists(Long id) {
        if (jldwdzMapper.selectById(id) == null) {
            throw exception(JLDWDZ_NOT_EXISTS);
        }
    }

    @Override
    public JldwdzDO getJldwdz(Long id) {
        return jldwdzMapper.selectById(id);
    }

    @Override
    public List<JldwdzDO> getJldwdzList(Collection<Long> ids) {
        return jldwdzMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<JldwdzDO> getJldwdzPage(JldwdzPageReqVO pageReqVO) {
        return jldwdzMapper.selectPage(pageReqVO);
    }

    @Override
    public List<JldwdzDO> getJldwdzList(JldwdzExportReqVO exportReqVO) {
        return jldwdzMapper.selectList(exportReqVO);
    }

}
