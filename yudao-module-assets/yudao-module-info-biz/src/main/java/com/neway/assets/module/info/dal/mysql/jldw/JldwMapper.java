package com.neway.assets.module.info.dal.mysql.jldw;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.jldw.JldwDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.jldw.vo.*;

/**
 * 计量单位 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JldwMapper extends BaseMapperX<JldwDO> {

    default PageResult<JldwDO> selectPage(JldwPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JldwDO>()
                .likeIfPresent(JldwDO::getJldwjc, reqVO.getJldwjc())
                .likeIfPresent(JldwDO::getJldwmc, reqVO.getJldwmc())
                .eqIfPresent(JldwDO::getQybs, reqVO.getQybs())
                .eqIfPresent(JldwDO::getBz, reqVO.getBz())
                .betweenIfPresent(JldwDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JldwDO::getId));
    }

    default List<JldwDO> selectList(JldwExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<JldwDO>()
                .likeIfPresent(JldwDO::getJldwjc, reqVO.getJldwjc())
                .likeIfPresent(JldwDO::getJldwmc, reqVO.getJldwmc())
                .eqIfPresent(JldwDO::getQybs, reqVO.getQybs())
                .eqIfPresent(JldwDO::getBz, reqVO.getBz())
                .betweenIfPresent(JldwDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JldwDO::getId));
    }

}
