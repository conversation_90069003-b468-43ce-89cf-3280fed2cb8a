package com.neway.assets.module.info.controller.admin.zcfl;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.zcfl.vo.*;
import com.neway.assets.module.info.convert.zcfl.ZcflConvert;
import com.neway.assets.module.info.dal.dataobject.zcfl.ZcflDO;
import com.neway.assets.module.info.service.zcfl.ZcflService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产分类")
@RestController
@RequestMapping("/info/zcfl")
@Validated
public class ZcflController {

    @Resource
    private ZcflService zcflService;

    @PostMapping("/create")
    @Operation(summary = "创建资产分类")
    @PreAuthorize("@ss.hasPermission('info:zcfl:create')")
    public CommonResult<Long> createZcfl(@Valid @RequestBody ZcflCreateReqVO createReqVO) {
        return success(zcflService.createZcfl(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产分类")
    @PreAuthorize("@ss.hasPermission('info:zcfl:update')")
    public CommonResult<Boolean> updateZcfl(@Valid @RequestBody ZcflUpdateReqVO updateReqVO) {
        zcflService.updateZcfl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:zcfl:delete')")
    public CommonResult<Boolean> deleteZcfl(@RequestParam("id") Long id) {
        zcflService.deleteZcfl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:zcfl:query')")
    public CommonResult<ZcflRespVO> getZcfl(@RequestParam("id") Long id) {
        ZcflDO zcfl = zcflService.getZcfl(id);
        return success(ZcflConvert.INSTANCE.convert(zcfl));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产分类列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('info:zcfl:query')")
    public CommonResult<List<ZcflRespVO>> getZcflList(@Valid ZcflExportReqVO reqVO) {
        List<ZcflDO> list = zcflService.getZcflList(reqVO);
        return success(ZcflConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产分类分页")
    @PreAuthorize("@ss.hasPermission('info:zcfl:query')")
    public CommonResult<PageResult<ZcflRespVO>> getZcflPage(@Valid ZcflPageReqVO pageVO) {
        PageResult<ZcflDO> pageResult = zcflService.getZcflPage(pageVO);
        return success(ZcflConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产分类 Excel")
    @PreAuthorize("@ss.hasPermission('info:zcfl:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcflExcel(@Valid ZcflExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcflDO> list = zcflService.getZcflList(exportReqVO);
        // 导出 Excel
        List<ZcflExcelVO> datas = ZcflConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产分类.xls", "数据", ZcflExcelVO.class, datas);
    }

}
