package com.neway.assets.module.info.dal.dataobject.zjfs;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 资产折旧方式 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_zjfs", schema = "gdzc")
@KeySequence("bs_zjfs_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZjfsDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 资产分类
     */
    private Long zcfl;
    /**
     * 折旧年限
     */
    private BigDecimal zjnx;
    /**
     * 残值率
     */
    private BigDecimal czl;
    /**
     * 是否当月折旧
     */
    private String sfdyzj;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
