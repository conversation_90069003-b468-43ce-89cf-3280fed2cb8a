package com.neway.assets.module.info.dal.mysql.secondzcfl;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.secondzcfl.SecondZcflDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.*;

/**
 * 资产二级分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SecondZcflMapper extends BaseMapperX<SecondZcflDO> {

    default PageResult<SecondZcflDO> selectPage(SecondZcflPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SecondZcflDO>()
                .eqIfPresent(SecondZcflDO::getZcflfsId, reqVO.getZcflfsId())
                .likeIfPresent(SecondZcflDO::getZcflms, reqVO.getZcflms())
                .eqIfPresent(SecondZcflDO::getQybs, reqVO.getQybs())
                .eqIfPresent(SecondZcflDO::getBz, reqVO.getBz())
                .betweenIfPresent(SecondZcflDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SecondZcflDO::getId));
    }

    default List<SecondZcflDO> selectList(SecondZcflExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SecondZcflDO>()
                .eqIfPresent(SecondZcflDO::getZcflfsId, reqVO.getZcflfsId())
                .likeIfPresent(SecondZcflDO::getZcflms, reqVO.getZcflms())
                .eqIfPresent(SecondZcflDO::getQybs, reqVO.getQybs())
                .eqIfPresent(SecondZcflDO::getBz, reqVO.getBz())
                .betweenIfPresent(SecondZcflDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SecondZcflDO::getId));
    }

}
