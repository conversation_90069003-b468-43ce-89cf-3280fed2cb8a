package com.neway.assets.module.info.controller.admin.bzxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 币种信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BzxxUpdateReqVO extends BzxxBaseVO {

    @Schema(description = "自增长id", required = true, example = "18786")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
