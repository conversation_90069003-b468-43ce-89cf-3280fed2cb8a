package com.neway.assets.module.info.api.zjfs;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.zjfs.dto.ZjfsRespDTO;
import com.neway.assets.module.info.convert.zjfs.ZjfsConvert;
import com.neway.assets.module.info.dal.dataobject.zjfs.ZjfsDO;
import com.neway.assets.module.info.service.zjfs.ZjfsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 13:56
 **/
@Service
public class ZjfsApiImpl implements ZjfsApi{

    @Resource
    private ZjfsService zjfsService;

    @Override
    public List<ZjfsRespDTO> getZjfsList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<ZjfsDO> zjfsList = zjfsService.getZjfsList(ids);
        return ZjfsConvert.INSTANCE.convertList03(zjfsList);
    }
}
