package com.neway.assets.module.info.service.kcdd;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.controller.admin.kcdd.vo.*;
import com.neway.assets.module.info.dal.dataobject.kcdd.KcddDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 库存地点 Service 接口
 *
 * <AUTHOR>
 */
public interface KcddService {

    /**
     * 创建库存地点
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createKcdd(@Valid KcddCreateReqVO createReqVO);

    /**
     * 更新库存地点
     *
     * @param updateReqVO 更新信息
     */
    void updateKcdd(@Valid KcddUpdateReqVO updateReqVO);

    /**
     * 删除库存地点
     *
     * @param id 编号
     */
    void deleteKcdd(Long id);

    /**
     * 获得库存地点
     *
     * @param id 编号
     * @return 库存地点
     */
    KcddDO getKcdd(Long id);

    /**
     * 获得库存地点列表
     *
     * @param ids 编号
     * @return 库存地点列表
     */
    List<KcddDO> getKcddList(Collection<Long> ids);

    /**
     * 获得库存地点分页
     *
     * @param pageReqVO 分页查询
     * @return 库存地点分页
     */
    PageResult<KcddDO> getKcddPage(KcddPageReqVO pageReqVO);


    PageResult<KcddPageItemRespVO> getKcddPageDetial(KcddPageReqVO pageReqVO);

    /**
     * 获得库存地点列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 库存地点列表
     */
    List<KcddDO> getKcddList(KcddExportReqVO exportReqVO);

}
