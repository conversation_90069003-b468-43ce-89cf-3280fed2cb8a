package com.neway.assets.module.info.service.zcxz;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.zcxz.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcxz.ZcxzDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.zcxz.ZcxzConvert;
import com.neway.assets.module.info.dal.mysql.zcxz.ZcxzMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 资产性质 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZcxzServiceImpl implements ZcxzService {

    @Resource
    private ZcxzMapper zcxzMapper;

    @Override
    public Integer createZcxz(ZcxzCreateReqVO createReqVO) {
        // 插入
        ZcxzDO zcxz = ZcxzConvert.INSTANCE.convert(createReqVO);
        zcxzMapper.insert(zcxz);
        // 返回
        return zcxz.getId();
    }

    @Override
    public void updateZcxz(ZcxzUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcxzExists(updateReqVO.getId());
        // 更新
        ZcxzDO updateObj = ZcxzConvert.INSTANCE.convert(updateReqVO);
        zcxzMapper.updateById(updateObj);
    }

    @Override
    public void deleteZcxz(Integer id) {
        // 校验存在
        validateZcxzExists(id);
        // 删除
        zcxzMapper.deleteById(id);
    }

    private void validateZcxzExists(Integer id) {
        if (zcxzMapper.selectById(id) == null) {
            throw exception(ZCXZ_NOT_EXISTS);
        }
    }

    @Override
    public ZcxzDO getZcxz(Integer id) {
        return zcxzMapper.selectById(id);
    }

    @Override
    public List<ZcxzDO> getZcxzList(Collection<Integer> ids) {
        return zcxzMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcxzDO> getZcxzPage(ZcxzPageReqVO pageReqVO) {
        return zcxzMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZcxzDO> getZcxzList(ZcxzExportReqVO exportReqVO) {
        return zcxzMapper.selectList(exportReqVO);
    }

}
