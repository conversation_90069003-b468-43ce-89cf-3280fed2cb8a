package com.neway.assets.module.info.dal.dataobject.bzxx;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 币种信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_bzxx", schema = "gdzc")
@KeySequence("bs_bzxx_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BzxxDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 币种简称
     */
    private String bzjc;
    /**
     * 币种名称
     */
    private String bzmc;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
