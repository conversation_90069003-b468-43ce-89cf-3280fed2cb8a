package com.neway.assets.module.info.dal.dataobject.hbjfl;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 耗材/备品备件分类 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_hbjfl", schema = "gdzc")
@KeySequence("bs_hbjfl_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HbjflDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 分类名称
     */
    private String bflmc;
    /**
     * 库存成本计算方式（0：移动平均，1：批次价格）
     *
     */
    private Integer cbjsfs;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
