package com.neway.assets.module.info.service.jldwdz;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.*;
import com.neway.assets.module.info.dal.dataobject.jldwdz.JldwdzDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 计量单位转换对照 Service 接口
 *
 * <AUTHOR>
 */
public interface JldwdzService {

    /**
     * 创建计量单位转换对照
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJldwdz(@Valid JldwdzCreateReqVO createReqVO);

    /**
     * 更新计量单位转换对照
     *
     * @param updateReqVO 更新信息
     */
    void updateJldwdz(@Valid JldwdzUpdateReqVO updateReqVO);

    /**
     * 删除计量单位转换对照
     *
     * @param id 编号
     */
    void deleteJldwdz(Long id);

    /**
     * 获得计量单位转换对照
     *
     * @param id 编号
     * @return 计量单位转换对照
     */
    JldwdzDO getJldwdz(Long id);

    /**
     * 获得计量单位转换对照列表
     *
     * @param ids 编号
     * @return 计量单位转换对照列表
     */
    List<JldwdzDO> getJldwdzList(Collection<Long> ids);

    /**
     * 获得计量单位转换对照分页
     *
     * @param pageReqVO 分页查询
     * @return 计量单位转换对照分页
     */
    PageResult<JldwdzDO> getJldwdzPage(JldwdzPageReqVO pageReqVO);

    /**
     * 获得计量单位转换对照列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 计量单位转换对照列表
     */
    List<JldwdzDO> getJldwdzList(JldwdzExportReqVO exportReqVO);

}
