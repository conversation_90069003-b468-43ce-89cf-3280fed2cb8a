package com.neway.assets.module.info.controller.admin.zcfl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产分类 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcflRespVO extends ZcflBaseVO {

    @Schema(description = "部门编号", required = true, example = "1024")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
