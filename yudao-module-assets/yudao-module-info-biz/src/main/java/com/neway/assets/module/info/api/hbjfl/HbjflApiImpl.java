package com.neway.assets.module.info.api.hbjfl;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.hbjfl.dto.HbjflRespDTO;
import com.neway.assets.module.info.convert.hbjfl.HbjflConvert;
import com.neway.assets.module.info.dal.dataobject.hbjfl.HbjflDO;
import com.neway.assets.module.info.service.hbjfl.HbjflService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 备品备件分类API接口实现类
 * <AUTHOR>
 * @since 2023/6/7 10:07
 **/
@Service
@RequiredArgsConstructor
public class HbjflApiImpl implements HbjflApi{

    private final HbjflService hbjflService;

    @Override
    public List<HbjflRespDTO> getHbflList(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<HbjflDO> list = hbjflService.getHbjflList(ids);
        return HbjflConvert.INSTANCE.convertList03(list);
    }
}
