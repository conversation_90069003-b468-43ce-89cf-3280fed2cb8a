package com.neway.assets.module.info.controller.admin.jldw;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.jldw.vo.*;
import com.neway.assets.module.info.convert.jldw.JldwConvert;
import com.neway.assets.module.info.dal.dataobject.jldw.JldwDO;
import com.neway.assets.module.info.service.jldw.JldwService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 计量单位")
@RestController
@RequestMapping("/info/jldw")
@Validated
public class JldwController {

    @Resource
    private JldwService jldwService;

    @PostMapping("/create")
    @Operation(summary = "创建计量单位")
    @PreAuthorize("@ss.hasPermission('info:jldw:create')")
    public CommonResult<Long> createJldw(@Valid @RequestBody JldwCreateReqVO createReqVO) {
        return success(jldwService.createJldw(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新计量单位")
    @PreAuthorize("@ss.hasPermission('info:jldw:update')")
    public CommonResult<Boolean> updateJldw(@Valid @RequestBody JldwUpdateReqVO updateReqVO) {
        jldwService.updateJldw(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除计量单位")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:jldw:delete')")
    public CommonResult<Boolean> deleteJldw(@RequestParam("id") Long id) {
        jldwService.deleteJldw(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得计量单位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:jldw:query')")
    public CommonResult<JldwRespVO> getJldw(@RequestParam("id") Long id) {
        JldwDO jldw = jldwService.getJldw(id);
        return success(JldwConvert.INSTANCE.convert(jldw));
    }

    @GetMapping("/list")
    @Operation(summary = "获得计量单位列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('info:jldw:query')")
    public CommonResult<List<JldwRespVO>> getJldwList(@Valid JldwExportReqVO reqVO) {
        List<JldwDO> list = jldwService.getJldwList(reqVO);
        return success(JldwConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得计量单位分页")
    @PreAuthorize("@ss.hasPermission('info:jldw:query')")
    public CommonResult<PageResult<JldwRespVO>> getJldwPage(@Valid JldwPageReqVO pageVO) {
        PageResult<JldwDO> pageResult = jldwService.getJldwPage(pageVO);
        return success(JldwConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出计量单位 Excel")
    @PreAuthorize("@ss.hasPermission('info:jldw:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportJldwExcel(@Valid JldwExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<JldwDO> list = jldwService.getJldwList(exportReqVO);
        // 导出 Excel
        List<JldwExcelVO> datas = JldwConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "计量单位.xls", "数据", JldwExcelVO.class, datas);
    }

}
