package com.neway.assets.module.info.controller.admin.kcdd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 库存地点 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class KcddBaseVO {

    @Schema(description = "隶属公司", required = true)
    @NotNull(message = "隶属公司不能为空")
    private Long gs;

    @Schema(description = "隶属工厂", required = true)
    @NotNull(message = "隶属工厂不能为空")
    private Long gc;

    @Schema(description = "库存地点描述", required = true)
    @NotNull(message = "库存地点描述不能为空")
    private String kcddms;

    @Schema(description = "库存地点编号", required = true)
    @NotNull(message = "库存地点编号不能为空")
    private String kcddbh;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
