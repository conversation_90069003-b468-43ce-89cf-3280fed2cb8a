package com.neway.assets.module.info.api.zcflZgy;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.zcflZgy.dto.ZcflZgyRespDTO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyExportReqVO;
import com.neway.assets.module.info.convert.zcflzgy.ZcflZgyConvert;
import com.neway.assets.module.info.dal.dataobject.zcflzgy.ZcflZgyDO;
import com.neway.assets.module.info.service.zcflzgy.ZcflZgyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/16 14:13
 **/
@Service
public class ZcflZgyApiImpl implements ZcflZgyApi{
    @Resource
    private ZcflZgyService zcflZgyService;

    @Override
    public List<ZcflZgyRespDTO> getZcflZgyList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<ZcflZgyDO> zcflZgyList = zcflZgyService.getZcflZgyList(ids);
        return ZcflZgyConvert.INSTANCE.convertList03(zcflZgyList);
    }

    @Override
    public List<ZcflZgyRespDTO> getZcflZgyListByUserId(Long userId) {
        if (userId == null) return Collections.emptyList();
        List<ZcflZgyDO> zcflZgyList = zcflZgyService.getZcflZgyList(new ZcflZgyExportReqVO().setUserId(userId));
        return ZcflZgyConvert.INSTANCE.convertList03(zcflZgyList);
    }
}
