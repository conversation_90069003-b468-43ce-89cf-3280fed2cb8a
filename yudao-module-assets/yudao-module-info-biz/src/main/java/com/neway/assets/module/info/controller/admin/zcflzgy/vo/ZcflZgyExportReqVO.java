package com.neway.assets.module.info.controller.admin.zcflzgy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产分类资管员关联 Excel 导出 Request VO，参数和 ZcflZgyPageReqVO 是一致的")
@Data
public class ZcflZgyExportReqVO {

    @Schema(description = "公司")
    private Long gs;

    @Schema(description = "一级分类id", example = "11015")
    private Long zcflId;

    @Schema(description = "用户id", example = "1947")
    private Long userId;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
