package com.neway.assets.module.info.controller.admin.zcxz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产性质 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcxzRespVO extends ZcxzBaseVO {

    @Schema(description = "自增长id", required = true, example = "6163")
    private Integer id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
