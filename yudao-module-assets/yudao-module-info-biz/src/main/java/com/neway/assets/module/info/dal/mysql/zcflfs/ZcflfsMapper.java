package com.neway.assets.module.info.dal.mysql.zcflfs;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.ZcflfsExportReqVO;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.ZcflfsPageReqVO;
import com.neway.assets.module.info.dal.dataobject.zcflfs.ZcflfsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产分类方式 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcflfsMapper extends BaseMapperX<ZcflfsDO> {

    default PageResult<ZcflfsDO> selectPage(ZcflfsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcflfsDO>()
                .likeIfPresent(ZcflfsDO::getFlfssm, reqVO.getFlfssm())
                .eqIfPresent(ZcflfsDO::getQybs, reqVO.getQybs())
                .likeIfPresent(ZcflfsDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcflfsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcflfsDO::getId));
    }

    default List<ZcflfsDO> selectList(ZcflfsExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcflfsDO>()
                .likeIfPresent(ZcflfsDO::getFlfssm, reqVO.getFlfssm())
                .eqIfPresent(ZcflfsDO::getQybs, reqVO.getQybs())
                .likeIfPresent(ZcflfsDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcflfsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcflfsDO::getId));
    }

}
