package com.neway.assets.module.info.service.zcxz;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.zcxz.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcxz.ZcxzDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产性质 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcxzService {

    /**
     * 创建资产性质
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createZcxz(@Valid ZcxzCreateReqVO createReqVO);

    /**
     * 更新资产性质
     *
     * @param updateReqVO 更新信息
     */
    void updateZcxz(@Valid ZcxzUpdateReqVO updateReqVO);

    /**
     * 删除资产性质
     *
     * @param id 编号
     */
    void deleteZcxz(Integer id);

    /**
     * 获得资产性质
     *
     * @param id 编号
     * @return 资产性质
     */
    ZcxzDO getZcxz(Integer id);

    /**
     * 获得资产性质列表
     *
     * @param ids 编号
     * @return 资产性质列表
     */
    List<ZcxzDO> getZcxzList(Collection<Integer> ids);

    /**
     * 获得资产性质分页
     *
     * @param pageReqVO 分页查询
     * @return 资产性质分页
     */
    PageResult<ZcxzDO> getZcxzPage(ZcxzPageReqVO pageReqVO);

    /**
     * 获得资产性质列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产性质列表
     */
    List<ZcxzDO> getZcxzList(ZcxzExportReqVO exportReqVO);

}
