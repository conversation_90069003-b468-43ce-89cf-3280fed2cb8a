package com.neway.assets.module.info.controller.admin.zcflfs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产分类方式 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcflfsRespVO extends ZcflfsBaseVO {

    @Schema(description = "分类方式id")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
