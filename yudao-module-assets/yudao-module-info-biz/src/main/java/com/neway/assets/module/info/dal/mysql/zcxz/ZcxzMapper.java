package com.neway.assets.module.info.dal.mysql.zcxz;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.info.controller.admin.zcxz.vo.ZcxzExportReqVO;
import com.neway.assets.module.info.controller.admin.zcxz.vo.ZcxzPageReqVO;
import com.neway.assets.module.info.dal.dataobject.zcxz.ZcxzDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产性质 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcxzMapper extends BaseMapperX<ZcxzDO> {

    default PageResult<ZcxzDO> selectPage(ZcxzPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcxzDO>()
                .likeIfPresent(ZcxzDO::getZcxzms, reqVO.getZcxzms())
                .eqIfPresent(ZcxzDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcxzDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcxzDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcxzDO::getId));
    }

    default List<ZcxzDO> selectList(ZcxzExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcxzDO>()
                .likeIfPresent(ZcxzDO::getZcxzms, reqVO.getZcxzms())
                .eqIfPresent(ZcxzDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcxzDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcxzDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcxzDO::getId));
    }

}
