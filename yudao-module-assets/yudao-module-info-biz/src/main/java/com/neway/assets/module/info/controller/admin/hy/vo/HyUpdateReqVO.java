package com.neway.assets.module.info.controller.admin.hy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 行业更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HyUpdateReqVO extends HyBaseVO {

    @Schema(description = "自增长id", required = true, example = "4769")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
