package com.neway.assets.module.info.api.hjxx;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.info.api.hjxx.dto.HjxxRespDTO;
import com.neway.assets.module.info.convert.hjxx.HjxxConvert;
import com.neway.assets.module.info.dal.dataobject.hjxx.HjxxDO;
import com.neway.assets.module.info.service.hjxx.HjxxService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/6/13 15:29
 **/
@Service
@RequiredArgsConstructor
public class HjxxApiImpl implements HjxxApi{

    private final HjxxService hjxxService;

    @Override
    public List<HjxxRespDTO> getHjxxList(Set<Long> ids) {
        if (CollectionUtils.isAnyEmpty(ids)) return Collections.emptyList();
        List<HjxxDO> hjxxList = hjxxService.getHjxxList(ids);
        return HjxxConvert.INSTANCE.convertList03(hjxxList);
    }
}
