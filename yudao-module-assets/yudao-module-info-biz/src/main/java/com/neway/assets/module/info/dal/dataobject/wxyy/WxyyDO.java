package com.neway.assets.module.info.dal.dataobject.wxyy;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 维护/维修原因 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_wxyy", schema = "gdzc")
@KeySequence("bs_wxyy_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxyyDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 维修维护原因描述
     */
    private String wxyyms;
    /**
     * 启用标识（1:启用 ；0:未启用）
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
