package com.neway.assets.module.info.service.zjfs;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.zjfs.vo.*;
import com.neway.assets.module.info.dal.dataobject.zjfs.ZjfsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.zjfs.ZjfsConvert;
import com.neway.assets.module.info.dal.mysql.zjfs.ZjfsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 资产折旧方式 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZjfsServiceImpl implements ZjfsService {

    @Resource
    private ZjfsMapper zjfsMapper;

    @Override
    public Long createZjfs(ZjfsCreateReqVO createReqVO) {
        // 插入
        ZjfsDO zjfs = ZjfsConvert.INSTANCE.convert(createReqVO);
        zjfsMapper.insert(zjfs);
        // 返回
        return zjfs.getId();
    }

    @Override
    public void updateZjfs(ZjfsUpdateReqVO updateReqVO) {
        // 校验存在
        validateZjfsExists(updateReqVO.getId());
        // 更新
        ZjfsDO updateObj = ZjfsConvert.INSTANCE.convert(updateReqVO);
        zjfsMapper.updateById(updateObj);
    }

    @Override
    public void deleteZjfs(Long id) {
        // 校验存在
        validateZjfsExists(id);
        // 删除
        zjfsMapper.deleteById(id);
    }

    private void validateZjfsExists(Long id) {
        if (zjfsMapper.selectById(id) == null) {
            throw exception(ZJFS_NOT_EXISTS);
        }
    }

    @Override
    public ZjfsDO getZjfs(Long id) {
        return zjfsMapper.selectById(id);
    }

    @Override
    public List<ZjfsDO> getZjfsList(Collection<Long> ids) {
        return zjfsMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZjfsDO> getZjfsPage(ZjfsPageReqVO pageReqVO) {
        return zjfsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZjfsDO> getZjfsList(ZjfsExportReqVO exportReqVO) {
        return zjfsMapper.selectList(exportReqVO);
    }

}
