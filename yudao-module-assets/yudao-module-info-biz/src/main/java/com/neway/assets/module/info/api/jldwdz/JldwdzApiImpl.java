package com.neway.assets.module.info.api.jldwdz;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.JldwdzExportReqVO;
import com.neway.assets.module.info.dal.dataobject.jldwdz.JldwdzDO;
import com.neway.assets.module.info.enums.enable.QybsStatusEnum;
import com.neway.assets.module.info.service.jldwdz.JldwdzService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.JLDWDZ_NOT_ROUNDED;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.JLDWDZ_NO_MATCHED;

/**
 * <AUTHOR>
 * @since 2023/8/23 9:59
 **/
@Service
public class JldwdzApiImpl implements JldwdzApi{

    @Resource
    private JldwdzService jldwdzService;

    @Override
    public BigDecimal transferUnit(Long origId, Long targetId, BigDecimal originQty) {
        List<JldwdzDO> convertList = jldwdzService.getJldwdzList(new JldwdzExportReqVO()
                .setYjldw(origId).setZhdw(targetId)
                .setQybs(QybsStatusEnum.ENABLED.getVal()));
        if (CollUtil.isEmpty(convertList)) throw exception(JLDWDZ_NO_MATCHED);
        try {
            return originQty.divide(convertList.get(0).getYuansl(), RoundingMode.UNNECESSARY).multiply(convertList.get(0).getZhsl());
        } catch (ArithmeticException e) {
            throw exception(JLDWDZ_NOT_ROUNDED);
        }

    }
}
