package com.neway.assets.module.info.service.zcfl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.zcfl.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcfl.ZcflDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.zcfl.ZcflConvert;
import com.neway.assets.module.info.dal.mysql.zcfl.ZcflMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 资产分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZcflServiceImpl implements ZcflService {

    @Resource
    private ZcflMapper zcflMapper;

    @Override
    public Long createZcfl(ZcflCreateReqVO createReqVO) {
        // 插入
        ZcflDO zcfl = ZcflConvert.INSTANCE.convert(createReqVO);
        zcflMapper.insert(zcfl);
        // 返回
        return zcfl.getId();
    }

    @Override
    public void updateZcfl(ZcflUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcflExists(updateReqVO.getId());
        // 更新
        ZcflDO updateObj = ZcflConvert.INSTANCE.convert(updateReqVO);
        zcflMapper.updateById(updateObj);
    }

    @Override
    public void deleteZcfl(Long id) {
        // 校验存在
        validateZcflExists(id);
        // 删除
        zcflMapper.deleteById(id);
    }

    private void validateZcflExists(Long id) {
        if (zcflMapper.selectById(id) == null) {
            throw exception(ZCFL_NOT_EXISTS);
        }
    }

    @Override
    public ZcflDO getZcfl(Long id) {
        return zcflMapper.selectById(id);
    }

    @Override
    public List<ZcflDO> getZcflList(Collection<Long> ids) {
        return zcflMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcflDO> getZcflPage(ZcflPageReqVO pageReqVO) {
        return zcflMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZcflDO> getZcflList(ZcflExportReqVO exportReqVO) {
        return zcflMapper.selectList(exportReqVO);
    }

}
