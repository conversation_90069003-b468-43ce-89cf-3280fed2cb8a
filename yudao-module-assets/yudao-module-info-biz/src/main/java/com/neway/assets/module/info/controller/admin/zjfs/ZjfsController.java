package com.neway.assets.module.info.controller.admin.zjfs;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.zjfs.vo.*;
import com.neway.assets.module.info.convert.zjfs.ZjfsConvert;
import com.neway.assets.module.info.dal.dataobject.zjfs.ZjfsDO;
import com.neway.assets.module.info.service.zjfs.ZjfsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产折旧方式")
@RestController
@RequestMapping("/info/zjfs")
@Validated
public class ZjfsController {

    @Resource
    private ZjfsService zjfsService;

    @PostMapping("/create")
    @Operation(summary = "创建资产折旧方式")
    @PreAuthorize("@ss.hasPermission('info:zjfs:create')")
    public CommonResult<Long> createZjfs(@Valid @RequestBody ZjfsCreateReqVO createReqVO) {
        return success(zjfsService.createZjfs(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产折旧方式")
    @PreAuthorize("@ss.hasPermission('info:zjfs:update')")
    public CommonResult<Boolean> updateZjfs(@Valid @RequestBody ZjfsUpdateReqVO updateReqVO) {
        zjfsService.updateZjfs(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产折旧方式")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:zjfs:delete')")
    public CommonResult<Boolean> deleteZjfs(@RequestParam("id") Long id) {
        zjfsService.deleteZjfs(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产折旧方式")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:zjfs:query')")
    public CommonResult<ZjfsRespVO> getZjfs(@RequestParam("id") Long id) {
        ZjfsDO zjfs = zjfsService.getZjfs(id);
        return success(ZjfsConvert.INSTANCE.convert(zjfs));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产折旧方式列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('info:zjfs:query')")
    public CommonResult<List<ZjfsRespVO>> getZjfsList(@RequestParam("ids") Collection<Long> ids) {
        List<ZjfsDO> list = zjfsService.getZjfsList(ids);
        return success(ZjfsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产折旧方式分页")
    @PreAuthorize("@ss.hasPermission('info:zjfs:query')")
    public CommonResult<PageResult<ZjfsRespVO>> getZjfsPage(@Valid ZjfsPageReqVO pageVO) {
        PageResult<ZjfsDO> pageResult = zjfsService.getZjfsPage(pageVO);
        return success(ZjfsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产折旧方式 Excel")
    @PreAuthorize("@ss.hasPermission('info:zjfs:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZjfsExcel(@Valid ZjfsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZjfsDO> list = zjfsService.getZjfsList(exportReqVO);
        // 导出 Excel
        List<ZjfsExcelVO> datas = ZjfsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产折旧方式.xls", "数据", ZjfsExcelVO.class, datas);
    }

}
