package com.neway.assets.module.info.dal.dataobject.kwxx;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 库位信息 DO
 *
 * <AUTHOR>
 */
@TableName(value="bs_kwxx", schema = "gdzc")
@KeySequence("bs_kwxx_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KwxxDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 库存地点
     */
    private Long kcdd;
    /**
     * 库位编码
     */
    private String kwbh;
    /**
     * 库位描述
     */
    private String kwms;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
