package com.neway.assets.module.info.controller.admin.zjfs.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 资产折旧方式 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZjfsExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("资产分类")
    private Long zcfl;

    @ExcelProperty("折旧年限")
    private BigDecimal zjnx;

    @ExcelProperty("残值率")
    private BigDecimal czl;

    @ExcelProperty("是否当月折旧")
    private String sfdyzj;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
