package com.neway.assets.module.info.service.wxyy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.controller.admin.wxyy.vo.WxyyCreateReqVO;
import com.neway.assets.module.info.controller.admin.wxyy.vo.WxyyExportReqVO;
import com.neway.assets.module.info.controller.admin.wxyy.vo.WxyyPageReqVO;
import com.neway.assets.module.info.controller.admin.wxyy.vo.WxyyUpdateReqVO;
import com.neway.assets.module.info.convert.wxyy.WxyyConvert;
import com.neway.assets.module.info.dal.dataobject.wxyy.WxyyDO;
import com.neway.assets.module.info.dal.mysql.wxyy.WxyyMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.WXYY_NOT_EXISTS;

/**
 * 维护/维修原因 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WxyyServiceImpl implements WxyyService {

    @Resource
    private WxyyMapper wxyyMapper;

    @Override
    public Long createWxyy(WxyyCreateReqVO createReqVO) {
        // 插入
       WxyyDO wxyy = WxyyConvert.INSTANCE.convert(createReqVO);
       wxyyMapper.insert(wxyy);
        // 返回
        return wxyy.getId();
    }

    @Override
    public void updateWxyy(WxyyUpdateReqVO updateReqVO) {
        // 校验存在
        validateWxyyExists(updateReqVO.getId());
        // 更新
        WxyyDO updateObj = WxyyConvert.INSTANCE.convert(updateReqVO);
        wxyyMapper.updateById(updateObj);
    }

    @Override
    public void deleteWxyy(Long id) {
        // 校验存在
        validateWxyyExists(id);
        // 删除
        wxyyMapper.deleteById(id);
    }

    private void validateWxyyExists(Long id) {
        if (wxyyMapper.selectById(id) == null) {
            throw exception(WXYY_NOT_EXISTS);
        }
    }

    @Override
    public WxyyDO getWxyy(Long id) {
        return wxyyMapper.selectById(id);
    }

    @Override
    public List<WxyyDO> getWxyyList(Collection<Long> ids) {
        return wxyyMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WxyyDO> getWxyyPage(WxyyPageReqVO pageReqVO) {
        return wxyyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<WxyyDO> getWxyyList(WxyyExportReqVO exportReqVO) {
        return wxyyMapper.selectList(exportReqVO);
    }

}
