package com.neway.assets.module.info.controller.admin.hjxx;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.hjxx.vo.*;
import com.neway.assets.module.info.convert.hjxx.HjxxConvert;
import com.neway.assets.module.info.dal.dataobject.hjxx.HjxxDO;
import com.neway.assets.module.info.service.hjxx.HjxxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 货架")
@RestController
@RequestMapping("/info/hjxx")
@Validated
public class HjxxController {

    @Resource
    private HjxxService hjxxService;

    @PostMapping("/create")
    @Operation(summary = "创建货架")
    @PreAuthorize("@ss.hasPermission('info:hjxx:create')")
    public CommonResult<Long> createHjxx(@Valid @RequestBody HjxxCreateReqVO createReqVO) {
        return success(hjxxService.createHjxx(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货架")
    @PreAuthorize("@ss.hasPermission('info:hjxx:update')")
    public CommonResult<Boolean> updateHjxx(@Valid @RequestBody HjxxUpdateReqVO updateReqVO) {
        hjxxService.updateHjxx(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货架")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:hjxx:delete')")
    public CommonResult<Boolean> deleteHjxx(@RequestParam("id") Long id) {
        hjxxService.deleteHjxx(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货架")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:hjxx:query')")
    public CommonResult<HjxxRespVO> getHjxx(@RequestParam("id") Long id) {
        HjxxDO hjxx = hjxxService.getHjxx(id);
        return success(HjxxConvert.INSTANCE.convert(hjxx));
    }

    @GetMapping("/list")
    @Operation(summary = "获得货架列表")
    @PreAuthorize("@ss.hasPermission('info:hjxx:query')")
    public CommonResult<List<HjxxRespVO>> getHjxxList(@Valid HjxxExportReqVO reqVO) {
        List<HjxxDO> list = hjxxService.getHjxxList(reqVO);
        return success(HjxxConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货架分页")
    @PreAuthorize("@ss.hasPermission('info:hjxx:query')")
    public CommonResult<PageResult<HjxxRespVO>> getHjxxPage(@Valid HjxxPageReqVO pageVO) {
        PageResult<HjxxDO> pageResult = hjxxService.getHjxxPage(pageVO);
        return success(HjxxConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货架 Excel")
    @PreAuthorize("@ss.hasPermission('info:hjxx:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportHjxxExcel(@Valid HjxxExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<HjxxDO> list = hjxxService.getHjxxList(exportReqVO);
        // 导出 Excel
        List<HjxxExcelVO> datas = HjxxConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "货架.xls", "数据", HjxxExcelVO.class, datas);
    }

}
