package com.neway.assets.module.info.controller.admin.zcflfs;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.*;
import com.neway.assets.module.info.convert.zcflfs.ZcflfsConvert;
import com.neway.assets.module.info.dal.dataobject.zcflfs.ZcflfsDO;
import com.neway.assets.module.info.service.zcflfs.ZcflfsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产分类方式")
@RestController
@RequestMapping("/info/zcflfs")
@Validated
public class ZcflfsController {

    @Resource
    private ZcflfsService zcflfsService;

    @PostMapping("/create")
    @Operation(summary = "创建资产分类方式")
    @PreAuthorize("@ss.hasPermission('info:zcflfs:create')")
    public CommonResult<Integer> createZcflfs(@Valid @RequestBody ZcflfsCreateReqVO createReqVO) {
        return success(zcflfsService.createZcflfs(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产分类方式")
    @PreAuthorize("@ss.hasPermission('info:zcflfs:update')")
    public CommonResult<Boolean> updateZcflfs(@Valid @RequestBody ZcflfsUpdateReqVO updateReqVO) {
        zcflfsService.updateZcflfs(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产分类方式")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:zcflfs:delete')")
    public CommonResult<Boolean> deleteZcflfs(@RequestParam("id") Integer id) {
        zcflfsService.deleteZcflfs(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产分类方式")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:zcflfs:query')")
    public CommonResult<ZcflfsRespVO> getZcflfs(@RequestParam("id") Integer id) {
        ZcflfsDO zcflfs = zcflfsService.getZcflfs(id);
        return success(ZcflfsConvert.INSTANCE.convert(zcflfs));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产分类方式列表")
    @PreAuthorize("@ss.hasPermission('info:zcflfs:query')")
    public CommonResult<List<ZcflfsRespVO>> getZcflfsList(@Valid ZcflfsExportReqVO exportReqVO) {
        List<ZcflfsDO> list = zcflfsService.getZcflfsList(exportReqVO);
        return success(ZcflfsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产分类方式分页")
    @PreAuthorize("@ss.hasPermission('info:zcflfs:query')")
    public CommonResult<PageResult<ZcflfsRespVO>> getZcflfsPage(@Valid ZcflfsPageReqVO pageVO) {
        PageResult<ZcflfsDO> pageResult = zcflfsService.getZcflfsPage(pageVO);
        return success(ZcflfsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产分类方式 Excel")
    @PreAuthorize("@ss.hasPermission('info:zcflfs:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcflfsExcel(@Valid ZcflfsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcflfsDO> list = zcflfsService.getZcflfsList(exportReqVO);
        // 导出 Excel
        List<ZcflfsExcelVO> datas = ZcflfsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产分类方式.xls", "数据", ZcflfsExcelVO.class, datas);
    }

}
