package com.neway.assets.module.info.dal.mysql.wxyy;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.wxyy.WxyyDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.wxyy.vo.*;

/**
 * 维护/维修原因 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WxyyMapper extends BaseMapperX<WxyyDO> {

    default PageResult<WxyyDO> selectPage(WxyyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WxyyDO>()
                .likeIfPresent(WxyyDO::getWxyyms, reqVO.getWxyyms())
                .eqIfPresent(WxyyDO::getQybs, reqVO.getQybs())
                .likeIfPresent(WxyyDO::getBz, reqVO.getBz())
                .betweenIfPresent(WxyyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WxyyDO::getId));
    }

    default List<WxyyDO> selectList(WxyyExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<WxyyDO>()
                .likeIfPresent(WxyyDO::getWxyyms, reqVO.getWxyyms())
                .eqIfPresent(WxyyDO::getQybs, reqVO.getQybs())
                .likeIfPresent(WxyyDO::getBz, reqVO.getBz())
                .betweenIfPresent(WxyyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WxyyDO::getId));
    }

}
