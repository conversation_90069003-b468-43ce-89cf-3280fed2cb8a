package com.neway.assets.module.info.controller.admin.kcdd.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 库存地点 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class KcddExcelVO {

    @ExcelProperty("隶属公司")
    private Long gs;

    @ExcelProperty("隶属工厂")
    private Long gc;

    @ExcelProperty("库存地点描述")
    private String kcddms;

    @ExcelProperty("库存地点编号")
    private String kcddbh;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
