package com.neway.assets.module.info.controller.admin.gcxx.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 工厂信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class GcxxExcelVO {

    @ExcelProperty("隶属公司")
    private Long gs;

    @ExcelProperty("工厂描述")
    private String gcms;

    @ExcelProperty("工厂地址信息")
    private String dzxx;

    @ExcelProperty("工厂地址经纬度")
    private String jwdxx;

    @ExcelProperty("工厂照片链接")
    private String gczp;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
