package com.neway.assets.module.info.controller.admin.zcflfs.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 资产分类方式 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcflfsExcelVO {

    @ExcelProperty("分类方式说明")
    private String flfssm;

    @ExcelProperty(value = "启用标识（1:启用 ；0:未启用）", converter = DictConvert.class)
    @DictFormat("assets_qybs_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
