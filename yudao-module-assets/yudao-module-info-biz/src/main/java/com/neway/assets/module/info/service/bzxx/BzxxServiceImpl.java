package com.neway.assets.module.info.service.bzxx;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.bzxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.bzxx.BzxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.bzxx.BzxxConvert;
import com.neway.assets.module.info.dal.mysql.bzxx.BzxxMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 币种信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BzxxServiceImpl implements BzxxService {

    @Resource
    private BzxxMapper bzxxMapper;

    @Override
    public Long createBzxx(BzxxCreateReqVO createReqVO) {
        // 插入
        BzxxDO bzxx = BzxxConvert.INSTANCE.convert(createReqVO);
        bzxxMapper.insert(bzxx);
        // 返回
        return bzxx.getId();
    }

    @Override
    public void updateBzxx(BzxxUpdateReqVO updateReqVO) {
        // 校验存在
        validateBzxxExists(updateReqVO.getId());
        // 更新
        BzxxDO updateObj = BzxxConvert.INSTANCE.convert(updateReqVO);
        bzxxMapper.updateById(updateObj);
    }

    @Override
    public void deleteBzxx(Long id) {
        // 校验存在
        validateBzxxExists(id);
        // 删除
        bzxxMapper.deleteById(id);
    }

    private void validateBzxxExists(Long id) {
        if (bzxxMapper.selectById(id) == null) {
            throw exception(BZXX_NOT_EXISTS);
        }
    }

    @Override
    public BzxxDO getBzxx(Long id) {
        return bzxxMapper.selectById(id);
    }

    @Override
    public List<BzxxDO> getBzxxList(Collection<Long> ids) {
        return bzxxMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BzxxDO> getBzxxPage(BzxxPageReqVO pageReqVO) {
        return bzxxMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BzxxDO> getBzxxList(BzxxExportReqVO exportReqVO) {
        return bzxxMapper.selectList(exportReqVO);
    }

}
