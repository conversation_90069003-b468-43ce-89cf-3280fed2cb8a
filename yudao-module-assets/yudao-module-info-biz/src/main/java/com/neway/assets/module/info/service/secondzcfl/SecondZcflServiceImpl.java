package com.neway.assets.module.info.service.secondzcfl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflCreateReqVO;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflExportReqVO;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflPageReqVO;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflUpdateReqVO;
import com.neway.assets.module.info.convert.secondzcfl.SecondZcflConvert;
import com.neway.assets.module.info.dal.dataobject.secondzcfl.SecondZcflDO;
import com.neway.assets.module.info.dal.mysql.secondzcfl.SecondZcflMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.SECOND_ZCFL_NOT_EXISTS;

/**
 * 资产二级分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SecondZcflServiceImpl implements SecondZcflService {

    @Resource
    private SecondZcflMapper secondZcflMapper;

    @Override
    public Long createSecondZcfl(SecondZcflCreateReqVO createReqVO) {
        // 插入
        SecondZcflDO secondZcfl = SecondZcflConvert.INSTANCE.convert(createReqVO);
        secondZcflMapper.insert(secondZcfl);
        // 返回
        return secondZcfl.getId();
    }

    @Override
    public void updateSecondZcfl(SecondZcflUpdateReqVO updateReqVO) {
        // 校验存在
        validateSecondZcflExists(updateReqVO.getId());
        // 更新
        SecondZcflDO updateObj = SecondZcflConvert.INSTANCE.convert(updateReqVO);
        secondZcflMapper.updateById(updateObj);
    }

    @Override
    public void deleteSecondZcfl(Long id) {
        // 校验存在
        validateSecondZcflExists(id);
        // 删除
        secondZcflMapper.deleteById(id);
    }

    private void validateSecondZcflExists(Long id) {
        if (secondZcflMapper.selectById(id) == null) {
            throw exception(SECOND_ZCFL_NOT_EXISTS);
        }
    }

    @Override
    public SecondZcflDO getSecondZcfl(Long id) {
        return secondZcflMapper.selectById(id);
    }

    @Override
    public List<SecondZcflDO> getSecondZcflList(Collection<Long> ids) {
        return secondZcflMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SecondZcflDO> getSecondZcflPage(SecondZcflPageReqVO pageReqVO) {
        return secondZcflMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SecondZcflDO> getSecondZcflList(SecondZcflExportReqVO exportReqVO) {
        return secondZcflMapper.selectList(exportReqVO);
    }

}
