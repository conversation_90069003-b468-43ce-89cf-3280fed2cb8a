package com.neway.assets.module.info.controller.admin.bzxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 币种信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BzxxRespVO extends BzxxBaseVO {

    @Schema(description = "自增长id", required = true, example = "18786")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
