package com.neway.assets.module.info.controller.admin.jldwdz.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 计量单位转换对照 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class JldwdzExcelVO {

    @ExcelProperty("成本中心")
    private String cbzx;

    @ExcelProperty("源计量单位")
    private Long yjldw;

    @ExcelProperty("源数量")
    private BigDecimal yuansl;

    @ExcelProperty("转换计量单位")
    private Long zhdw;

    @ExcelProperty("转换数量")
    private BigDecimal zhsl;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
