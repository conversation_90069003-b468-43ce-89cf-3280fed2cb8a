package com.neway.assets.module.info.dal.mysql.kwxx;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.kwxx.KwxxDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.kwxx.vo.*;

/**
 * 库位信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KwxxMapper extends BaseMapperX<KwxxDO> {

    default PageResult<KwxxDO> selectPage(KwxxPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<KwxxDO>()
                .eqIfPresent(KwxxDO::getKcdd, reqVO.getKcdd())
                .eqIfPresent(KwxxDO::getKwbh, reqVO.getKwbh())
                .likeIfPresent(KwxxDO::getKwms, reqVO.getKwms())
                .eqIfPresent(KwxxDO::getQybs, reqVO.getQybs())
                .eqIfPresent(KwxxDO::getBz, reqVO.getBz())
                .betweenIfPresent(KwxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(KwxxDO::getId));
    }

    default List<KwxxDO> selectList(KwxxExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<KwxxDO>()
                .eqIfPresent(KwxxDO::getKcdd, reqVO.getKcdd())
                .eqIfPresent(KwxxDO::getKwbh, reqVO.getKwbh())
                .likeIfPresent(KwxxDO::getKwms, reqVO.getKwms())
                .eqIfPresent(KwxxDO::getQybs, reqVO.getQybs())
                .eqIfPresent(KwxxDO::getBz, reqVO.getBz())
                .betweenIfPresent(KwxxDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(KwxxDO::getId));
    }

}
