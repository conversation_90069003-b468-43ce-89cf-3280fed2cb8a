package com.neway.assets.module.info.dal.mysql.hy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.info.controller.admin.hy.vo.HyExportReqVO;
import com.neway.assets.module.info.controller.admin.hy.vo.HyPageReqVO;
import com.neway.assets.module.info.dal.dataobject.hy.HyDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 行业 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HyMapper extends BaseMapperX<HyDO> {

    default PageResult<HyDO> selectPage(HyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HyDO>()
                .eqIfPresent(HyDO::getHyms, reqVO.getHyms())
                .eqIfPresent(HyDO::getQybs, reqVO.getQybs())
                .eqIfPresent(HyDO::getBz, reqVO.getBz())
                .betweenIfPresent(HyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HyDO::getId));
    }

    default List<HyDO> selectList(HyExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HyDO>()
                .eqIfPresent(HyDO::getHyms, reqVO.getHyms())
                .eqIfPresent(HyDO::getQybs, reqVO.getQybs())
                .eqIfPresent(HyDO::getBz, reqVO.getBz())
                .betweenIfPresent(HyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(HyDO::getId));
    }

}
