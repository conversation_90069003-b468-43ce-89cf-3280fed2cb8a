package com.neway.assets.module.info.dal.dataobject.jldw;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 计量单位 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_jldw", schema = "gdzc")
@KeySequence("bs_jldw_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JldwDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 计量单位简称
     */
    private String jldwjc;
    /**
     * 计量单位名称
     */
    private String jldwmc;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
