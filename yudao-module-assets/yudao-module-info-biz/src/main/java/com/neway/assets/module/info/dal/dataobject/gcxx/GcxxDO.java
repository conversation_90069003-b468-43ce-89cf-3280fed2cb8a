package com.neway.assets.module.info.dal.dataobject.gcxx;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.neway.assets.module.info.enums.enable.QybsStatusEnum;
import lombok.*;

/**
 * 工厂信息 DO
 *
 * <AUTHOR>
 */
@TableName(value="bs_gcxx", schema = "gdzc")
@KeySequence("bs_gcxx_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GcxxDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 隶属公司
     */
    private Long gs;
    /**
     * 工厂描述
     */
    private String gcms;
    /**
     * 工厂地址信息
     */
    private String dzxx;
    /**
     * 工厂地址经纬度
     */
    private String jwdxx;
    /**
     * 工厂照片链接
     */
    private String gczp;
    /**
     * 启用标识
     *
     * 枚举 {@link QybsStatusEnum }
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
