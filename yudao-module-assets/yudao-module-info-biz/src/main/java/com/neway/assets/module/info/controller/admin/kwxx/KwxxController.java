package com.neway.assets.module.info.controller.admin.kwxx;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.kwxx.vo.*;
import com.neway.assets.module.info.convert.kwxx.KwxxConvert;
import com.neway.assets.module.info.dal.dataobject.kwxx.KwxxDO;
import com.neway.assets.module.info.service.kwxx.KwxxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 库位信息")
@RestController
@RequestMapping("/info/kwxx")
@Validated
public class KwxxController {

    @Resource
    private KwxxService kwxxService;

    @PostMapping("/create")
    @Operation(summary = "创建库位信息")
    @PreAuthorize("@ss.hasPermission('info:kwxx:create')")
    public CommonResult<Long> createKwxx(@Valid @RequestBody KwxxCreateReqVO createReqVO) {
        return success(kwxxService.createKwxx(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库位信息")
    @PreAuthorize("@ss.hasPermission('info:kwxx:update')")
    public CommonResult<Boolean> updateKwxx(@Valid @RequestBody KwxxUpdateReqVO updateReqVO) {
        kwxxService.updateKwxx(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库位信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:kwxx:delete')")
    public CommonResult<Boolean> deleteKwxx(@RequestParam("id") Long id) {
        kwxxService.deleteKwxx(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库位信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:kwxx:query')")
    public CommonResult<KwxxRespVO> getKwxx(@RequestParam("id") Long id) {
        KwxxDO kwxx = kwxxService.getKwxx(id);
        return success(KwxxConvert.INSTANCE.convert(kwxx));
    }

    @GetMapping("/list")
    @Operation(summary = "获得库位信息列表")
    @PreAuthorize("@ss.hasPermission('info:kwxx:query')")
    public CommonResult<List<KwxxRespVO>> getKwxxList(@Valid KwxxExportReqVO reqVO) {
        List<KwxxDO> list = kwxxService.getKwxxList(reqVO);
        return success(KwxxConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库位信息分页")
    @PreAuthorize("@ss.hasPermission('info:kwxx:query')")
    public CommonResult<PageResult<KwxxRespVO>> getKwxxPage(@Valid KwxxPageReqVO pageVO) {
        PageResult<KwxxDO> pageResult = kwxxService.getKwxxPage(pageVO);
        return success(KwxxConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库位信息 Excel")
    @PreAuthorize("@ss.hasPermission('info:kwxx:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportKwxxExcel(@Valid KwxxExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<KwxxDO> list = kwxxService.getKwxxList(exportReqVO);
        // 导出 Excel
        List<KwxxExcelVO> datas = KwxxConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "库位信息.xls", "数据", KwxxExcelVO.class, datas);
    }

}
