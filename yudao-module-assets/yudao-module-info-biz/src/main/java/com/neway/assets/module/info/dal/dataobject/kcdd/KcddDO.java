package com.neway.assets.module.info.dal.dataobject.kcdd;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 库存地点 DO
 *
 * <AUTHOR>
 */
@TableName(value="bs_kcdd", schema = "gdzc")
@KeySequence("bs_kcdd_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KcddDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 隶属公司
     */
    private Long gs;
    /**
     * 隶属工厂
     */
    private Long gc;
    /**
     * 库存地点描述
     */
    private String kcddms;
    /**
     * 库存地点编号
     */
    private String kcddbh;
    /**
     * 启用标识
     *
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
