package com.neway.assets.module.info.api.secondzcfl;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.secondzcfl.dto.SecondZcflRespDTO;
import com.neway.assets.module.info.convert.secondzcfl.SecondZcflConvert;
import com.neway.assets.module.info.dal.dataobject.secondzcfl.SecondZcflDO;
import com.neway.assets.module.info.service.secondzcfl.SecondZcflService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 资产二级分类API接口实现类
 * <AUTHOR>
 * @since 2023/6/6 9:42
 **/
@Service
@RequiredArgsConstructor
public class SecondZcflApiImpl implements SecondZcflApi{

    private final SecondZcflService secondZcflService;

    @Override
    public List<SecondZcflRespDTO> getSecondZcflList(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<SecondZcflDO> doList = secondZcflService.getSecondZcflList(ids);
        return SecondZcflConvert.INSTANCE.convertList03(doList);
    }
}
