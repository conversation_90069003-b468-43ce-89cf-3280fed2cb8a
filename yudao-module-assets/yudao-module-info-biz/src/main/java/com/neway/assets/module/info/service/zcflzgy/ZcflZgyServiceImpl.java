package com.neway.assets.module.info.service.zcflzgy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyCreateReqVO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyExportReqVO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyPageReqVO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyUpdateReqVO;
import com.neway.assets.module.info.convert.zcflzgy.ZcflZgyConvert;
import com.neway.assets.module.info.dal.dataobject.zcflzgy.ZcflZgyDO;
import com.neway.assets.module.info.dal.mysql.zcflzgy.ZcflZgyMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZCFL_ZGY_DUPLICATE;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZCFL_ZGY_NOT_EXISTS;

/**
 * 资产分类资管员关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ZcflZgyServiceImpl implements ZcflZgyService {

    @Resource
    private ZcflZgyMapper zcflZgyMapper;

    @Override
    public Long createZcflZgy(ZcflZgyCreateReqVO createReqVO) {
        validateZcflZgyDuplicate(createReqVO.getGs(), createReqVO.getZcflId(), null);
        // 插入
        ZcflZgyDO zcflZgy = ZcflZgyConvert.INSTANCE.convert(createReqVO);
        zcflZgyMapper.insert(zcflZgy);
        // 返回
        return zcflZgy.getId();
    }

    @Override
    public void updateZcflZgy(ZcflZgyUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcflZgyExists(updateReqVO.getId());
        // 校验重复性
        validateZcflZgyDuplicate(updateReqVO.getGs(), updateReqVO.getZcflId(), updateReqVO.getId());
        // 更新
        ZcflZgyDO updateObj = ZcflZgyConvert.INSTANCE.convert(updateReqVO);
        zcflZgyMapper.updateById(updateObj);
    }

    @Override
    public void deleteZcflZgy(Long id) {
        // 校验存在
        validateZcflZgyExists(id);
        // 删除
        zcflZgyMapper.deleteById(id);
    }

    private void validateZcflZgyExists(Long id) {
        if (zcflZgyMapper.selectById(id) == null) {
            throw exception(ZCFL_ZGY_NOT_EXISTS);
        }
    }

    private void validateZcflZgyDuplicate(Long companyId, Long typeId, Long id) {
        ZcflZgyDO entity = zcflZgyMapper.selectOne(ZcflZgyDO::getGs, companyId, ZcflZgyDO::getZcflId, typeId);
        if (entity != null && !Objects.equals(entity.getId(), id))
            throw exception(ZCFL_ZGY_DUPLICATE);
    }

    @Override
    public ZcflZgyDO getZcflZgy(Long id) {
        return zcflZgyMapper.selectById(id);
    }

    @Override
    public List<ZcflZgyDO> getZcflZgyList(Collection<Long> ids) {
        return zcflZgyMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcflZgyDO> getZcflZgyPage(ZcflZgyPageReqVO pageReqVO) {
        return zcflZgyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ZcflZgyDO> getZcflZgyList(ZcflZgyExportReqVO exportReqVO) {
        return zcflZgyMapper.selectList(exportReqVO);
    }

}
