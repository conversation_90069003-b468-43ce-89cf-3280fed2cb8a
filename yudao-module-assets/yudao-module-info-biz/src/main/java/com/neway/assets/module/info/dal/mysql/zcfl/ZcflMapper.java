package com.neway.assets.module.info.dal.mysql.zcfl;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.zcfl.ZcflDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.zcfl.vo.*;

/**
 * 资产分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcflMapper extends BaseMapperX<ZcflDO> {

    default PageResult<ZcflDO> selectPage(ZcflPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcflDO>()
                .eqIfPresent(ZcflDO::getZcflfs, reqVO.getZcflfs())
                .eqIfPresent(ZcflDO::getZcflms, reqVO.getZcflms())
                .eqIfPresent(ZcflDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcflDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcflDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcflDO::getId));
    }

    default List<ZcflDO> selectList(ZcflExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcflDO>()
                .eqIfPresent(ZcflDO::getZcflfs, reqVO.getZcflfs())
                .eqIfPresent(ZcflDO::getZcflms, reqVO.getZcflms())
                .eqIfPresent(ZcflDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZcflDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZcflDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcflDO::getId));
    }

}
