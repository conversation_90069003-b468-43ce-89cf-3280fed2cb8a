package com.neway.assets.module.info.api.zcfl;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.zcfl.dto.ZcflRespDTO;
import com.neway.assets.module.info.convert.zcfl.ZcflConvert;
import com.neway.assets.module.info.dal.dataobject.zcfl.ZcflDO;
import com.neway.assets.module.info.service.zcfl.ZcflService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 资产分类API 实现类
 * <AUTHOR>
 * @since 2023/5/8 8:30
 **/
@Service
public class ZcflApiImpl implements ZcflApi{

    @Resource
    private ZcflService zcflService;

    @Override
    public List<ZcflRespDTO> getZcflList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<ZcflDO> zcflList = zcflService.getZcflList(ids);
        return ZcflConvert.INSTANCE.convertList03(zcflList);
    }
}
