package com.neway.assets.module.info.service.wxyy;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.wxyy.vo.*;
import com.neway.assets.module.info.dal.dataobject.wxyy.WxyyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 维护/维修原因 Service 接口
 *
 * <AUTHOR>
 */
public interface WxyyService {

    /**
     * 创建维护/维修原因
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWxyy(@Valid WxyyCreateReqVO createReqVO);

    /**
     * 更新维护/维修原因
     *
     * @param updateReqVO 更新信息
     */
    void updateWxyy(@Valid WxyyUpdateReqVO updateReqVO);

    /**
     * 删除维护/维修原因
     *
     * @param id 编号
     */
    void deleteWxyy(Long id);

    /**
     * 获得维护/维修原因
     *
     * @param id 编号
     * @return 维护/维修原因
     */
    WxyyDO getWxyy(Long id);

    /**
     * 获得维护/维修原因列表
     *
     * @param ids 编号
     * @return 维护/维修原因列表
     */
    List<WxyyDO> getWxyyList(Collection<Long> ids);

    /**
     * 获得维护/维修原因分页
     *
     * @param pageReqVO 分页查询
     * @return 维护/维修原因分页
     */
    PageResult<WxyyDO> getWxyyPage(WxyyPageReqVO pageReqVO);

    /**
     * 获得维护/维修原因列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 维护/维修原因列表
     */
    List<WxyyDO> getWxyyList(WxyyExportReqVO exportReqVO);

}
