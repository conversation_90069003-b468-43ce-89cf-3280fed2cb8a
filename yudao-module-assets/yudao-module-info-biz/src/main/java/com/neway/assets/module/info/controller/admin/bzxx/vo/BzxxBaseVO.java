package com.neway.assets.module.info.controller.admin.bzxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 币种信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BzxxBaseVO {

    @Schema(description = "币种简称", required = true)
    @NotNull(message = "币种简称不能为空")
    private String bzjc;

    @Schema(description = "币种名称", required = true)
    @NotNull(message = "币种名称不能为空")
    private String bzmc;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
