package com.neway.assets.module.info.service.hjxx;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.info.controller.admin.hjxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.hjxx.HjxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.info.convert.hjxx.HjxxConvert;
import com.neway.assets.module.info.dal.mysql.hjxx.HjxxMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;

/**
 * 货架 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HjxxServiceImpl implements HjxxService {

    @Resource
    private HjxxMapper hjxxMapper;

    @Override
    public Long createHjxx(HjxxCreateReqVO createReqVO) {
        // 插入
        HjxxDO hjxx = HjxxConvert.INSTANCE.convert(createReqVO);
        hjxxMapper.insert(hjxx);
        // 返回
        return hjxx.getId();
    }

    @Override
    public void updateHjxx(HjxxUpdateReqVO updateReqVO) {
        // 校验存在
        validateHjxxExists(updateReqVO.getId());
        // 更新
        HjxxDO updateObj = HjxxConvert.INSTANCE.convert(updateReqVO);
        hjxxMapper.updateById(updateObj);
    }

    @Override
    public void deleteHjxx(Long id) {
        // 校验存在
        validateHjxxExists(id);
        // 删除
        hjxxMapper.deleteById(id);
    }

    private void validateHjxxExists(Long id) {
        if (hjxxMapper.selectById(id) == null) {
            throw exception(HJXX_NOT_EXISTS);
        }
    }

    @Override
    public HjxxDO getHjxx(Long id) {
        return hjxxMapper.selectById(id);
    }

    @Override
    public List<HjxxDO> getHjxxList(Collection<Long> ids) {
        return hjxxMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<HjxxDO> getHjxxPage(HjxxPageReqVO pageReqVO) {
        return hjxxMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HjxxDO> getHjxxList(HjxxExportReqVO exportReqVO) {
        return hjxxMapper.selectList(exportReqVO);
    }

}
