package com.neway.assets.module.info.controller.admin.zcflfs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 资产分类方式 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcflfsBaseVO {

    @Schema(description = "分类方式说明", required = true)
    @NotNull(message = "分类方式说明不能为空")
    private String flfssm;

    @Schema(description = "启用标识（1:启用 ；0:未启用）")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
