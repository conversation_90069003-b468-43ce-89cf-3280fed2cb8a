package com.neway.assets.module.info.controller.admin.jldwdz;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.*;
import com.neway.assets.module.info.convert.jldwdz.JldwdzConvert;
import com.neway.assets.module.info.dal.dataobject.jldwdz.JldwdzDO;
import com.neway.assets.module.info.service.jldwdz.JldwdzService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 计量单位转换对照")
@RestController
@RequestMapping("/info/jldwdz")
@Validated
public class JldwdzController {

    @Resource
    private JldwdzService jldwdzService;

    @PostMapping("/create")
    @Operation(summary = "创建计量单位转换对照")
    @PreAuthorize("@ss.hasPermission('info:jldwdz:create')")
    public CommonResult<Long> createJldwdz(@Valid @RequestBody JldwdzCreateReqVO createReqVO) {
        return success(jldwdzService.createJldwdz(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新计量单位转换对照")
    @PreAuthorize("@ss.hasPermission('info:jldwdz:update')")
    public CommonResult<Boolean> updateJldwdz(@Valid @RequestBody JldwdzUpdateReqVO updateReqVO) {
        jldwdzService.updateJldwdz(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除计量单位转换对照")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:jldwdz:delete')")
    public CommonResult<Boolean> deleteJldwdz(@RequestParam("id") Long id) {
        jldwdzService.deleteJldwdz(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得计量单位转换对照")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:jldwdz:query')")
    public CommonResult<JldwdzRespVO> getJldwdz(@RequestParam("id") Long id) {
        JldwdzDO jldwdz = jldwdzService.getJldwdz(id);
        return success(JldwdzConvert.INSTANCE.convert(jldwdz));
    }

    @GetMapping("/list")
    @Operation(summary = "获得计量单位转换对照列表")
    @PreAuthorize("@ss.hasPermission('info:jldwdz:query')")
    public CommonResult<List<JldwdzRespVO>> getJldwdzList(@Valid JldwdzExportReqVO reqVO) {
        List<JldwdzDO> list = jldwdzService.getJldwdzList(reqVO);
        return success(JldwdzConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得计量单位转换对照分页")
    @PreAuthorize("@ss.hasPermission('info:jldwdz:query')")
    public CommonResult<PageResult<JldwdzRespVO>> getJldwdzPage(@Valid JldwdzPageReqVO pageVO) {
        PageResult<JldwdzDO> pageResult = jldwdzService.getJldwdzPage(pageVO);
        return success(JldwdzConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出计量单位转换对照 Excel")
    @PreAuthorize("@ss.hasPermission('info:jldwdz:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportJldwdzExcel(@Valid JldwdzExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<JldwdzDO> list = jldwdzService.getJldwdzList(exportReqVO);
        // 导出 Excel
        List<JldwdzExcelVO> datas = JldwdzConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "计量单位转换对照.xls", "数据", JldwdzExcelVO.class, datas);
    }

}
