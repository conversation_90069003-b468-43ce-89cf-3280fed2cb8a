package com.neway.assets.module.info.controller.admin.secondzcfl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 资产二级分类 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SecondZcflBaseVO {

    @Schema(description = "资产一级分类", required = true, example = "19295")
    @NotNull(message = "资产一级分类不能为空")
    private Long zcflfsId;

    @Schema(description = "二级分类描述", required = true)
    @NotNull(message = "二级分类描述不能为空")
    private String zcflms;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
