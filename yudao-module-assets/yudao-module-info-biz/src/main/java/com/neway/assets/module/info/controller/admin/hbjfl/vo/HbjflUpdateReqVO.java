package com.neway.assets.module.info.controller.admin.hbjfl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 耗材/备品备件分类更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HbjflUpdateReqVO extends HbjflBaseVO {

    @Schema(description = "自增长id", required = true, example = "9743")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
