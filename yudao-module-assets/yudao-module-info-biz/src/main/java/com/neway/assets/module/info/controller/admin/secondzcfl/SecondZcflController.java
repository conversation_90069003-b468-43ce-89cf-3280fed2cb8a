package com.neway.assets.module.info.controller.admin.secondzcfl;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.*;
import com.neway.assets.module.info.convert.secondzcfl.SecondZcflConvert;
import com.neway.assets.module.info.dal.dataobject.secondzcfl.SecondZcflDO;
import com.neway.assets.module.info.enums.enable.QybsStatusEnum;
import com.neway.assets.module.info.service.secondzcfl.SecondZcflService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产二级分类")
@RestController
@RequestMapping("/info/second-zcfl")
@Validated
public class SecondZcflController {

    @Resource
    private SecondZcflService secondZcflService;

    @PostMapping("/create")
    @Operation(summary = "创建资产二级分类")
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:create')")
    public CommonResult<Long> createSecondZcfl(@Valid @RequestBody SecondZcflCreateReqVO createReqVO) {
        return success(secondZcflService.createSecondZcfl(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产二级分类")
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:update')")
    public CommonResult<Boolean> updateSecondZcfl(@Valid @RequestBody SecondZcflUpdateReqVO updateReqVO) {
        secondZcflService.updateSecondZcfl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产二级分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:delete')")
    public CommonResult<Boolean> deleteSecondZcfl(@RequestParam("id") Long id) {
        secondZcflService.deleteSecondZcfl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产二级分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:query')")
    public CommonResult<SecondZcflRespVO> getSecondZcfl(@RequestParam("id") Long id) {
        SecondZcflDO secondZcfl = secondZcflService.getSecondZcfl(id);
        return success(SecondZcflConvert.INSTANCE.convert(secondZcfl));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产二级分类列表")
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:query')")
    public CommonResult<List<SecondZcflRespVO>> getSecondZcflList(@Valid SecondZcflExportReqVO reqVO) {
        List<SecondZcflDO> list = secondZcflService.getSecondZcflList(reqVO)
                .stream().filter(o -> Objects.equals(QybsStatusEnum.ENABLED.getVal(), o.getQybs()))
                .collect(Collectors.toList());
        return success(SecondZcflConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产二级分类分页")
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:query')")
    public CommonResult<PageResult<SecondZcflRespVO>> getSecondZcflPage(@Valid SecondZcflPageReqVO pageVO) {
        PageResult<SecondZcflDO> pageResult = secondZcflService.getSecondZcflPage(pageVO);
        return success(SecondZcflConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产二级分类 Excel")
    @PreAuthorize("@ss.hasPermission('info:second-zcfl:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSecondZcflExcel(@Valid SecondZcflExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SecondZcflDO> list = secondZcflService.getSecondZcflList(exportReqVO);
        // 导出 Excel
        List<SecondZcflExcelVO> datas = SecondZcflConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产二级分类.xls", "数据", SecondZcflExcelVO.class, datas);
    }

}
