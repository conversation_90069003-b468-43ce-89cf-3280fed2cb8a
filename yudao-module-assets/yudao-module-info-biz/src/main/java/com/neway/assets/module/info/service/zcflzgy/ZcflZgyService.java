package com.neway.assets.module.info.service.zcflzgy;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.*;
import com.neway.assets.module.info.dal.dataobject.zcflzgy.ZcflZgyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产分类资管员关联 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcflZgyService {

    /**
     * 创建资产分类资管员关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcflZgy(@Valid ZcflZgyCreateReqVO createReqVO);

    /**
     * 更新资产分类资管员关联
     *
     * @param updateReqVO 更新信息
     */
    void updateZcflZgy(@Valid ZcflZgyUpdateReqVO updateReqVO);

    /**
     * 删除资产分类资管员关联
     *
     * @param id 编号
     */
    void deleteZcflZgy(Long id);

    /**
     * 获得资产分类资管员关联
     *
     * @param id 编号
     * @return 资产分类资管员关联
     */
    ZcflZgyDO getZcflZgy(Long id);

    /**
     * 获得资产分类资管员关联列表
     *
     * @param ids 编号
     * @return 资产分类资管员关联列表
     */
    List<ZcflZgyDO> getZcflZgyList(Collection<Long> ids);

    /**
     * 获得资产分类资管员关联分页
     *
     * @param pageReqVO 分页查询
     * @return 资产分类资管员关联分页
     */
    PageResult<ZcflZgyDO> getZcflZgyPage(ZcflZgyPageReqVO pageReqVO);

    /**
     * 获得资产分类资管员关联列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产分类资管员关联列表
     */
    List<ZcflZgyDO> getZcflZgyList(ZcflZgyExportReqVO exportReqVO);

}
