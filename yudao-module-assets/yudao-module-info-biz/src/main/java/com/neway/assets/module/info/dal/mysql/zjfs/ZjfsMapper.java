package com.neway.assets.module.info.dal.mysql.zjfs;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.info.dal.dataobject.zjfs.ZjfsDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.info.controller.admin.zjfs.vo.*;

/**
 * 资产折旧方式 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZjfsMapper extends BaseMapperX<ZjfsDO> {

    default PageResult<ZjfsDO> selectPage(ZjfsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZjfsDO>()
                .eqIfPresent(ZjfsDO::getZcfl, reqVO.getZcfl())
                .eqIfPresent(ZjfsDO::getZjnx, reqVO.getZjnx())
                .eqIfPresent(ZjfsDO::getCzl, reqVO.getCzl())
                .eqIfPresent(ZjfsDO::getSfdyzj, reqVO.getSfdyzj())
                .eqIfPresent(ZjfsDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZjfsDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZjfsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZjfsDO::getId));
    }

    default List<ZjfsDO> selectList(ZjfsExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZjfsDO>()
                .eqIfPresent(ZjfsDO::getZcfl, reqVO.getZcfl())
                .eqIfPresent(ZjfsDO::getZjnx, reqVO.getZjnx())
                .eqIfPresent(ZjfsDO::getCzl, reqVO.getCzl())
                .eqIfPresent(ZjfsDO::getSfdyzj, reqVO.getSfdyzj())
                .eqIfPresent(ZjfsDO::getQybs, reqVO.getQybs())
                .eqIfPresent(ZjfsDO::getBz, reqVO.getBz())
                .betweenIfPresent(ZjfsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZjfsDO::getId));
    }

}
