package com.neway.assets.module.info.controller.admin.kwxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 库位信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class KwxxBaseVO {

    @Schema(description = "库存地点", required = true)
    @NotNull(message = "库存地点不能为空")
    private Long kcdd;

    @Schema(description = "库位编码", required = true)
    @NotNull(message = "库位编码不能为空")
    private String kwbh;

    @Schema(description = "库位描述", required = true)
    @NotNull(message = "库位描述不能为空")
    private String kwms;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
