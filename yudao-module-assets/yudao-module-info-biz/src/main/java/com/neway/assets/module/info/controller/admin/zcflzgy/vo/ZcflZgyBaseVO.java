package com.neway.assets.module.info.controller.admin.zcflzgy.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 资产分类资管员关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcflZgyBaseVO {

    @Schema(description = "公司", required = true)
    @NotNull(message = "公司不能为空")
    private Long gs;

    @Schema(description = "一级分类id", required = true, example = "11015")
    @NotNull(message = "一级分类id不能为空")
    private Long zcflId;

    @Schema(description = "用户id", required = true, example = "1947")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
