package com.neway.assets.module.info.controller.admin.kcdd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存地点 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class KcddRespVO extends KcddBaseVO {

    @Schema(description = "自增长id", example = "14644")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
