package com.neway.assets.module.info.controller.admin.zjfs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产折旧方式 Excel 导出 Request VO，参数和 ZjfsPageReqVO 是一致的")
@Data
public class ZjfsExportReqVO {

    @Schema(description = "资产分类")
    private Long zcfl;

    @Schema(description = "折旧年限")
    private BigDecimal zjnx;

    @Schema(description = "残值率")
    private BigDecimal czl;

    @Schema(description = "是否当月折旧")
    private String sfdyzj;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
