package com.neway.assets.module.info.service.kwxx;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.info.controller.admin.kwxx.vo.*;
import com.neway.assets.module.info.dal.dataobject.kwxx.KwxxDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 库位信息 Service 接口
 *
 * <AUTHOR>
 */
public interface KwxxService {

    /**
     * 创建库位信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createKwxx(@Valid KwxxCreateReqVO createReqVO);

    /**
     * 更新库位信息
     *
     * @param updateReqVO 更新信息
     */
    void updateKwxx(@Valid KwxxUpdateReqVO updateReqVO);

    /**
     * 删除库位信息
     *
     * @param id 编号
     */
    void deleteKwxx(Long id);

    /**
     * 获得库位信息
     *
     * @param id 编号
     * @return 库位信息
     */
    KwxxDO getKwxx(Long id);

    /**
     * 获得库位信息列表
     *
     * @param ids 编号
     * @return 库位信息列表
     */
    List<KwxxDO> getKwxxList(Collection<Long> ids);

    /**
     * 获得库位信息分页
     *
     * @param pageReqVO 分页查询
     * @return 库位信息分页
     */
    PageResult<KwxxDO> getKwxxPage(KwxxPageReqVO pageReqVO);

    /**
     * 获得库位信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 库位信息列表
     */
    List<KwxxDO> getKwxxList(KwxxExportReqVO exportReqVO);

}
