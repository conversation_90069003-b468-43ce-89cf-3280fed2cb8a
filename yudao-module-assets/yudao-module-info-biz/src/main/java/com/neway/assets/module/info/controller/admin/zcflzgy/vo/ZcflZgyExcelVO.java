package com.neway.assets.module.info.controller.admin.zcflzgy.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 资产分类资管员关联 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcflZgyExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("公司")
    private Long gs;

    @ExcelProperty("一级分类id")
    private Long zcflId;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty(value = "启用标识", converter = DictConvert.class)
    @DictFormat("assets_qybs_status")
    private Integer qybs;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
