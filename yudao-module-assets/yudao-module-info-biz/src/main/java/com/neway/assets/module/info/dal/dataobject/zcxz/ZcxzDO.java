package com.neway.assets.module.info.dal.dataobject.zcxz;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 资产性质 DO
 *
 * <AUTHOR>
 */
@TableName(value = "bs_zcxz", schema = "gdzc")
@KeySequence("gdzc.bs_zcxz_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcxzDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Integer id;
    /**
     * 资产性质描述
     */
    private String zcxzms;
    /**
     * 启用标识（1:启用 ；0:未启用）
     *
     * 枚举
     */
    private Integer qybs;
    /**
     * 备注
     */
    private String bz;

}
