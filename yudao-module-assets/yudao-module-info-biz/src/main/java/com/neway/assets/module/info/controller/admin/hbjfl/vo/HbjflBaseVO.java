package com.neway.assets.module.info.controller.admin.hbjfl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 耗材/备品备件分类 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class HbjflBaseVO {

    @Schema(description = "分类名称", required = true)
    @NotNull(message = "分类名称不能为空")
    private String bflmc;

    @Schema(description = "库存成本计算方式（0：移动平均，1：批次价格）", required = true)
    @NotNull(message = "库存成本计算方式不能为空")
    private Integer cbjsfs;

    @Schema(description = "启用标识")
    private Integer qybs;

    @Schema(description = "备注")
    private String bz;

}
