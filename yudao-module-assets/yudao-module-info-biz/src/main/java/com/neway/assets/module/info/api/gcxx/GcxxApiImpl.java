package com.neway.assets.module.info.api.gcxx;

import cn.hutool.core.collection.CollUtil;
import com.neway.assets.module.info.api.gcxx.dto.GcxxRespDTO;
import com.neway.assets.module.info.convert.gcxx.GcxxConvert;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import com.neway.assets.module.info.service.gcxx.GcxxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8 8:59
 **/
@Service
public class GcxxApiImpl implements GcxxApi{
    @Resource
    private GcxxService gcxxService;

    @Override
    public List<GcxxRespDTO> getGcxxList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        List<GcxxDO> gcxxList = gcxxService.getGcxxList(ids);
        return GcxxConvert.INSTANCE.convertList03(gcxxList);
    }
}
