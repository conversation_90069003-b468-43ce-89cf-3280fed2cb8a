package com.neway.assets.module.info.controller.admin.gcxx;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.neway.assets.module.info.controller.admin.gcxx.vo.*;
import com.neway.assets.module.info.convert.gcxx.GcxxConvert;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import com.neway.assets.module.info.service.gcxx.GcxxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;

@Tag(name = "管理后台 - 工厂信息")
@RestController
@RequestMapping("/info/gcxx")
@Validated
public class GcxxController {

    @Resource
    private GcxxService gcxxService;

    @Resource
    private DeptApi deptApi;

    @PostMapping("/create")
    @Operation(summary = "创建工厂信息")
    @PreAuthorize("@ss.hasPermission('info:gcxx:create')")
    public CommonResult<Long> createGcxx(@Valid @RequestBody GcxxCreateReqVO createReqVO) {
        return success(gcxxService.createGcxx(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新工厂信息")
    @PreAuthorize("@ss.hasPermission('info:gcxx:update')")
    public CommonResult<Boolean> updateGcxx(@Valid @RequestBody GcxxUpdateReqVO updateReqVO) {
        gcxxService.updateGcxx(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除工厂信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('info:gcxx:delete')")
    public CommonResult<Boolean> deleteGcxx(@RequestParam("id") Long id) {
        gcxxService.deleteGcxx(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得工厂信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('info:gcxx:query')")
    public CommonResult<GcxxRespVO> getGcxx(@RequestParam("id") Long id) {
        GcxxDO gcxx = gcxxService.getGcxx(id);
        return success(GcxxConvert.INSTANCE.convert(gcxx));
    }

    @GetMapping("/list")
    @Operation(summary = "获得工厂信息列表")
    @PreAuthorize("@ss.hasPermission('info:gcxx:query')")
    public CommonResult<List<GcxxRespVO>> getGcxxList(@Valid GcxxExportReqVO reqVO) {
        List<GcxxDO> list = gcxxService.getGcxxList(reqVO);
        return success(GcxxConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得工厂信息分页")
    @PreAuthorize("@ss.hasPermission('info:gcxx:query')")
    public CommonResult<PageResult<GcxxPageItemRespVO>> getGcxxPage(@Valid GcxxPageReqVO pageVO) {
        PageResult<GcxxDO> pageResult = gcxxService.getGcxxPage(pageVO);
        if (CollUtil.isEmpty(pageResult.getList())) return success(new PageResult<>(pageResult.getTotal()));
        // 获得拼接部门信息
        Set<Long> deptIds = convertSet(pageResult.getList(), GcxxDO::getGs);
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(deptIds);
        // 拼接结果返回
        List<GcxxPageItemRespVO> gcxxList = new ArrayList<>(pageResult.getList().size());
        pageResult.getList().forEach(gcxxDO -> {
            GcxxPageItemRespVO respVO = GcxxConvert.INSTANCE.convert2(gcxxDO);
            respVO.setCompany(GcxxConvert.INSTANCE.convert(deptMap.get(gcxxDO.getGs())));
            gcxxList.add(respVO);
        });
        return success(new PageResult<>(gcxxList, pageResult.getTotal()));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出工厂信息 Excel")
    @PreAuthorize("@ss.hasPermission('info:gcxx:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGcxxExcel(@Valid GcxxExportReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<GcxxDO> list = gcxxService.getGcxxList(exportReqVO);
        // 导出 Excel
        List<GcxxExcelVO> datas = GcxxConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "工厂信息.xls", "数据", GcxxExcelVO.class, datas);
    }

}
