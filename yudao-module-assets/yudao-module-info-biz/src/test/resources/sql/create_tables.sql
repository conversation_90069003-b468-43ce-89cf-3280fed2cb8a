
CREATE TABLE IF NOT EXISTS "bs_zcxz" (
    "id" int NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zcxzms" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产性质';

CREATE TABLE IF NOT EXISTS "bs_zcflfs" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "flfssm" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产分类方式';

CREATE TABLE IF NOT EXISTS "bs_zcfl" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zcflfs" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zcflms" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产分类表';

CREATE TABLE IF NOT EXISTS "bs_gcxx" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "gs" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "gcms" varchar NOT NULL,
    "dzxx" varchar,
    "jwdxx" varchar,
    "gczp" varchar,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '工厂信息表';

CREATE TABLE IF NOT EXISTS "bs_kcdd" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "gs" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "gc" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "kcddms" varchar NOT NULL,
    "kcddbh" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '库存地点';

CREATE TABLE IF NOT EXISTS "bs_kwxx" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "kcdd" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "kwbh" varchar NOT NULL,
    "kwms" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '库位信息表';

CREATE TABLE IF NOT EXISTS "bs_hjxx" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "kw" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "hjbh" varchar NOT NULL,
    "hjms" varchar NOT NULL,
    "hang" int,
    "lie" int,
    "ceng" int,
    "dany" int,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '货架信息表';

CREATE TABLE IF NOT EXISTS "bs_jldw" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "jldwjc" varchar NOT NULL,
    "jldwmc" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '计量单位';

CREATE TABLE IF NOT EXISTS "bs_jldwdz" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "cbzx" varchar,
    "yjldw" bigint NOT NULL,
    "yuansl" varchar NOT NULL,
    "zhdw" bigint NOT NULL,
    "zhsl" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '计量单位转换对照表';

CREATE TABLE IF NOT EXISTS "bs_wxyy" (
     "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
     "wxyyms" varchar NOT NULL,
     "qybs" int,
     "bz" varchar,
     "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
     "creator" varchar DEFAULT '',
     "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     "updater" varchar DEFAULT '',
     "deleted" bit NOT NULL DEFAULT FALSE,
     PRIMARY KEY ("id")
) COMMENT '维护/维修原因';

CREATE TABLE IF NOT EXISTS "bs_zjfs" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zcfl" bigint NOT NULL,
    "zjnx" varchar,
    "czl" varchar,
    "sfdyzj" varchar,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产折旧方式';

CREATE TABLE IF NOT EXISTS "bs_hy" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "hyms" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '行业';

CREATE TABLE IF NOT EXISTS "bs_hbjfl" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "bflmc" varchar NOT NULL,
    "cbjsfs" int NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '耗材/备品备件分类';

CREATE TABLE IF NOT EXISTS "bs_bzxx" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "bzjc" varchar NOT NULL,
    "bzmc" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '币种信息表';

CREATE TABLE IF NOT EXISTS "bs_second_zcfl" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zcflfs_id" bigint NOT NULL,
    "zcflms" varchar NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
    ) COMMENT '资产二级分类';

CREATE TABLE IF NOT EXISTS "bs_zcfl_zgy" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "gs" bigint NOT NULL,
    "zcfl_id" bigint NOT NULL,
    "user_id" bigint NOT NULL,
    "qybs" int,
    "bz" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产分类资管员关联表';
