package com.neway.assets.module.info.service.hy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.hy.vo.HyCreateReqVO;
import com.neway.assets.module.info.controller.admin.hy.vo.HyExportReqVO;
import com.neway.assets.module.info.controller.admin.hy.vo.HyPageReqVO;
import com.neway.assets.module.info.controller.admin.hy.vo.HyUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.hy.HyDO;
import com.neway.assets.module.info.dal.mysql.hy.HyMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.HY_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link HyServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(HyServiceImpl.class)
public class HyServiceImplTest extends BaseDbUnitTest {

    @Resource
    private HyServiceImpl hyService;

    @Resource
    private HyMapper hyMapper;

    @Test
    public void testCreateHy_success() {
        // 准备参数
        HyCreateReqVO reqVO = randomPojo(HyCreateReqVO.class);

        // 调用
        Long hyId = hyService.createHy(reqVO);
        // 断言
        assertNotNull(hyId);
        // 校验记录的属性是否正确
        HyDO hy = hyMapper.selectById(hyId);
        assertPojoEquals(reqVO, hy);
    }

    @Test
    public void testUpdateHy_success() {
        // mock 数据
        HyDO dbHy = randomPojo(HyDO.class);
        hyMapper.insert(dbHy);// @Sql: 先插入出一条存在的数据
        // 准备参数
        HyUpdateReqVO reqVO = randomPojo(HyUpdateReqVO.class, o -> {
            o.setId(dbHy.getId()); // 设置更新的 ID
        });

        // 调用
        hyService.updateHy(reqVO);
        // 校验是否更新正确
        HyDO hy = hyMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, hy);
    }

    @Test
    public void testUpdateHy_notExists() {
        // 准备参数
        HyUpdateReqVO reqVO = randomPojo(HyUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> hyService.updateHy(reqVO), HY_NOT_EXISTS);
    }

    @Test
    public void testDeleteHy_success() {
        // mock 数据
        HyDO dbHy = randomPojo(HyDO.class);
        hyMapper.insert(dbHy);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbHy.getId();

        // 调用
        hyService.deleteHy(id);
       // 校验数据不存在了
       assertNull(hyMapper.selectById(id));
    }

    @Test
    public void testDeleteHy_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> hyService.deleteHy(id), HY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHyPage() {
       // mock 数据
       HyDO dbHy = randomPojo(HyDO.class, o -> { // 等会查询到
           o.setHyms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       hyMapper.insert(dbHy);
       // 测试 hyms 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setHyms(null)));
       // 测试 qybs 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setCreateTime(null)));
       // 准备参数
       HyPageReqVO reqVO = new HyPageReqVO();
       reqVO.setHyms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<HyDO> pageResult = hyService.getHyPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbHy, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHyList() {
       // mock 数据
       HyDO dbHy = randomPojo(HyDO.class, o -> { // 等会查询到
           o.setHyms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       hyMapper.insert(dbHy);
       // 测试 hyms 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setHyms(null)));
       // 测试 qybs 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       hyMapper.insert(cloneIgnoreId(dbHy, o -> o.setCreateTime(null)));
       // 准备参数
       HyExportReqVO reqVO = new HyExportReqVO();
       reqVO.setHyms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<HyDO> list = hyService.getHyList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbHy, list.get(0));
    }

}
