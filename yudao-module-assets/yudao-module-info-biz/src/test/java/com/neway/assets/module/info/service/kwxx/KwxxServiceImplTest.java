package com.neway.assets.module.info.service.kwxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.kwxx.vo.KwxxCreateReqVO;
import com.neway.assets.module.info.controller.admin.kwxx.vo.KwxxExportReqVO;
import com.neway.assets.module.info.controller.admin.kwxx.vo.KwxxPageReqVO;
import com.neway.assets.module.info.controller.admin.kwxx.vo.KwxxUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.kwxx.KwxxDO;
import com.neway.assets.module.info.dal.mysql.kwxx.KwxxMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.KWXX_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link KwxxServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(KwxxServiceImpl.class)
public class KwxxServiceImplTest extends BaseDbUnitTest {

    @Resource
    private KwxxServiceImpl kwxxService;

    @Resource
    private KwxxMapper kwxxMapper;

    @Test
    public void testCreateKwxx_success() {
        // 准备参数
        KwxxCreateReqVO reqVO = randomPojo(KwxxCreateReqVO.class);

        // 调用
        Long kwxxId = kwxxService.createKwxx(reqVO);
        // 断言
        assertNotNull(kwxxId);
        // 校验记录的属性是否正确
        KwxxDO kwxx = kwxxMapper.selectById(kwxxId);
        assertPojoEquals(reqVO, kwxx);
    }

    @Test
    public void testUpdateKwxx_success() {
        // mock 数据
        KwxxDO dbKwxx = randomPojo(KwxxDO.class);
        kwxxMapper.insert(dbKwxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        KwxxUpdateReqVO reqVO = randomPojo(KwxxUpdateReqVO.class, o -> {
            o.setId(dbKwxx.getId()); // 设置更新的 ID
        });

        // 调用
        kwxxService.updateKwxx(reqVO);
        // 校验是否更新正确
        KwxxDO kwxx = kwxxMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, kwxx);
    }

    @Test
    public void testUpdateKwxx_notExists() {
        // 准备参数
        KwxxUpdateReqVO reqVO = randomPojo(KwxxUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> kwxxService.updateKwxx(reqVO), KWXX_NOT_EXISTS);
    }

    @Test
    public void testDeleteKwxx_success() {
        // mock 数据
        KwxxDO dbKwxx = randomPojo(KwxxDO.class);
        kwxxMapper.insert(dbKwxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbKwxx.getId();

        // 调用
        kwxxService.deleteKwxx(id);
       // 校验数据不存在了
       assertNull(kwxxMapper.selectById(id));
    }

    @Test
    public void testDeleteKwxx_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> kwxxService.deleteKwxx(id), KWXX_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetKwxxPage() {
       // mock 数据
       KwxxDO dbKwxx = randomPojo(KwxxDO.class, o -> { // 等会查询到
           o.setKcdd(null);
           o.setKwbh(null);
           o.setKwms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       kwxxMapper.insert(dbKwxx);
       // 测试 kcdd 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setKcdd(null)));
       // 测试 kwbh 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setKwbh(null)));
       // 测试 kwms 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setKwms(null)));
       // 测试 qybs 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setCreateTime(null)));
       // 准备参数
       KwxxPageReqVO reqVO = new KwxxPageReqVO();
       reqVO.setKcdd(null);
       reqVO.setKwbh(null);
       reqVO.setKwms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<KwxxDO> pageResult = kwxxService.getKwxxPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbKwxx, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetKwxxList() {
       // mock 数据
       KwxxDO dbKwxx = randomPojo(KwxxDO.class, o -> { // 等会查询到
           o.setKcdd(null);
           o.setKwbh(null);
           o.setKwms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       kwxxMapper.insert(dbKwxx);
       // 测试 kcdd 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setKcdd(null)));
       // 测试 kwbh 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setKwbh(null)));
       // 测试 kwms 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setKwms(null)));
       // 测试 qybs 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       kwxxMapper.insert(cloneIgnoreId(dbKwxx, o -> o.setCreateTime(null)));
       // 准备参数
       KwxxExportReqVO reqVO = new KwxxExportReqVO();
       reqVO.setKcdd(null);
       reqVO.setKwbh(null);
       reqVO.setKwms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<KwxxDO> list = kwxxService.getKwxxList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbKwxx, list.get(0));
    }

}
