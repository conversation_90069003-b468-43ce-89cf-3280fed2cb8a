package com.neway.assets.module.info.service.jldwdz;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.JldwdzCreateReqVO;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.JldwdzExportReqVO;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.JldwdzPageReqVO;
import com.neway.assets.module.info.controller.admin.jldwdz.vo.JldwdzUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.jldwdz.JldwdzDO;
import com.neway.assets.module.info.dal.mysql.jldwdz.JldwdzMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.JLDWDZ_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link JldwdzServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(JldwdzServiceImpl.class)
public class JldwdzServiceImplTest extends BaseDbUnitTest {

    @Resource
    private JldwdzServiceImpl jldwdzService;

    @Resource
    private JldwdzMapper jldwdzMapper;

    @Test
    public void testCreateJldwdz_success() {
        // 准备参数
        JldwdzCreateReqVO reqVO = randomPojo(JldwdzCreateReqVO.class);

        // 调用
        Long jldwdzId = jldwdzService.createJldwdz(reqVO);
        // 断言
        assertNotNull(jldwdzId);
        // 校验记录的属性是否正确
        JldwdzDO jldwdz = jldwdzMapper.selectById(jldwdzId);
        assertPojoEquals(reqVO, jldwdz);
    }

    @Test
    public void testUpdateJldwdz_success() {
        // mock 数据
        JldwdzDO dbJldwdz = randomPojo(JldwdzDO.class);
        jldwdzMapper.insert(dbJldwdz);// @Sql: 先插入出一条存在的数据
        // 准备参数
        JldwdzUpdateReqVO reqVO = randomPojo(JldwdzUpdateReqVO.class, o -> {
            o.setId(dbJldwdz.getId()); // 设置更新的 ID
        });

        // 调用
        jldwdzService.updateJldwdz(reqVO);
        // 校验是否更新正确
        JldwdzDO jldwdz = jldwdzMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, jldwdz);
    }

    @Test
    public void testUpdateJldwdz_notExists() {
        // 准备参数
        JldwdzUpdateReqVO reqVO = randomPojo(JldwdzUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> jldwdzService.updateJldwdz(reqVO), JLDWDZ_NOT_EXISTS);
    }

    @Test
    public void testDeleteJldwdz_success() {
        // mock 数据
        JldwdzDO dbJldwdz = randomPojo(JldwdzDO.class);
        jldwdzMapper.insert(dbJldwdz);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbJldwdz.getId();

        // 调用
        jldwdzService.deleteJldwdz(id);
       // 校验数据不存在了
       assertNull(jldwdzMapper.selectById(id));
    }

    @Test
    public void testDeleteJldwdz_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> jldwdzService.deleteJldwdz(id), JLDWDZ_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetJldwdzPage() {
       // mock 数据
       JldwdzDO dbJldwdz = randomPojo(JldwdzDO.class, o -> { // 等会查询到
           o.setCbzx(null);
           o.setYjldw(null);
           o.setYuansl(null);
           o.setZhdw(null);
           o.setZhsl(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       jldwdzMapper.insert(dbJldwdz);
       // 测试 cbzx 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setCbzx(null)));
       // 测试 yjldw 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setYjldw(null)));
       // 测试 yuansl 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setYuansl(null)));
       // 测试 zhdw 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setZhdw(null)));
       // 测试 zhsl 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setZhsl(null)));
       // 测试 qybs 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setCreateTime(null)));
       // 准备参数
       JldwdzPageReqVO reqVO = new JldwdzPageReqVO();
       reqVO.setCbzx(null);
       reqVO.setYjldw(null);
       reqVO.setYuansl(null);
       reqVO.setZhdw(null);
       reqVO.setZhsl(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<JldwdzDO> pageResult = jldwdzService.getJldwdzPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbJldwdz, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetJldwdzList() {
       // mock 数据
       JldwdzDO dbJldwdz = randomPojo(JldwdzDO.class, o -> { // 等会查询到
           o.setCbzx(null);
           o.setYjldw(null);
           o.setYuansl(null);
           o.setZhdw(null);
           o.setZhsl(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       jldwdzMapper.insert(dbJldwdz);
       // 测试 cbzx 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setCbzx(null)));
       // 测试 yjldw 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setYjldw(null)));
       // 测试 yuansl 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setYuansl(null)));
       // 测试 zhdw 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setZhdw(null)));
       // 测试 zhsl 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setZhsl(null)));
       // 测试 qybs 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       jldwdzMapper.insert(cloneIgnoreId(dbJldwdz, o -> o.setCreateTime(null)));
       // 准备参数
       JldwdzExportReqVO reqVO = new JldwdzExportReqVO();
       reqVO.setCbzx(null);
       reqVO.setYjldw(null);
       reqVO.setYuansl(null);
       reqVO.setZhdw(null);
       reqVO.setZhsl(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<JldwdzDO> list = jldwdzService.getJldwdzList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbJldwdz, list.get(0));
    }

}
