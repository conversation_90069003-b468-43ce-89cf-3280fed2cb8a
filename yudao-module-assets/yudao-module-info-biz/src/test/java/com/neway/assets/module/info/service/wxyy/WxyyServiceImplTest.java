package com.neway.assets.module.info.service.wxyy;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;


import com.neway.assets.module.info.controller.admin.wxyy.vo.*;
import com.neway.assets.module.info.dal.dataobject.wxyy.WxyyDO;
import com.neway.assets.module.info.dal.mysql.wxyy.WxyyMapper;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link WxyyServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(WxyyServiceImpl.class)
public class WxyyServiceImplTest extends BaseDbUnitTest {

    @Resource
    private WxyyServiceImpl wxyyService;

    @Resource
    private WxyyMapper wxyyMapper;

    @Test
    public void testCreateWxyy_success() {
        // 准备参数
        WxyyCreateReqVO reqVO = randomPojo(WxyyCreateReqVO.class);

        // 调用
        Long wxyyId = wxyyService.createWxyy(reqVO);
        // 断言
        assertNotNull(wxyyId);
        // 校验记录的属性是否正确
        WxyyDO wxyy = wxyyMapper.selectById(wxyyId);
        assertPojoEquals(reqVO, wxyy);
    }

    @Test
    public void testUpdateWxyy_success() {
        // mock 数据
        WxyyDO dbWxyy = randomPojo(WxyyDO.class);
        wxyyMapper.insert(dbWxyy);// @Sql: 先插入出一条存在的数据
        // 准备参数
        WxyyUpdateReqVO reqVO = randomPojo(WxyyUpdateReqVO.class, o -> {
            o.setId(dbWxyy.getId()); // 设置更新的 ID
        });

        // 调用
        wxyyService.updateWxyy(reqVO);
        // 校验是否更新正确
        WxyyDO wxyy = wxyyMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, wxyy);
    }

    @Test
    public void testUpdateWxyy_notExists() {
        // 准备参数
        WxyyUpdateReqVO reqVO = randomPojo(WxyyUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> wxyyService.updateWxyy(reqVO), WXYY_NOT_EXISTS);
    }

    @Test
    public void testDeleteWxyy_success() {
        // mock 数据
        WxyyDO dbWxyy = randomPojo(WxyyDO.class);
        wxyyMapper.insert(dbWxyy);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbWxyy.getId();

        // 调用
        wxyyService.deleteWxyy(id);
       // 校验数据不存在了
       assertNull(wxyyMapper.selectById(id));
    }

    @Test
    public void testDeleteWxyy_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> wxyyService.deleteWxyy(id), WXYY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetWxyyPage() {
       // mock 数据
       WxyyDO dbWxyy = randomPojo(WxyyDO.class, o -> { // 等会查询到
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       wxyyMapper.insert(dbWxyy);
       // 测试 qybs 不匹配
       wxyyMapper.insert(cloneIgnoreId(dbWxyy, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       wxyyMapper.insert(cloneIgnoreId(dbWxyy, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       wxyyMapper.insert(cloneIgnoreId(dbWxyy, o -> o.setCreateTime(null)));
       // 准备参数
       WxyyPageReqVO reqVO = new WxyyPageReqVO();
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<WxyyDO> pageResult = wxyyService.getWxyyPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbWxyy, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetWxyyList() {
       // mock 数据
       WxyyDO dbWxyy = randomPojo(WxyyDO.class, o -> { // 等会查询到
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       wxyyMapper.insert(dbWxyy);
       // 测试 qybs 不匹配
       wxyyMapper.insert(cloneIgnoreId(dbWxyy, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       wxyyMapper.insert(cloneIgnoreId(dbWxyy, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       wxyyMapper.insert(cloneIgnoreId(dbWxyy, o -> o.setCreateTime(null)));
       // 准备参数
       WxyyExportReqVO reqVO = new WxyyExportReqVO();
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<WxyyDO> list = wxyyService.getWxyyList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbWxyy, list.get(0));
    }

}
