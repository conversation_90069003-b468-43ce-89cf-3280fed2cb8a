package com.neway.assets.module.info.service.zcfl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.zcfl.vo.ZcflCreateReqVO;
import com.neway.assets.module.info.controller.admin.zcfl.vo.ZcflExportReqVO;
import com.neway.assets.module.info.controller.admin.zcfl.vo.ZcflPageReqVO;
import com.neway.assets.module.info.controller.admin.zcfl.vo.ZcflUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.zcfl.ZcflDO;
import com.neway.assets.module.info.dal.mysql.zcfl.ZcflMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZCFL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcflServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcflServiceImpl.class)
public class ZcflServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcflServiceImpl zcflService;

    @Resource
    private ZcflMapper zcflMapper;

    @Test
    public void testCreateZcfl_success() {
        // 准备参数
        ZcflCreateReqVO reqVO = randomPojo(ZcflCreateReqVO.class);

        // 调用
        Long zcflId = zcflService.createZcfl(reqVO);
        // 断言
        assertNotNull(zcflId);
        // 校验记录的属性是否正确
        ZcflDO zcfl = zcflMapper.selectById(zcflId);
        assertPojoEquals(reqVO, zcfl);
    }

    @Test
    public void testUpdateZcfl_success() {
        // mock 数据
        ZcflDO dbZcfl = randomPojo(ZcflDO.class);
        zcflMapper.insert(dbZcfl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcflUpdateReqVO reqVO = randomPojo(ZcflUpdateReqVO.class, o -> {
            o.setId(dbZcfl.getId()); // 设置更新的 ID
        });

        // 调用
        zcflService.updateZcfl(reqVO);
        // 校验是否更新正确
        ZcflDO zcfl = zcflMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcfl);
    }

    @Test
    public void testUpdateZcfl_notExists() {
        // 准备参数
        ZcflUpdateReqVO reqVO = randomPojo(ZcflUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcflService.updateZcfl(reqVO), ZCFL_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcfl_success() {
        // mock 数据
        ZcflDO dbZcfl = randomPojo(ZcflDO.class);
        zcflMapper.insert(dbZcfl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcfl.getId();

        // 调用
        zcflService.deleteZcfl(id);
       // 校验数据不存在了
       assertNull(zcflMapper.selectById(id));
    }

    @Test
    public void testDeleteZcfl_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcflService.deleteZcfl(id), ZCFL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcflPage() {
       // mock 数据
       ZcflDO dbZcfl = randomPojo(ZcflDO.class, o -> { // 等会查询到
           o.setZcflfs(null);
           o.setZcflms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcflMapper.insert(dbZcfl);
       // 测试 zcflfs 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setZcflfs(null)));
       // 测试 zcflms 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setZcflms(null)));
       // 测试 qybs 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setCreateTime(null)));
       // 准备参数
       ZcflPageReqVO reqVO = new ZcflPageReqVO();
       reqVO.setZcflfs(null);
       reqVO.setZcflms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcflDO> pageResult = zcflService.getZcflPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcfl, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcflList() {
       // mock 数据
       ZcflDO dbZcfl = randomPojo(ZcflDO.class, o -> { // 等会查询到
           o.setZcflfs(null);
           o.setZcflms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcflMapper.insert(dbZcfl);
       // 测试 zcflfs 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setZcflfs(null)));
       // 测试 zcflms 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setZcflms(null)));
       // 测试 qybs 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcflMapper.insert(cloneIgnoreId(dbZcfl, o -> o.setCreateTime(null)));
       // 准备参数
       ZcflExportReqVO reqVO = new ZcflExportReqVO();
       reqVO.setZcflfs(null);
       reqVO.setZcflms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcflDO> list = zcflService.getZcflList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcfl, list.get(0));
    }

}
