package com.neway.assets.module.info.service.zcxz;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.zcxz.vo.ZcxzCreateReqVO;
import com.neway.assets.module.info.controller.admin.zcxz.vo.ZcxzExportReqVO;
import com.neway.assets.module.info.controller.admin.zcxz.vo.ZcxzPageReqVO;
import com.neway.assets.module.info.controller.admin.zcxz.vo.ZcxzUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.zcxz.ZcxzDO;
import com.neway.assets.module.info.dal.mysql.zcxz.ZcxzMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZCXZ_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcxzServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcxzServiceImpl.class)
public class ZcxzServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcxzServiceImpl zcxzService;

    @Resource
    private ZcxzMapper zcxzMapper;

    @Test
    public void testCreateZcxz_success() {
        // 准备参数
        ZcxzCreateReqVO reqVO = randomPojo(ZcxzCreateReqVO.class);

        // 调用
        Integer zcxzId = zcxzService.createZcxz(reqVO);
        // 断言
        assertNotNull(zcxzId);
        // 校验记录的属性是否正确
        ZcxzDO zcxz = zcxzMapper.selectById(zcxzId);
        assertPojoEquals(reqVO, zcxz);
    }

    @Test
    public void testUpdateZcxz_success() {
        // mock 数据
        ZcxzDO dbZcxz = randomPojo(ZcxzDO.class);
        zcxzMapper.insert(dbZcxz);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcxzUpdateReqVO reqVO = randomPojo(ZcxzUpdateReqVO.class, o -> {
            o.setId(dbZcxz.getId()); // 设置更新的 ID
        });

        // 调用
        zcxzService.updateZcxz(reqVO);
        // 校验是否更新正确
        ZcxzDO zcxz = zcxzMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcxz);
    }

    @Test
    public void testUpdateZcxz_notExists() {
        // 准备参数
        ZcxzUpdateReqVO reqVO = randomPojo(ZcxzUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcxzService.updateZcxz(reqVO), ZCXZ_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcxz_success() {
        // mock 数据
        ZcxzDO dbZcxz = randomPojo(ZcxzDO.class);
        zcxzMapper.insert(dbZcxz);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Integer id = dbZcxz.getId();

        // 调用
        zcxzService.deleteZcxz(id);
       // 校验数据不存在了
       assertNull(zcxzMapper.selectById(id));
    }

    @Test
    public void testDeleteZcxz_notExists() {
        // 准备参数
        Integer id = randomInteger();

        // 调用, 并断言异常
        assertServiceException(() -> zcxzService.deleteZcxz(id), ZCXZ_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcxzPage() {
       // mock 数据
       ZcxzDO dbZcxz = randomPojo(ZcxzDO.class, o -> { // 等会查询到
           o.setZcxzms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcxzMapper.insert(dbZcxz);
       // 测试 zcxzms 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setZcxzms(null)));
       // 测试 qybs 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setCreateTime(null)));
       // 准备参数
       ZcxzPageReqVO reqVO = new ZcxzPageReqVO();
       reqVO.setZcxzms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcxzDO> pageResult = zcxzService.getZcxzPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcxz, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcxzList() {
       // mock 数据
       ZcxzDO dbZcxz = randomPojo(ZcxzDO.class, o -> { // 等会查询到
           o.setZcxzms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcxzMapper.insert(dbZcxz);
       // 测试 zcxzms 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setZcxzms(null)));
       // 测试 qybs 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcxzMapper.insert(cloneIgnoreId(dbZcxz, o -> o.setCreateTime(null)));
       // 准备参数
       ZcxzExportReqVO reqVO = new ZcxzExportReqVO();
       reqVO.setZcxzms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcxzDO> list = zcxzService.getZcxzList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcxz, list.get(0));
    }

}
