package com.neway.assets.module.info.service.hjxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.hjxx.vo.HjxxCreateReqVO;
import com.neway.assets.module.info.controller.admin.hjxx.vo.HjxxExportReqVO;
import com.neway.assets.module.info.controller.admin.hjxx.vo.HjxxPageReqVO;
import com.neway.assets.module.info.controller.admin.hjxx.vo.HjxxUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.hjxx.HjxxDO;
import com.neway.assets.module.info.dal.mysql.hjxx.HjxxMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.HJXX_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link HjxxServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(HjxxServiceImpl.class)
public class HjxxServiceImplTest extends BaseDbUnitTest {

    @Resource
    private HjxxServiceImpl hjxxService;

    @Resource
    private HjxxMapper hjxxMapper;

    @Test
    public void testCreateHjxx_success() {
        // 准备参数
        HjxxCreateReqVO reqVO = randomPojo(HjxxCreateReqVO.class);

        // 调用
        Long hjxxId = hjxxService.createHjxx(reqVO);
        // 断言
        assertNotNull(hjxxId);
        // 校验记录的属性是否正确
        HjxxDO hjxx = hjxxMapper.selectById(hjxxId);
        assertPojoEquals(reqVO, hjxx);
    }

    @Test
    public void testUpdateHjxx_success() {
        // mock 数据
        HjxxDO dbHjxx = randomPojo(HjxxDO.class);
        hjxxMapper.insert(dbHjxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        HjxxUpdateReqVO reqVO = randomPojo(HjxxUpdateReqVO.class, o -> {
            o.setId(dbHjxx.getId()); // 设置更新的 ID
        });

        // 调用
        hjxxService.updateHjxx(reqVO);
        // 校验是否更新正确
        HjxxDO hjxx = hjxxMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, hjxx);
    }

    @Test
    public void testUpdateHjxx_notExists() {
        // 准备参数
        HjxxUpdateReqVO reqVO = randomPojo(HjxxUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> hjxxService.updateHjxx(reqVO), HJXX_NOT_EXISTS);
    }

    @Test
    public void testDeleteHjxx_success() {
        // mock 数据
        HjxxDO dbHjxx = randomPojo(HjxxDO.class);
        hjxxMapper.insert(dbHjxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbHjxx.getId();

        // 调用
        hjxxService.deleteHjxx(id);
       // 校验数据不存在了
       assertNull(hjxxMapper.selectById(id));
    }

    @Test
    public void testDeleteHjxx_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> hjxxService.deleteHjxx(id), HJXX_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHjxxPage() {
       // mock 数据
       HjxxDO dbHjxx = randomPojo(HjxxDO.class, o -> { // 等会查询到
           o.setKw(null);
           o.setHjbh(null);
           o.setHjms(null);
           o.setHang(null);
           o.setLie(null);
           o.setCeng(null);
           o.setDany(null);
           o.setQybs(null);
           o.setCreateTime(null);
       });
       hjxxMapper.insert(dbHjxx);
       // 测试 kw 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setKw(null)));
       // 测试 hjbh 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setHjbh(null)));
       // 测试 hjms 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setHjms(null)));
       // 测试 hang 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setHang(null)));
       // 测试 lie 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setLie(null)));
       // 测试 ceng 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setCeng(null)));
       // 测试 dany 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setDany(null)));
       // 测试 qybs 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setQybs(null)));
       // 测试 createTime 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setCreateTime(null)));
       // 准备参数
       HjxxPageReqVO reqVO = new HjxxPageReqVO();
       reqVO.setKw(null);
       reqVO.setHjbh(null);
       reqVO.setHjms(null);
       reqVO.setHang(null);
       reqVO.setLie(null);
       reqVO.setCeng(null);
       reqVO.setDany(null);
       reqVO.setQybs(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<HjxxDO> pageResult = hjxxService.getHjxxPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbHjxx, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHjxxList() {
       // mock 数据
       HjxxDO dbHjxx = randomPojo(HjxxDO.class, o -> { // 等会查询到
           o.setKw(null);
           o.setHjbh(null);
           o.setHjms(null);
           o.setHang(null);
           o.setLie(null);
           o.setCeng(null);
           o.setDany(null);
           o.setQybs(null);
           o.setCreateTime(null);
       });
       hjxxMapper.insert(dbHjxx);
       // 测试 kw 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setKw(null)));
       // 测试 hjbh 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setHjbh(null)));
       // 测试 hjms 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setHjms(null)));
       // 测试 hang 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setHang(null)));
       // 测试 lie 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setLie(null)));
       // 测试 ceng 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setCeng(null)));
       // 测试 dany 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setDany(null)));
       // 测试 qybs 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setQybs(null)));
       // 测试 createTime 不匹配
       hjxxMapper.insert(cloneIgnoreId(dbHjxx, o -> o.setCreateTime(null)));
       // 准备参数
       HjxxExportReqVO reqVO = new HjxxExportReqVO();
       reqVO.setKw(null);
       reqVO.setHjbh(null);
       reqVO.setHjms(null);
       reqVO.setHang(null);
       reqVO.setLie(null);
       reqVO.setCeng(null);
       reqVO.setDany(null);
       reqVO.setQybs(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<HjxxDO> list = hjxxService.getHjxxList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbHjxx, list.get(0));
    }

}
