package com.neway.assets.module.info.service.hbjfl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.HbjflCreateReqVO;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.HbjflExportReqVO;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.HbjflPageReqVO;
import com.neway.assets.module.info.controller.admin.hbjfl.vo.HbjflUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.hbjfl.HbjflDO;
import com.neway.assets.module.info.dal.mysql.hbjfl.HbjflMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.HBJFL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link HbjflServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(HbjflServiceImpl.class)
public class HbjflServiceImplTest extends BaseDbUnitTest {

    @Resource
    private HbjflServiceImpl hbjflService;

    @Resource
    private HbjflMapper hbjflMapper;

    @Test
    public void testCreateHbjfl_success() {
        // 准备参数
        HbjflCreateReqVO reqVO = randomPojo(HbjflCreateReqVO.class);

        // 调用
        Long hbjflId = hbjflService.createHbjfl(reqVO);
        // 断言
        assertNotNull(hbjflId);
        // 校验记录的属性是否正确
        HbjflDO hbjfl = hbjflMapper.selectById(hbjflId);
        assertPojoEquals(reqVO, hbjfl);
    }

    @Test
    public void testUpdateHbjfl_success() {
        // mock 数据
        HbjflDO dbHbjfl = randomPojo(HbjflDO.class);
        hbjflMapper.insert(dbHbjfl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        HbjflUpdateReqVO reqVO = randomPojo(HbjflUpdateReqVO.class, o -> {
            o.setId(dbHbjfl.getId()); // 设置更新的 ID
        });

        // 调用
        hbjflService.updateHbjfl(reqVO);
        // 校验是否更新正确
        HbjflDO hbjfl = hbjflMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, hbjfl);
    }

    @Test
    public void testUpdateHbjfl_notExists() {
        // 准备参数
        HbjflUpdateReqVO reqVO = randomPojo(HbjflUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> hbjflService.updateHbjfl(reqVO), HBJFL_NOT_EXISTS);
    }

    @Test
    public void testDeleteHbjfl_success() {
        // mock 数据
        HbjflDO dbHbjfl = randomPojo(HbjflDO.class);
        hbjflMapper.insert(dbHbjfl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbHbjfl.getId();

        // 调用
        hbjflService.deleteHbjfl(id);
       // 校验数据不存在了
       assertNull(hbjflMapper.selectById(id));
    }

    @Test
    public void testDeleteHbjfl_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> hbjflService.deleteHbjfl(id), HBJFL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHbjflPage() {
       // mock 数据
       HbjflDO dbHbjfl = randomPojo(HbjflDO.class, o -> { // 等会查询到
           o.setBflmc(null);
           o.setCbjsfs(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       hbjflMapper.insert(dbHbjfl);
       // 测试 bflmc 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setBflmc(null)));
       // 测试 cbjsfs 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setCbjsfs(null)));
       // 测试 qybs 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setCreateTime(null)));
       // 准备参数
       HbjflPageReqVO reqVO = new HbjflPageReqVO();
       reqVO.setBflmc(null);
       reqVO.setCbjsfs(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<HbjflDO> pageResult = hbjflService.getHbjflPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbHbjfl, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetHbjflList() {
       // mock 数据
       HbjflDO dbHbjfl = randomPojo(HbjflDO.class, o -> { // 等会查询到
           o.setBflmc(null);
           o.setCbjsfs(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       hbjflMapper.insert(dbHbjfl);
       // 测试 bflmc 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setBflmc(null)));
       // 测试 cbjsfs 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setCbjsfs(null)));
       // 测试 qybs 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       hbjflMapper.insert(cloneIgnoreId(dbHbjfl, o -> o.setCreateTime(null)));
       // 准备参数
       HbjflExportReqVO reqVO = new HbjflExportReqVO();
       reqVO.setBflmc(null);
       reqVO.setCbjsfs(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<HbjflDO> list = hbjflService.getHbjflList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbHbjfl, list.get(0));
    }

}
