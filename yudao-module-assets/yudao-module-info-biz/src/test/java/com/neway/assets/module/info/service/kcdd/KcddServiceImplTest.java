package com.neway.assets.module.info.service.kcdd;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.kcdd.vo.KcddCreateReqVO;
import com.neway.assets.module.info.controller.admin.kcdd.vo.KcddExportReqVO;
import com.neway.assets.module.info.controller.admin.kcdd.vo.KcddPageReqVO;
import com.neway.assets.module.info.controller.admin.kcdd.vo.KcddUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.kcdd.KcddDO;
import com.neway.assets.module.info.dal.mysql.kcdd.KcddMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.KCDD_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link KcddServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(KcddServiceImpl.class)
public class KcddServiceImplTest extends BaseDbUnitTest {

    @Resource
    private KcddServiceImpl kcddService;

    @Resource
    private KcddMapper kcddMapper;

    @Test
    public void testCreateKcdd_success() {
        // 准备参数
        KcddCreateReqVO reqVO = randomPojo(KcddCreateReqVO.class);

        // 调用
        Long kcddId = kcddService.createKcdd(reqVO);
        // 断言
        assertNotNull(kcddId);
        // 校验记录的属性是否正确
        KcddDO kcdd = kcddMapper.selectById(kcddId);
        assertPojoEquals(reqVO, kcdd);
    }

    @Test
    public void testUpdateKcdd_success() {
        // mock 数据
        KcddDO dbKcdd = randomPojo(KcddDO.class);
        kcddMapper.insert(dbKcdd);// @Sql: 先插入出一条存在的数据
        // 准备参数
        KcddUpdateReqVO reqVO = randomPojo(KcddUpdateReqVO.class, o -> {
            o.setId(dbKcdd.getId()); // 设置更新的 ID
        });

        // 调用
        kcddService.updateKcdd(reqVO);
        // 校验是否更新正确
        KcddDO kcdd = kcddMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, kcdd);
    }

    @Test
    public void testUpdateKcdd_notExists() {
        // 准备参数
        KcddUpdateReqVO reqVO = randomPojo(KcddUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> kcddService.updateKcdd(reqVO), KCDD_NOT_EXISTS);
    }

    @Test
    public void testDeleteKcdd_success() {
        // mock 数据
        KcddDO dbKcdd = randomPojo(KcddDO.class);
        kcddMapper.insert(dbKcdd);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbKcdd.getId();

        // 调用
        kcddService.deleteKcdd(id);
       // 校验数据不存在了
       assertNull(kcddMapper.selectById(id));
    }

    @Test
    public void testDeleteKcdd_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> kcddService.deleteKcdd(id), KCDD_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetKcddPage() {
       // mock 数据
       KcddDO dbKcdd = randomPojo(KcddDO.class, o -> { // 等会查询到
           o.setGs(null);
           o.setGc(null);
           o.setKcddms(null);
           o.setKcddbh(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       kcddMapper.insert(dbKcdd);
       // 测试 gs 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setGs(null)));
       // 测试 gc 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setGc(null)));
       // 测试 kcddms 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setKcddms(null)));
       // 测试 kcddbh 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setKcddbh(null)));
       // 测试 qybs 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setCreateTime(null)));
       // 准备参数
       KcddPageReqVO reqVO = new KcddPageReqVO();
       reqVO.setGs(null);
       reqVO.setGc(null);
       reqVO.setKcddms(null);
       reqVO.setKcddbh(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<KcddDO> pageResult = kcddService.getKcddPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbKcdd, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetKcddList() {
       // mock 数据
       KcddDO dbKcdd = randomPojo(KcddDO.class, o -> { // 等会查询到
           o.setGs(null);
           o.setGc(null);
           o.setKcddms(null);
           o.setKcddbh(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       kcddMapper.insert(dbKcdd);
       // 测试 gs 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setGs(null)));
       // 测试 gc 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setGc(null)));
       // 测试 kcddms 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setKcddms(null)));
       // 测试 kcddbh 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setKcddbh(null)));
       // 测试 qybs 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       kcddMapper.insert(cloneIgnoreId(dbKcdd, o -> o.setCreateTime(null)));
       // 准备参数
       KcddExportReqVO reqVO = new KcddExportReqVO();
       reqVO.setGs(null);
       reqVO.setGc(null);
       reqVO.setKcddms(null);
       reqVO.setKcddbh(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<KcddDO> list = kcddService.getKcddList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbKcdd, list.get(0));
    }

}
