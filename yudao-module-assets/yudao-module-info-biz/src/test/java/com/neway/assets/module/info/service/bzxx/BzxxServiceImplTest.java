package com.neway.assets.module.info.service.bzxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.bzxx.vo.BzxxCreateReqVO;
import com.neway.assets.module.info.controller.admin.bzxx.vo.BzxxExportReqVO;
import com.neway.assets.module.info.controller.admin.bzxx.vo.BzxxPageReqVO;
import com.neway.assets.module.info.controller.admin.bzxx.vo.BzxxUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.bzxx.BzxxDO;
import com.neway.assets.module.info.dal.mysql.bzxx.BzxxMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.BZXX_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BzxxServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BzxxServiceImpl.class)
public class BzxxServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BzxxServiceImpl bzxxService;

    @Resource
    private BzxxMapper bzxxMapper;

    @Test
    public void testCreateBzxx_success() {
        // 准备参数
        BzxxCreateReqVO reqVO = randomPojo(BzxxCreateReqVO.class);

        // 调用
        Long bzxxId = bzxxService.createBzxx(reqVO);
        // 断言
        assertNotNull(bzxxId);
        // 校验记录的属性是否正确
        BzxxDO bzxx = bzxxMapper.selectById(bzxxId);
        assertPojoEquals(reqVO, bzxx);
    }

    @Test
    public void testUpdateBzxx_success() {
        // mock 数据
        BzxxDO dbBzxx = randomPojo(BzxxDO.class);
        bzxxMapper.insert(dbBzxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BzxxUpdateReqVO reqVO = randomPojo(BzxxUpdateReqVO.class, o -> {
            o.setId(dbBzxx.getId()); // 设置更新的 ID
        });

        // 调用
        bzxxService.updateBzxx(reqVO);
        // 校验是否更新正确
        BzxxDO bzxx = bzxxMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, bzxx);
    }

    @Test
    public void testUpdateBzxx_notExists() {
        // 准备参数
        BzxxUpdateReqVO reqVO = randomPojo(BzxxUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> bzxxService.updateBzxx(reqVO), BZXX_NOT_EXISTS);
    }

    @Test
    public void testDeleteBzxx_success() {
        // mock 数据
        BzxxDO dbBzxx = randomPojo(BzxxDO.class);
        bzxxMapper.insert(dbBzxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbBzxx.getId();

        // 调用
        bzxxService.deleteBzxx(id);
       // 校验数据不存在了
       assertNull(bzxxMapper.selectById(id));
    }

    @Test
    public void testDeleteBzxx_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> bzxxService.deleteBzxx(id), BZXX_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetBzxxPage() {
       // mock 数据
       BzxxDO dbBzxx = randomPojo(BzxxDO.class, o -> { // 等会查询到
           o.setBzjc(null);
           o.setBzmc(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       bzxxMapper.insert(dbBzxx);
       // 测试 bzjc 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setBzjc(null)));
       // 测试 bzmc 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setBzmc(null)));
       // 测试 qybs 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setCreateTime(null)));
       // 准备参数
       BzxxPageReqVO reqVO = new BzxxPageReqVO();
       reqVO.setBzjc(null);
       reqVO.setBzmc(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BzxxDO> pageResult = bzxxService.getBzxxPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbBzxx, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetBzxxList() {
       // mock 数据
       BzxxDO dbBzxx = randomPojo(BzxxDO.class, o -> { // 等会查询到
           o.setBzjc(null);
           o.setBzmc(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       bzxxMapper.insert(dbBzxx);
       // 测试 bzjc 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setBzjc(null)));
       // 测试 bzmc 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setBzmc(null)));
       // 测试 qybs 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       bzxxMapper.insert(cloneIgnoreId(dbBzxx, o -> o.setCreateTime(null)));
       // 准备参数
       BzxxExportReqVO reqVO = new BzxxExportReqVO();
       reqVO.setBzjc(null);
       reqVO.setBzmc(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BzxxDO> list = bzxxService.getBzxxList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbBzxx, list.get(0));
    }

}
