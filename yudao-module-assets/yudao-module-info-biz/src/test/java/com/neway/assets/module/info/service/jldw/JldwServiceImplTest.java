package com.neway.assets.module.info.service.jldw;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.jldw.vo.JldwCreateReqVO;
import com.neway.assets.module.info.controller.admin.jldw.vo.JldwExportReqVO;
import com.neway.assets.module.info.controller.admin.jldw.vo.JldwPageReqVO;
import com.neway.assets.module.info.controller.admin.jldw.vo.JldwUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.jldw.JldwDO;
import com.neway.assets.module.info.dal.mysql.jldw.JldwMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.JLDW_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link JldwServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(JldwServiceImpl.class)
public class JldwServiceImplTest extends BaseDbUnitTest {

    @Resource
    private JldwServiceImpl jldwService;

    @Resource
    private JldwMapper jldwMapper;

    @Test
    public void testCreateJldw_success() {
        // 准备参数
        JldwCreateReqVO reqVO = randomPojo(JldwCreateReqVO.class);

        // 调用
        Long jldwId = jldwService.createJldw(reqVO);
        // 断言
        assertNotNull(jldwId);
        // 校验记录的属性是否正确
        JldwDO jldw = jldwMapper.selectById(jldwId);
        assertPojoEquals(reqVO, jldw);
    }

    @Test
    public void testUpdateJldw_success() {
        // mock 数据
        JldwDO dbJldw = randomPojo(JldwDO.class);
        jldwMapper.insert(dbJldw);// @Sql: 先插入出一条存在的数据
        // 准备参数
        JldwUpdateReqVO reqVO = randomPojo(JldwUpdateReqVO.class, o -> {
            o.setId(dbJldw.getId()); // 设置更新的 ID
        });

        // 调用
        jldwService.updateJldw(reqVO);
        // 校验是否更新正确
        JldwDO jldw = jldwMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, jldw);
    }

    @Test
    public void testUpdateJldw_notExists() {
        // 准备参数
        JldwUpdateReqVO reqVO = randomPojo(JldwUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> jldwService.updateJldw(reqVO), JLDW_NOT_EXISTS);
    }

    @Test
    public void testDeleteJldw_success() {
        // mock 数据
        JldwDO dbJldw = randomPojo(JldwDO.class);
        jldwMapper.insert(dbJldw);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbJldw.getId();

        // 调用
        jldwService.deleteJldw(id);
       // 校验数据不存在了
       assertNull(jldwMapper.selectById(id));
    }

    @Test
    public void testDeleteJldw_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> jldwService.deleteJldw(id), JLDW_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetJldwPage() {
       // mock 数据
       JldwDO dbJldw = randomPojo(JldwDO.class, o -> { // 等会查询到
           o.setJldwjc(null);
           o.setJldwmc(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       jldwMapper.insert(dbJldw);
       // 测试 jldwjc 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setJldwjc(null)));
       // 测试 jldwmc 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setJldwmc(null)));
       // 测试 qybs 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setCreateTime(null)));
       // 准备参数
       JldwPageReqVO reqVO = new JldwPageReqVO();
       reqVO.setJldwjc(null);
       reqVO.setJldwmc(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<JldwDO> pageResult = jldwService.getJldwPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbJldw, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetJldwList() {
       // mock 数据
       JldwDO dbJldw = randomPojo(JldwDO.class, o -> { // 等会查询到
           o.setJldwjc(null);
           o.setJldwmc(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       jldwMapper.insert(dbJldw);
       // 测试 jldwjc 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setJldwjc(null)));
       // 测试 jldwmc 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setJldwmc(null)));
       // 测试 qybs 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       jldwMapper.insert(cloneIgnoreId(dbJldw, o -> o.setCreateTime(null)));
       // 准备参数
       JldwExportReqVO reqVO = new JldwExportReqVO();
       reqVO.setJldwjc(null);
       reqVO.setJldwmc(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<JldwDO> list = jldwService.getJldwList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbJldw, list.get(0));
    }

}
