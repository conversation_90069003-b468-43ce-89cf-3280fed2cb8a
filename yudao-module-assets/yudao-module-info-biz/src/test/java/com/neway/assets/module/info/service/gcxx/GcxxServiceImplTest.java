package com.neway.assets.module.info.service.gcxx;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.gcxx.vo.GcxxCreateReqVO;
import com.neway.assets.module.info.controller.admin.gcxx.vo.GcxxExportReqVO;
import com.neway.assets.module.info.controller.admin.gcxx.vo.GcxxPageReqVO;
import com.neway.assets.module.info.controller.admin.gcxx.vo.GcxxUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.gcxx.GcxxDO;
import com.neway.assets.module.info.dal.mysql.gcxx.GcxxMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.GCXX_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link GcxxServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(GcxxServiceImpl.class)
public class GcxxServiceImplTest extends BaseDbUnitTest {

    @Resource
    private GcxxServiceImpl gcxxService;

    @Resource
    private GcxxMapper gcxxMapper;

    @Test
    public void testCreateGcxx_success() {
        // 准备参数
        GcxxCreateReqVO reqVO = randomPojo(GcxxCreateReqVO.class);

        // 调用
        Long gcxxId = gcxxService.createGcxx(reqVO);
        // 断言
        assertNotNull(gcxxId);
        // 校验记录的属性是否正确
        GcxxDO gcxx = gcxxMapper.selectById(gcxxId);
        assertPojoEquals(reqVO, gcxx);
    }

    @Test
    public void testUpdateGcxx_success() {
        // mock 数据
        GcxxDO dbGcxx = randomPojo(GcxxDO.class);
        gcxxMapper.insert(dbGcxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        GcxxUpdateReqVO reqVO = randomPojo(GcxxUpdateReqVO.class, o -> {
            o.setId(dbGcxx.getId()); // 设置更新的 ID
        });

        // 调用
        gcxxService.updateGcxx(reqVO);
        // 校验是否更新正确
        GcxxDO gcxx = gcxxMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, gcxx);
    }

    @Test
    public void testUpdateGcxx_notExists() {
        // 准备参数
        GcxxUpdateReqVO reqVO = randomPojo(GcxxUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> gcxxService.updateGcxx(reqVO), GCXX_NOT_EXISTS);
    }

    @Test
    public void testDeleteGcxx_success() {
        // mock 数据
        GcxxDO dbGcxx = randomPojo(GcxxDO.class);
        gcxxMapper.insert(dbGcxx);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbGcxx.getId();

        // 调用
        gcxxService.deleteGcxx(id);
       // 校验数据不存在了
       assertNull(gcxxMapper.selectById(id));
    }

    @Test
    public void testDeleteGcxx_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> gcxxService.deleteGcxx(id), GCXX_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetGcxxPage() {
       // mock 数据
       GcxxDO dbGcxx = randomPojo(GcxxDO.class, o -> { // 等会查询到
           o.setGs(null);
           o.setGcms(null);
           o.setDzxx(null);
           o.setJwdxx(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       gcxxMapper.insert(dbGcxx);
       // 测试 gs 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setGs(null)));
       // 测试 gcms 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setGcms(null)));
       // 测试 dzxx 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setDzxx(null)));
       // 测试 jwdxx 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setJwdxx(null)));
       // 测试 qybs 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setCreateTime(null)));
       // 准备参数
       GcxxPageReqVO reqVO = new GcxxPageReqVO();
       reqVO.setGs(null);
       reqVO.setGcms(null);
       reqVO.setDzxx(null);
       reqVO.setJwdxx(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<GcxxDO> pageResult = gcxxService.getGcxxPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbGcxx, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetGcxxList() {
       // mock 数据
       GcxxDO dbGcxx = randomPojo(GcxxDO.class, o -> { // 等会查询到
           o.setGs(null);
           o.setGcms(null);
           o.setDzxx(null);
           o.setJwdxx(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       gcxxMapper.insert(dbGcxx);
       // 测试 gs 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setGs(null)));
       // 测试 gcms 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setGcms(null)));
       // 测试 dzxx 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setDzxx(null)));
       // 测试 jwdxx 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setJwdxx(null)));
       // 测试 qybs 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       gcxxMapper.insert(cloneIgnoreId(dbGcxx, o -> o.setCreateTime(null)));
       // 准备参数
       GcxxExportReqVO reqVO = new GcxxExportReqVO();
       reqVO.setGs(null);
       reqVO.setGcms(null);
       reqVO.setDzxx(null);
       reqVO.setJwdxx(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<GcxxDO> list = gcxxService.getGcxxList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbGcxx, list.get(0));
    }

}
