package com.neway.assets.module.info.service.zjfs;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.zjfs.vo.ZjfsCreateReqVO;
import com.neway.assets.module.info.controller.admin.zjfs.vo.ZjfsExportReqVO;
import com.neway.assets.module.info.controller.admin.zjfs.vo.ZjfsPageReqVO;
import com.neway.assets.module.info.controller.admin.zjfs.vo.ZjfsUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.zjfs.ZjfsDO;
import com.neway.assets.module.info.dal.mysql.zjfs.ZjfsMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZJFS_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZjfsServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZjfsServiceImpl.class)
public class ZjfsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZjfsServiceImpl zjfsService;

    @Resource
    private ZjfsMapper zjfsMapper;

    @Test
    public void testCreateZjfs_success() {
        // 准备参数
        ZjfsCreateReqVO reqVO = randomPojo(ZjfsCreateReqVO.class);

        // 调用
        Long zjfsId = zjfsService.createZjfs(reqVO);
        // 断言
        assertNotNull(zjfsId);
        // 校验记录的属性是否正确
        ZjfsDO zjfs = zjfsMapper.selectById(zjfsId);
        assertPojoEquals(reqVO, zjfs);
    }

    @Test
    public void testUpdateZjfs_success() {
        // mock 数据
        ZjfsDO dbZjfs = randomPojo(ZjfsDO.class);
        zjfsMapper.insert(dbZjfs);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZjfsUpdateReqVO reqVO = randomPojo(ZjfsUpdateReqVO.class, o -> {
            o.setId(dbZjfs.getId()); // 设置更新的 ID
        });

        // 调用
        zjfsService.updateZjfs(reqVO);
        // 校验是否更新正确
        ZjfsDO zjfs = zjfsMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zjfs);
    }

    @Test
    public void testUpdateZjfs_notExists() {
        // 准备参数
        ZjfsUpdateReqVO reqVO = randomPojo(ZjfsUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zjfsService.updateZjfs(reqVO), ZJFS_NOT_EXISTS);
    }

    @Test
    public void testDeleteZjfs_success() {
        // mock 数据
        ZjfsDO dbZjfs = randomPojo(ZjfsDO.class);
        zjfsMapper.insert(dbZjfs);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZjfs.getId();

        // 调用
        zjfsService.deleteZjfs(id);
       // 校验数据不存在了
       assertNull(zjfsMapper.selectById(id));
    }

    @Test
    public void testDeleteZjfs_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zjfsService.deleteZjfs(id), ZJFS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZjfsPage() {
       // mock 数据
       ZjfsDO dbZjfs = randomPojo(ZjfsDO.class, o -> { // 等会查询到
           o.setZcfl(null);
           o.setZjnx(null);
           o.setCzl(null);
           o.setSfdyzj(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zjfsMapper.insert(dbZjfs);
       // 测试 zcfl 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setZcfl(null)));
       // 测试 zjnx 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setZjnx(null)));
       // 测试 czl 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setCzl(null)));
       // 测试 sfdyzj 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setSfdyzj(null)));
       // 测试 qybs 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setCreateTime(null)));
       // 准备参数
       ZjfsPageReqVO reqVO = new ZjfsPageReqVO();
       reqVO.setZcfl(null);
       reqVO.setZjnx(null);
       reqVO.setCzl(null);
       reqVO.setSfdyzj(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZjfsDO> pageResult = zjfsService.getZjfsPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZjfs, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZjfsList() {
       // mock 数据
       ZjfsDO dbZjfs = randomPojo(ZjfsDO.class, o -> { // 等会查询到
           o.setZcfl(null);
           o.setZjnx(null);
           o.setCzl(null);
           o.setSfdyzj(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zjfsMapper.insert(dbZjfs);
       // 测试 zcfl 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setZcfl(null)));
       // 测试 zjnx 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setZjnx(null)));
       // 测试 czl 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setCzl(null)));
       // 测试 sfdyzj 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setSfdyzj(null)));
       // 测试 qybs 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zjfsMapper.insert(cloneIgnoreId(dbZjfs, o -> o.setCreateTime(null)));
       // 准备参数
       ZjfsExportReqVO reqVO = new ZjfsExportReqVO();
       reqVO.setZcfl(null);
       reqVO.setZjnx(null);
       reqVO.setCzl(null);
       reqVO.setSfdyzj(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZjfsDO> list = zjfsService.getZjfsList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZjfs, list.get(0));
    }

}
