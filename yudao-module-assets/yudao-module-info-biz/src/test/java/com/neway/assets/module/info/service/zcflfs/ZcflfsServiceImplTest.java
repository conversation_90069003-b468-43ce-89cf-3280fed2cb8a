package com.neway.assets.module.info.service.zcflfs;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.ZcflfsCreateReqVO;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.ZcflfsExportReqVO;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.ZcflfsPageReqVO;
import com.neway.assets.module.info.controller.admin.zcflfs.vo.ZcflfsUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.zcflfs.ZcflfsDO;
import com.neway.assets.module.info.dal.mysql.zcflfs.ZcflfsMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomInteger;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZCFLFS_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcflfsServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcflfsServiceImpl.class)
public class ZcflfsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcflfsServiceImpl zcflfsService;

    @Resource
    private ZcflfsMapper zcflfsMapper;

    @Test
    public void testCreateZcflfs_success() {
        // 准备参数
        ZcflfsCreateReqVO reqVO = randomPojo(ZcflfsCreateReqVO.class);

        // 调用
        Integer zcflfsId = zcflfsService.createZcflfs(reqVO);
        // 断言
        assertNotNull(zcflfsId);
        // 校验记录的属性是否正确
        ZcflfsDO zcflfs = zcflfsMapper.selectById(zcflfsId);
        assertPojoEquals(reqVO, zcflfs);
    }

    @Test
    public void testUpdateZcflfs_success() {
        // mock 数据
        ZcflfsDO dbZcflfs = randomPojo(ZcflfsDO.class);
        zcflfsMapper.insert(dbZcflfs);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcflfsUpdateReqVO reqVO = randomPojo(ZcflfsUpdateReqVO.class, o -> {
            o.setId(dbZcflfs.getId()); // 设置更新的 ID
        });

        // 调用
        zcflfsService.updateZcflfs(reqVO);
        // 校验是否更新正确
        ZcflfsDO zcflfs = zcflfsMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcflfs);
    }

    @Test
    public void testUpdateZcflfs_notExists() {
        // 准备参数
        ZcflfsUpdateReqVO reqVO = randomPojo(ZcflfsUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcflfsService.updateZcflfs(reqVO), ZCFLFS_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcflfs_success() {
        // mock 数据
        ZcflfsDO dbZcflfs = randomPojo(ZcflfsDO.class);
        zcflfsMapper.insert(dbZcflfs);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Integer id = dbZcflfs.getId();

        // 调用
        zcflfsService.deleteZcflfs(id);
       // 校验数据不存在了
       assertNull(zcflfsMapper.selectById(id));
    }

    @Test
    public void testDeleteZcflfs_notExists() {
        // 准备参数
        Integer id = randomInteger();

        // 调用, 并断言异常
        assertServiceException(() -> zcflfsService.deleteZcflfs(id), ZCFLFS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcflfsPage() {
       // mock 数据
       ZcflfsDO dbZcflfs = randomPojo(ZcflfsDO.class, o -> { // 等会查询到
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcflfsMapper.insert(dbZcflfs);
       // 测试 qybs 不匹配
       zcflfsMapper.insert(cloneIgnoreId(dbZcflfs, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcflfsMapper.insert(cloneIgnoreId(dbZcflfs, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcflfsMapper.insert(cloneIgnoreId(dbZcflfs, o -> o.setCreateTime(null)));
       // 准备参数
       ZcflfsPageReqVO reqVO = new ZcflfsPageReqVO();
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcflfsDO> pageResult = zcflfsService.getZcflfsPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcflfs, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcflfsList() {
       // mock 数据
       ZcflfsDO dbZcflfs = randomPojo(ZcflfsDO.class, o -> { // 等会查询到
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcflfsMapper.insert(dbZcflfs);
       // 测试 qybs 不匹配
       zcflfsMapper.insert(cloneIgnoreId(dbZcflfs, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcflfsMapper.insert(cloneIgnoreId(dbZcflfs, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcflfsMapper.insert(cloneIgnoreId(dbZcflfs, o -> o.setCreateTime(null)));
       // 准备参数
       ZcflfsExportReqVO reqVO = new ZcflfsExportReqVO();
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcflfsDO> list = zcflfsService.getZcflfsList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcflfs, list.get(0));
    }

}
