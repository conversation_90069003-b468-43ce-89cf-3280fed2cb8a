package com.neway.assets.module.info.service.secondzcfl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflCreateReqVO;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflExportReqVO;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflPageReqVO;
import com.neway.assets.module.info.controller.admin.secondzcfl.vo.SecondZcflUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.secondzcfl.SecondZcflDO;
import com.neway.assets.module.info.dal.mysql.secondzcfl.SecondZcflMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.SECOND_ZCFL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link SecondZcflServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(SecondZcflServiceImpl.class)
public class SecondZcflServiceImplTest extends BaseDbUnitTest {

    @Resource
    private SecondZcflServiceImpl secondZcflService;

    @Resource
    private SecondZcflMapper secondZcflMapper;

    @Test
    public void testCreateSecondZcfl_success() {
        // 准备参数
        SecondZcflCreateReqVO reqVO = randomPojo(SecondZcflCreateReqVO.class);

        // 调用
        Long secondZcflId = secondZcflService.createSecondZcfl(reqVO);
        // 断言
        assertNotNull(secondZcflId);
        // 校验记录的属性是否正确
        SecondZcflDO secondZcfl = secondZcflMapper.selectById(secondZcflId);
        assertPojoEquals(reqVO, secondZcfl);
    }

    @Test
    public void testUpdateSecondZcfl_success() {
        // mock 数据
        SecondZcflDO dbSecondZcfl = randomPojo(SecondZcflDO.class);
        secondZcflMapper.insert(dbSecondZcfl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        SecondZcflUpdateReqVO reqVO = randomPojo(SecondZcflUpdateReqVO.class, o -> {
            o.setId(dbSecondZcfl.getId()); // 设置更新的 ID
        });

        // 调用
        secondZcflService.updateSecondZcfl(reqVO);
        // 校验是否更新正确
        SecondZcflDO secondZcfl = secondZcflMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, secondZcfl);
    }

    @Test
    public void testUpdateSecondZcfl_notExists() {
        // 准备参数
        SecondZcflUpdateReqVO reqVO = randomPojo(SecondZcflUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> secondZcflService.updateSecondZcfl(reqVO), SECOND_ZCFL_NOT_EXISTS);
    }

    @Test
    public void testDeleteSecondZcfl_success() {
        // mock 数据
        SecondZcflDO dbSecondZcfl = randomPojo(SecondZcflDO.class);
        secondZcflMapper.insert(dbSecondZcfl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbSecondZcfl.getId();

        // 调用
        secondZcflService.deleteSecondZcfl(id);
       // 校验数据不存在了
       assertNull(secondZcflMapper.selectById(id));
    }

    @Test
    public void testDeleteSecondZcfl_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> secondZcflService.deleteSecondZcfl(id), SECOND_ZCFL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetSecondZcflPage() {
       // mock 数据
       SecondZcflDO dbSecondZcfl = randomPojo(SecondZcflDO.class, o -> { // 等会查询到
           o.setZcflfsId(null);
           o.setZcflms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       secondZcflMapper.insert(dbSecondZcfl);
       // 测试 zcflfsId 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setZcflfsId(null)));
       // 测试 zcflms 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setZcflms(null)));
       // 测试 qybs 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setCreateTime(null)));
       // 准备参数
       SecondZcflPageReqVO reqVO = new SecondZcflPageReqVO();
       reqVO.setZcflfsId(null);
       reqVO.setZcflms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<SecondZcflDO> pageResult = secondZcflService.getSecondZcflPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbSecondZcfl, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetSecondZcflList() {
       // mock 数据
       SecondZcflDO dbSecondZcfl = randomPojo(SecondZcflDO.class, o -> { // 等会查询到
           o.setZcflfsId(null);
           o.setZcflms(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       secondZcflMapper.insert(dbSecondZcfl);
       // 测试 zcflfsId 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setZcflfsId(null)));
       // 测试 zcflms 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setZcflms(null)));
       // 测试 qybs 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       secondZcflMapper.insert(cloneIgnoreId(dbSecondZcfl, o -> o.setCreateTime(null)));
       // 准备参数
       SecondZcflExportReqVO reqVO = new SecondZcflExportReqVO();
       reqVO.setZcflfsId(null);
       reqVO.setZcflms(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<SecondZcflDO> list = secondZcflService.getSecondZcflList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbSecondZcfl, list.get(0));
    }

}
