package com.neway.assets.module.info.service.zcflzgy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyCreateReqVO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyExportReqVO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyPageReqVO;
import com.neway.assets.module.info.controller.admin.zcflzgy.vo.ZcflZgyUpdateReqVO;
import com.neway.assets.module.info.dal.dataobject.zcflzgy.ZcflZgyDO;
import com.neway.assets.module.info.dal.mysql.zcflzgy.ZcflZgyMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.info.enums.ErrorCodeConstants.ZCFL_ZGY_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcflZgyServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcflZgyServiceImpl.class)
public class ZcflZgyServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcflZgyServiceImpl zcflZgyService;

    @Resource
    private ZcflZgyMapper zcflZgyMapper;

    @Test
    public void testCreateZcflZgy_success() {
        // 准备参数
        ZcflZgyCreateReqVO reqVO = randomPojo(ZcflZgyCreateReqVO.class);

        // 调用
        Long zcflZgyId = zcflZgyService.createZcflZgy(reqVO);
        // 断言
        assertNotNull(zcflZgyId);
        // 校验记录的属性是否正确
        ZcflZgyDO zcflZgy = zcflZgyMapper.selectById(zcflZgyId);
        assertPojoEquals(reqVO, zcflZgy);
    }

    @Test
    public void testUpdateZcflZgy_success() {
        // mock 数据
        ZcflZgyDO dbZcflZgy = randomPojo(ZcflZgyDO.class);
        zcflZgyMapper.insert(dbZcflZgy);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcflZgyUpdateReqVO reqVO = randomPojo(ZcflZgyUpdateReqVO.class, o -> {
            o.setId(dbZcflZgy.getId()); // 设置更新的 ID
        });

        // 调用
        zcflZgyService.updateZcflZgy(reqVO);
        // 校验是否更新正确
        ZcflZgyDO zcflZgy = zcflZgyMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcflZgy);
    }

    @Test
    public void testUpdateZcflZgy_notExists() {
        // 准备参数
        ZcflZgyUpdateReqVO reqVO = randomPojo(ZcflZgyUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcflZgyService.updateZcflZgy(reqVO), ZCFL_ZGY_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcflZgy_success() {
        // mock 数据
        ZcflZgyDO dbZcflZgy = randomPojo(ZcflZgyDO.class);
        zcflZgyMapper.insert(dbZcflZgy);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcflZgy.getId();

        // 调用
        zcflZgyService.deleteZcflZgy(id);
       // 校验数据不存在了
       assertNull(zcflZgyMapper.selectById(id));
    }

    @Test
    public void testDeleteZcflZgy_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcflZgyService.deleteZcflZgy(id), ZCFL_ZGY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcflZgyPage() {
       // mock 数据
       ZcflZgyDO dbZcflZgy = randomPojo(ZcflZgyDO.class, o -> { // 等会查询到
           o.setGs(null);
           o.setZcflId(null);
           o.setUserId(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcflZgyMapper.insert(dbZcflZgy);
       // 测试 gs 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setGs(null)));
       // 测试 zcflId 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setZcflId(null)));
       // 测试 userId 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setUserId(null)));
       // 测试 qybs 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setCreateTime(null)));
       // 准备参数
       ZcflZgyPageReqVO reqVO = new ZcflZgyPageReqVO();
       reqVO.setGs(null);
       reqVO.setZcflId(null);
       reqVO.setUserId(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcflZgyDO> pageResult = zcflZgyService.getZcflZgyPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcflZgy, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcflZgyList() {
       // mock 数据
       ZcflZgyDO dbZcflZgy = randomPojo(ZcflZgyDO.class, o -> { // 等会查询到
           o.setGs(null);
           o.setZcflId(null);
           o.setUserId(null);
           o.setQybs(null);
           o.setBz(null);
           o.setCreateTime(null);
       });
       zcflZgyMapper.insert(dbZcflZgy);
       // 测试 gs 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setGs(null)));
       // 测试 zcflId 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setZcflId(null)));
       // 测试 userId 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setUserId(null)));
       // 测试 qybs 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setQybs(null)));
       // 测试 bz 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setBz(null)));
       // 测试 createTime 不匹配
       zcflZgyMapper.insert(cloneIgnoreId(dbZcflZgy, o -> o.setCreateTime(null)));
       // 准备参数
       ZcflZgyExportReqVO reqVO = new ZcflZgyExportReqVO();
       reqVO.setGs(null);
       reqVO.setZcflId(null);
       reqVO.setUserId(null);
       reqVO.setQybs(null);
       reqVO.setBz(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcflZgyDO> list = zcflZgyService.getZcflZgyList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcflZgy, list.get(0));
    }

}
