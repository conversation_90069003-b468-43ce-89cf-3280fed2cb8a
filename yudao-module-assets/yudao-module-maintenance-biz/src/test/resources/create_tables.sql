CREATE TABLE IF NOT EXISTS "op_zcwxjl" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "zclb" int NOT NULL,
    "zcxxid" bigint NOT NULL,
    "whfs" int,
    "jhksrq" varchar,
    "jhjzrq" varchar,
    "sjksrq" varchar,
    "sjjsrq" varchar,
    "wxyy" bigint,
    "wxfan" varchar,
    "wxjg" varchar,
    "wxqpz" varchar,
    "wxhpz" varchar,
    "wxzt" int,
    "bz" varchar,
    "process_instance_id" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '资产维修保养记录表';


