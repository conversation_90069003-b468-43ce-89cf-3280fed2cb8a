package com.neway.assets.module.maintenance.service.zcwxjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo.*;
import com.neway.assets.module.maintenance.dal.dataobject.zcwxjl.ZcwxjlDO;
import com.neway.assets.module.maintenance.dal.mysql.zcwxjl.ZcwxjlMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.maintenance.enums.ErrorCodeConstants.ZCWXJL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ZcwxjlServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ZcwxjlServiceImpl.class)
public class ZcwxjlServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ZcwxjlServiceImpl zcwxjlService;

    @Resource
    private ZcwxjlMapper zcwxjlMapper;

    @Test
    public void testCreateZcwxjl_success() {
        // 准备参数
        ZcwxjlCreateReqVO reqVO = randomPojo(ZcwxjlCreateReqVO.class);

        // 调用
        Long zcwxjlId = zcwxjlService.createZcwxjl(reqVO);
        // 断言
        assertNotNull(zcwxjlId);
        // 校验记录的属性是否正确
        ZcwxjlDO zcwxjl = zcwxjlMapper.selectById(zcwxjlId);
        assertPojoEquals(reqVO, zcwxjl);
    }

    @Test
    public void testUpdateZcwxjl_success() {
        // mock 数据
        ZcwxjlDO dbZcwxjl = randomPojo(ZcwxjlDO.class);
        zcwxjlMapper.insert(dbZcwxjl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ZcwxjlUpdateReqVO reqVO = randomPojo(ZcwxjlUpdateReqVO.class, o -> {
            o.setId(dbZcwxjl.getId()); // 设置更新的 ID
        });

        // 调用
        zcwxjlService.updateZcwxjl(reqVO);
        // 校验是否更新正确
        ZcwxjlDO zcwxjl = zcwxjlMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, zcwxjl);
    }

    @Test
    public void testUpdateZcwxjl_notExists() {
        // 准备参数
        ZcwxjlUpdateReqVO reqVO = randomPojo(ZcwxjlUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> zcwxjlService.updateZcwxjl(reqVO), ZCWXJL_NOT_EXISTS);
    }

    @Test
    public void testDeleteZcwxjl_success() {
        // mock 数据
        ZcwxjlDO dbZcwxjl = randomPojo(ZcwxjlDO.class);
        zcwxjlMapper.insert(dbZcwxjl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbZcwxjl.getId();

        // 调用
        zcwxjlService.deleteZcwxjl(id);
       // 校验数据不存在了
       assertNull(zcwxjlMapper.selectById(id));
    }

    @Test
    public void testDeleteZcwxjl_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> zcwxjlService.deleteZcwxjl(id), ZCWXJL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcwxjlPage() {
       // mock 数据
       ZcwxjlDO dbZcwxjl = randomPojo(ZcwxjlDO.class, o -> { // 等会查询到
           o.setZclb(null);
           o.setZcxxid(null);
           o.setWhfs(null);
           o.setJhksrq(null);
           o.setJhjzrq(null);
           o.setSjksrq(null);
           o.setSjjsrq(null);
           o.setWxyy(null);
           o.setWxfan(null);
           o.setWxjg(null);
           o.setWxqpz(null);
           o.setWxhpz(null);
           o.setWxzt(null);
           o.setBz(null);
           o.setProcessInstanceId(null);
           o.setCreateTime(null);
       });
       zcwxjlMapper.insert(dbZcwxjl);
       // 测试 zclb 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setZclb(null)));
       // 测试 zcxxid 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setZcxxid(null)));
       // 测试 whfs 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWhfs(null)));
       // 测试 jhksrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setJhksrq(null)));
       // 测试 jhjzrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setJhjzrq(null)));
       // 测试 sjksrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setSjksrq(null)));
       // 测试 sjjsrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setSjjsrq(null)));
       // 测试 wxyy 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxyy(null)));
       // 测试 wxfan 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxfan(null)));
       // 测试 wxjg 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxjg(null)));
       // 测试 wxqpz 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxqpz(null)));
       // 测试 wxhpz 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxhpz(null)));
       // 测试 wxzt 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxzt(null)));
       // 测试 bz 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setBz(null)));
       // 测试 processInstanceId 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setProcessInstanceId(null)));
       // 测试 createTime 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setCreateTime(null)));
       // 准备参数
       ZcwxjlPageReqVO reqVO = new ZcwxjlPageReqVO();
       reqVO.setZclb(null);
       reqVO.setZcxxid(null);
       reqVO.setWhfs(null);
       reqVO.setJhksrq(null);
       reqVO.setJhjzrq(null);
       reqVO.setSjksrq(null);
       reqVO.setSjjsrq(null);
       reqVO.setWxyy(null);
       reqVO.setWxfan(null);
       reqVO.setWxjg(null);
       reqVO.setWxqpz(null);
       reqVO.setWxhpz(null);
       reqVO.setWxzt(null);
       reqVO.setBz(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ZcwxjlPageItemRespVO> pageResult = zcwxjlService.getZcwxjlPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbZcwxjl, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetZcwxjlList() {
       // mock 数据
       ZcwxjlDO dbZcwxjl = randomPojo(ZcwxjlDO.class, o -> { // 等会查询到
           o.setZclb(null);
           o.setZcxxid(null);
           o.setWhfs(null);
           o.setJhksrq(null);
           o.setJhjzrq(null);
           o.setSjksrq(null);
           o.setSjjsrq(null);
           o.setWxyy(null);
           o.setWxfan(null);
           o.setWxjg(null);
           o.setWxqpz(null);
           o.setWxhpz(null);
           o.setWxzt(null);
           o.setBz(null);
           o.setProcessInstanceId(null);
           o.setCreateTime(null);
       });
       zcwxjlMapper.insert(dbZcwxjl);
       // 测试 zclb 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setZclb(null)));
       // 测试 zcxxid 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setZcxxid(null)));
       // 测试 whfs 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWhfs(null)));
       // 测试 jhksrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setJhksrq(null)));
       // 测试 jhjzrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setJhjzrq(null)));
       // 测试 sjksrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setSjksrq(null)));
       // 测试 sjjsrq 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setSjjsrq(null)));
       // 测试 wxyy 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxyy(null)));
       // 测试 wxfan 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxfan(null)));
       // 测试 wxjg 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxjg(null)));
       // 测试 wxqpz 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxqpz(null)));
       // 测试 wxhpz 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxhpz(null)));
       // 测试 wxzt 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setWxzt(null)));
       // 测试 bz 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setBz(null)));
       // 测试 processInstanceId 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setProcessInstanceId(null)));
       // 测试 createTime 不匹配
       zcwxjlMapper.insert(cloneIgnoreId(dbZcwxjl, o -> o.setCreateTime(null)));
       // 准备参数
       ZcwxjlExportReqVO reqVO = new ZcwxjlExportReqVO();
       reqVO.setZclb(null);
       reqVO.setZcxxid(null);
       reqVO.setWhfs(null);
       reqVO.setJhksrq(null);
       reqVO.setJhjzrq(null);
       reqVO.setSjksrq(null);
       reqVO.setSjjsrq(null);
       reqVO.setWxyy(null);
       reqVO.setWxfan(null);
       reqVO.setWxjg(null);
       reqVO.setWxqpz(null);
       reqVO.setWxhpz(null);
       reqVO.setWxzt(null);
       reqVO.setBz(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ZcwxjlDO> list = zcwxjlService.getZcwxjlList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbZcwxjl, list.get(0));
    }

}
