package com.neway.assets.module.maintenance.service.sbybjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlCreateReqVO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlExportReqVO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlPageReqVO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlUpdateReqVO;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import com.neway.assets.module.maintenance.dal.mysql.sbybjl.SbybjlMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.maintenance.enums.ErrorCodeConstants.SBYBJL_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link SbybjlServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(SbybjlServiceImpl.class)
public class SbybjlServiceImplTest extends BaseDbUnitTest {

    @Resource
    private SbybjlServiceImpl sbybjlService;

    @Resource
    private SbybjlMapper sbybjlMapper;

    @Test
    public void testCreateSbybjl_success() {
        // 准备参数
        SbybjlCreateReqVO reqVO = randomPojo(SbybjlCreateReqVO.class);

        // 调用
        Long sbybjlId = sbybjlService.createSbybjl(reqVO);
        // 断言
        assertNotNull(sbybjlId);
        // 校验记录的属性是否正确
        SbybjlDO sbybjl = sbybjlMapper.selectById(sbybjlId);
        assertPojoEquals(reqVO, sbybjl);
    }

    @Test
    public void testUpdateSbybjl_success() {
        // mock 数据
        SbybjlDO dbSbybjl = randomPojo(SbybjlDO.class);
        sbybjlMapper.insert(dbSbybjl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        SbybjlUpdateReqVO reqVO = randomPojo(SbybjlUpdateReqVO.class, o -> {
            o.setId(dbSbybjl.getId()); // 设置更新的 ID
        });

        // 调用
        sbybjlService.updateSbybjl(reqVO);
        // 校验是否更新正确
        SbybjlDO sbybjl = sbybjlMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, sbybjl);
    }

    @Test
    public void testUpdateSbybjl_notExists() {
        // 准备参数
        SbybjlUpdateReqVO reqVO = randomPojo(SbybjlUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> sbybjlService.updateSbybjl(reqVO), SBYBJL_NOT_EXISTS);
    }

    @Test
    public void testDeleteSbybjl_success() {
        // mock 数据
        SbybjlDO dbSbybjl = randomPojo(SbybjlDO.class);
        sbybjlMapper.insert(dbSbybjl);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbSbybjl.getId();

        // 调用
        sbybjlService.deleteSbybjl(id);
       // 校验数据不存在了
       assertNull(sbybjlMapper.selectById(id));
    }

    @Test
    public void testDeleteSbybjl_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> sbybjlService.deleteSbybjl(id), SBYBJL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetSbybjlPage() {
       // mock 数据
       SbybjlDO dbSbybjl = randomPojo(SbybjlDO.class, o -> { // 等会查询到
           o.setZcid(null);
           o.setZbdqr(null);
           o.setGysid(null);
           o.setHtxy(null);
           o.setWbfy(null);
           o.setCreateTime(null);
       });
       sbybjlMapper.insert(dbSbybjl);
       // 测试 zcid 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setZcid(null)));
       // 测试 zbdqr 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setZbdqr(null)));
       // 测试 gysid 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setGysid(null)));
       // 测试 htxy 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setHtxy(null)));
       // 测试 wbfy 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setWbfy(null)));
       // 测试 createTime 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setCreateTime(null)));
       // 准备参数
       SbybjlPageReqVO reqVO = new SbybjlPageReqVO();
       reqVO.setZcid(null);
       reqVO.setZbdqr(null);
       reqVO.setGysid(null);
       reqVO.setHtxy(null);
       reqVO.setWbfy(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<SbybjlDO> pageResult = sbybjlService.getSbybjlPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbSbybjl, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetSbybjlList() {
       // mock 数据
       SbybjlDO dbSbybjl = randomPojo(SbybjlDO.class, o -> { // 等会查询到
           o.setZcid(null);
           o.setZbdqr(null);
           o.setGysid(null);
           o.setHtxy(null);
           o.setWbfy(null);
           o.setCreateTime(null);
       });
       sbybjlMapper.insert(dbSbybjl);
       // 测试 zcid 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setZcid(null)));
       // 测试 zbdqr 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setZbdqr(null)));
       // 测试 gysid 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setGysid(null)));
       // 测试 htxy 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setHtxy(null)));
       // 测试 wbfy 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setWbfy(null)));
       // 测试 createTime 不匹配
       sbybjlMapper.insert(cloneIgnoreId(dbSbybjl, o -> o.setCreateTime(null)));
       // 准备参数
       SbybjlExportReqVO reqVO = new SbybjlExportReqVO();
       reqVO.setZcid(null);
       reqVO.setZbdqr(null);
       reqVO.setGysid(null);
       reqVO.setHtxy(null);
       reqVO.setWbfy(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<SbybjlDO> list = sbybjlService.getSbybjlList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbSbybjl, list.get(0));
    }

}
