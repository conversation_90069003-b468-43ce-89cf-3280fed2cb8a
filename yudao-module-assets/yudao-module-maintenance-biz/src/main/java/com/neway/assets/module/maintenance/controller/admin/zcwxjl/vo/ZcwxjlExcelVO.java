package com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 资产维修保养记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ZcwxjlExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty(value = "资产类别", converter = DictConvert.class)
    @DictFormat("assets_class") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer zclb;

    @ExcelProperty("资产id")
    private Long zcxxid;

    @ExcelProperty(value = "维护方式", converter = DictConvert.class)
    @DictFormat("assets_maintenance_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer whfs;

    @ExcelProperty("计划开始日期")
    private LocalDate jhksrq;

    @ExcelProperty("计划截至日期")
    private LocalDate jhjzrq;

    @ExcelProperty("实际开始日期")
    private LocalDate sjksrq;

    @ExcelProperty("实际结束日期")
    private LocalDate sjjsrq;

    @ExcelProperty("维护/维修原因（bs_wxyy->id）")
    private Long wxyy;

    @ExcelProperty("维护/维修方案")
    private String wxfan;

    @ExcelProperty("维护/维修结果")
    private String wxjg;

    @ExcelProperty("维护/维修前拍照（存储路径）")
    private String wxqpz;

    @ExcelProperty("维护/维修后拍照（存储路径）")
    private String wxhpz;

    @ExcelProperty("维护/维修状态")
    private Integer wxzt;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("流程编号")
    private String processInstanceId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
