package com.neway.assets.module.maintenance.service.sbybjl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.*;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.maintenance.convert.sbybjl.SbybjlConvert;
import com.neway.assets.module.maintenance.dal.mysql.sbybjl.SbybjlMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.maintenance.enums.ErrorCodeConstants.*;

/**
 * 资产质保记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SbybjlServiceImpl implements SbybjlService {

    @Resource
    private SbybjlMapper sbybjlMapper;

    @Override
    public Long createSbybjl(SbybjlCreateReqVO createReqVO) {
        // 插入
        SbybjlDO sbybjl = SbybjlConvert.INSTANCE.convert(createReqVO);
        sbybjlMapper.insert(sbybjl);
        // 返回
        return sbybjl.getId();
    }

    @Override
    public void updateSbybjl(SbybjlUpdateReqVO updateReqVO) {
        // 校验存在
        validateSbybjlExists(updateReqVO.getId());
        // 更新
        SbybjlDO updateObj = SbybjlConvert.INSTANCE.convert(updateReqVO);
        sbybjlMapper.updateById(updateObj);
    }

    @Override
    public void deleteSbybjl(Long id) {
        // 校验存在
        validateSbybjlExists(id);
        // 删除
        sbybjlMapper.deleteById(id);
    }

    private void validateSbybjlExists(Long id) {
        if (sbybjlMapper.selectById(id) == null) {
            throw exception(SBYBJL_NOT_EXISTS);
        }
    }

    @Override
    public SbybjlDO getSbybjl(Long id) {
        return sbybjlMapper.selectById(id);
    }

    @Override
    public List<SbybjlDO> getSbybjlList(Collection<Long> ids) {
        return sbybjlMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SbybjlDO> getSbybjlPage(SbybjlPageReqVO pageReqVO) {
        return sbybjlMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SbybjlDO> getSbybjlList(SbybjlExportReqVO exportReqVO) {
        return sbybjlMapper.selectList(exportReqVO);
    }

}
