package com.neway.assets.module.maintenance.controller.admin.sbybjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
* 资产质保记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SbybjlBaseVO {

    @Schema(description = "主资产&子资产标识")
    private Integer zzbs;

    @Schema(description = "资产id", example = "24519")
    private Long zcid;

    @Schema(description = "质保开始日期")
    private LocalDate zbksr;

    @Schema(description = "质保到期日")
    private LocalDate zbdqr;

    @Schema(description = "维保供应商", example = "15775")
    private Long gysid;

    @Schema(description = "供应商合同&协议")
    private String htxy;

    @Schema(description = "维保费用")
    private BigDecimal wbfy;

}
