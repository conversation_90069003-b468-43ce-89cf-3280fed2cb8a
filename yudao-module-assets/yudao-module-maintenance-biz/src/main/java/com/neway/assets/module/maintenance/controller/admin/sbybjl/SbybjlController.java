package com.neway.assets.module.maintenance.controller.admin.sbybjl;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.*;
import com.neway.assets.module.maintenance.convert.sbybjl.SbybjlConvert;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import com.neway.assets.module.maintenance.service.sbybjl.SbybjlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资产质保记录")
@RestController
@RequestMapping("/maintenance/sbybjl")
@Validated
public class SbybjlController {

    @Resource
    private SbybjlService sbybjlService;

    @PostMapping("/create")
    @Operation(summary = "创建资产质保记录")
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:create')")
    public CommonResult<Long> createSbybjl(@Valid @RequestBody SbybjlCreateReqVO createReqVO) {
        return success(sbybjlService.createSbybjl(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产质保记录")
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:update')")
    public CommonResult<Boolean> updateSbybjl(@Valid @RequestBody SbybjlUpdateReqVO updateReqVO) {
        sbybjlService.updateSbybjl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产质保记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:delete')")
    public CommonResult<Boolean> deleteSbybjl(@RequestParam("id") Long id) {
        sbybjlService.deleteSbybjl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产质保记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:query')")
    public CommonResult<SbybjlRespVO> getSbybjl(@RequestParam("id") Long id) {
        SbybjlDO sbybjl = sbybjlService.getSbybjl(id);
        return success(SbybjlConvert.INSTANCE.convert(sbybjl));
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产质保记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:query')")
    public CommonResult<List<SbybjlRespVO>> getSbybjlList(@RequestParam("ids") Collection<Long> ids) {
        List<SbybjlDO> list = sbybjlService.getSbybjlList(ids);
        return success(SbybjlConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产质保记录分页")
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:query')")
    public CommonResult<PageResult<SbybjlRespVO>> getSbybjlPage(@Valid SbybjlPageReqVO pageVO) {
        PageResult<SbybjlDO> pageResult = sbybjlService.getSbybjlPage(pageVO);
        return success(SbybjlConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产质保记录 Excel")
    @PreAuthorize("@ss.hasPermission('maintenance:sbybjl:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSbybjlExcel(@Valid SbybjlExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SbybjlDO> list = sbybjlService.getSbybjlList(exportReqVO);
        // 导出 Excel
        List<SbybjlExcelVO> datas = SbybjlConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产质保记录.xls", "数据", SbybjlExcelVO.class, datas);
    }

}
