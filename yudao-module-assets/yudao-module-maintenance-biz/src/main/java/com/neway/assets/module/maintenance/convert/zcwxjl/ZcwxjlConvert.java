package com.neway.assets.module.maintenance.convert.zcwxjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo.*;
import com.neway.assets.module.maintenance.dal.dataobject.zcwxjl.ZcwxjlDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资产维修保养记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcwxjlConvert {

    ZcwxjlConvert INSTANCE = Mappers.getMapper(ZcwxjlConvert.class);

    ZcwxjlDO convert(ZcwxjlCreateReqVO bean);

    ZcwxjlDO convert(ZcwxjlUpdateReqVO bean);

    @Named("one")
    ZcwxjlRespVO convert(ZcwxjlDO bean);

    @Named("two")
    ZcwxjlPageItemRespVO convert2(ZcwxjlDO bean);

    @IterableMapping(qualifiedByName = "one")
    List<ZcwxjlRespVO> convertList(List<ZcwxjlDO> list);

    PageResult<ZcwxjlRespVO> convertPage(PageResult<ZcwxjlDO> page);

    List<ZcwxjlExcelVO> convertList02(List<ZcwxjlDO> list);

}
