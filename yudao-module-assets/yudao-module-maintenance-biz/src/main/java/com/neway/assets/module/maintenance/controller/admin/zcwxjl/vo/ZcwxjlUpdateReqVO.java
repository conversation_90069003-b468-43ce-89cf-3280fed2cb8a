package com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 资产维修保养记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcwxjlUpdateReqVO extends ZcwxjlBaseVO {

    @Schema(description = "自增长id", required = true, example = "10423")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
