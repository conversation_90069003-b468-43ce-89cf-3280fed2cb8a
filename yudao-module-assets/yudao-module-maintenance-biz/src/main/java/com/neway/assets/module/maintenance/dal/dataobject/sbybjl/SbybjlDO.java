package com.neway.assets.module.maintenance.dal.dataobject.sbybjl;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 资产质保记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "op_sbybjl", schema = "gdzc")
@KeySequence("op_sbybjl_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SbybjlDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 主资产&子资产标识
     */
    private Integer zzbs;
    /**
     * 资产id
     */
    private Long zcid;
    /**
     * 质保开始日
     */
    private LocalDate zbksr;
    /**
     * 质保到期日
     */
    private LocalDate zbdqr;
    /**
     * 维保供应商
     */
    private Long gysid;
    /**
     * 供应商合同&协议
     */
    private String htxy;
    /**
     * 维保费用
     */
    private BigDecimal wbfy;

}
