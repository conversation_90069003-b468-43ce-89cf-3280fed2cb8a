package com.neway.assets.module.maintenance.dal.mysql.zcwxjl;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.maintenance.dal.dataobject.zcwxjl.ZcwxjlDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo.*;

/**
 * 资产维修保养记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcwxjlMapper extends BaseMapperX<ZcwxjlDO> {

    default PageResult<ZcwxjlDO> selectPage(ZcwxjlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcwxjlDO>()
                .eqIfPresent(ZcwxjlDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcwxjlDO::getZcxxid, reqVO.getZcxxid())
                .eqIfPresent(ZcwxjlDO::getWhfs, reqVO.getWhfs())
                .eqIfPresent(ZcwxjlDO::getJhksrq, reqVO.getJhksrq())
                .eqIfPresent(ZcwxjlDO::getJhjzrq, reqVO.getJhjzrq())
                .eqIfPresent(ZcwxjlDO::getSjksrq, reqVO.getSjksrq())
                .eqIfPresent(ZcwxjlDO::getSjjsrq, reqVO.getSjjsrq())
                .eqIfPresent(ZcwxjlDO::getWxyy, reqVO.getWxyy())
                .eqIfPresent(ZcwxjlDO::getWxfan, reqVO.getWxfan())
                .eqIfPresent(ZcwxjlDO::getWxjg, reqVO.getWxjg())
                .eqIfPresent(ZcwxjlDO::getWxqpz, reqVO.getWxqpz())
                .eqIfPresent(ZcwxjlDO::getWxhpz, reqVO.getWxhpz())
                .eqIfPresent(ZcwxjlDO::getWxzt, reqVO.getWxzt())
                .eqIfPresent(ZcwxjlDO::getBz, reqVO.getBz())
                .eqIfPresent(ZcwxjlDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(ZcwxjlDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcwxjlDO::getId));
    }

    default List<ZcwxjlDO> selectList(ZcwxjlExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcwxjlDO>()
                .eqIfPresent(ZcwxjlDO::getZclb, reqVO.getZclb())
                .eqIfPresent(ZcwxjlDO::getZcxxid, reqVO.getZcxxid())
                .eqIfPresent(ZcwxjlDO::getWhfs, reqVO.getWhfs())
                .eqIfPresent(ZcwxjlDO::getJhksrq, reqVO.getJhksrq())
                .eqIfPresent(ZcwxjlDO::getJhjzrq, reqVO.getJhjzrq())
                .eqIfPresent(ZcwxjlDO::getSjksrq, reqVO.getSjksrq())
                .eqIfPresent(ZcwxjlDO::getSjjsrq, reqVO.getSjjsrq())
                .eqIfPresent(ZcwxjlDO::getWxyy, reqVO.getWxyy())
                .eqIfPresent(ZcwxjlDO::getWxfan, reqVO.getWxfan())
                .eqIfPresent(ZcwxjlDO::getWxjg, reqVO.getWxjg())
                .eqIfPresent(ZcwxjlDO::getWxqpz, reqVO.getWxqpz())
                .eqIfPresent(ZcwxjlDO::getWxhpz, reqVO.getWxhpz())
                .eqIfPresent(ZcwxjlDO::getWxzt, reqVO.getWxzt())
                .eqIfPresent(ZcwxjlDO::getBz, reqVO.getBz())
                .eqIfPresent(ZcwxjlDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(ZcwxjlDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ZcwxjlDO::getId));
    }

}
