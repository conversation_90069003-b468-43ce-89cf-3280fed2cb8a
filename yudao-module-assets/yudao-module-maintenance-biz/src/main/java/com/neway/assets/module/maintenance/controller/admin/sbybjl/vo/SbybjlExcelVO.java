package com.neway.assets.module.maintenance.controller.admin.sbybjl.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 资产质保记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SbybjlExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("资产id")
    private Long zcid;

    @ExcelProperty("质保开始日")
    private LocalDate zbksr;

    @ExcelProperty("质保到期日")
    private LocalDate zbdqr;

    @ExcelProperty("维保供应商")
    private Long gysid;

    @ExcelProperty("供应商合同&协议")
    private String htxy;

    @ExcelProperty("维保费用")
    private BigDecimal wbfy;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
