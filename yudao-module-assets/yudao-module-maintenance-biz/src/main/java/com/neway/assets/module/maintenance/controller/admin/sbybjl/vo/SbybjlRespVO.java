package com.neway.assets.module.maintenance.controller.admin.sbybjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产质保记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SbybjlRespVO extends SbybjlBaseVO {

    @Schema(description = "id", required = true, example = "13746")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
