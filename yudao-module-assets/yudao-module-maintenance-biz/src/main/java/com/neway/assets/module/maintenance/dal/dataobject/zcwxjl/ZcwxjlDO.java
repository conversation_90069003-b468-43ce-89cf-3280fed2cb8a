package com.neway.assets.module.maintenance.dal.dataobject.zcwxjl;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDate;

/**
 * 资产维修保养记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "op_zcwxjl", schema = "gdzc")
@KeySequence("op_zcwxjl_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZcwxjlDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 资产类别
     *
     * 枚举 {@link com.neway.assets.module.info.enums.enable.AssetsClassEnum}
     */
    private Integer zclb;
    /**
     * 资产id
     */
    private Long zcxxid;
    /**
     * 维护方式
     *
     * 枚举 {@link com.neway.assets.module.maintenance.enums.AssetsMaintenanceType}
     */
    private Integer whfs;
    /**
     * 计划开始日期
     */
    private LocalDate jhksrq;
    /**
     * 计划截至日期
     */
    private LocalDate jhjzrq;
    /**
     * 实际开始日期
     */
    private LocalDate sjksrq;
    /**
     * 实际结束日期
     */
    private LocalDate sjjsrq;
    /**
     * 维护/维修原因（bs_wxyy->id）
     */
    private Long wxyy;
    /**
     * 维护/维修方案
     */
    private String wxfan;
    /**
     * 维护/维修结果
     */
    private String wxjg;
    /**
     * 维护/维修前拍照（存储路径）
     */
    private String wxqpz;
    /**
     * 维护/维修后拍照（存储路径）
     */
    private String wxhpz;
    /**
     * 维护/维修状态
     */
    private Integer wxzt;
    /**
     * 备注
     */
    private String bz;
    /**
     * 流程编号
     */
    private String processInstanceId;

}
