package com.neway.assets.module.maintenance.convert.sbybjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlCreateReqDTO;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlRespDTO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlCreateReqVO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlExcelVO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlRespVO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlUpdateReqVO;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 资产质保记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SbybjlConvert {

    SbybjlConvert INSTANCE = Mappers.getMapper(SbybjlConvert.class);

    SbybjlDO convert(SbybjlCreateReqVO bean);

    SbybjlDO convert(SbybjlUpdateReqVO bean);

    SbybjlCreateReqVO convert(SbybjlCreateReqDTO bean);

    SbybjlRespVO convert(SbybjlDO bean);

    List<SbybjlRespVO> convertList(List<SbybjlDO> list);

    PageResult<SbybjlRespVO> convertPage(PageResult<SbybjlDO> page);

    List<SbybjlExcelVO> convertList02(List<SbybjlDO> list);

    List<SbybjlRespDTO> convertList03(List<SbybjlDO> list);

}
