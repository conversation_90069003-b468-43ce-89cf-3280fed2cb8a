package com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2023/5/25 14:38
 **/
@Schema(description = "管理后台 - 资产维修记录分页详细数据 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcwxjlPageItemRespVO extends ZcwxjlRespVO{

    @Schema(description = "资产名称", required = true)
    private String assetsName;
    @Schema(description = "维修原因", required = true)
    private String reason;
}
