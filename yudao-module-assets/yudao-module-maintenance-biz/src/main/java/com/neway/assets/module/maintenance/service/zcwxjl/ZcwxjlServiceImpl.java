package com.neway.assets.module.maintenance.service.zcwxjl;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.neway.assets.module.info.api.wxyy.WxyyApi;
import com.neway.assets.module.info.api.wxyy.dto.WxyyRespDTO;
import com.neway.assets.module.info.enums.enable.AssetsClassEnum;
import com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo.*;
import com.neway.assets.module.maintenance.convert.zcwxjl.ZcwxjlConvert;
import com.neway.assets.module.maintenance.dal.dataobject.zcwxjl.ZcwxjlDO;
import com.neway.assets.module.maintenance.dal.mysql.zcwxjl.ZcwxjlMapper;
import com.neway.assets.module.management.api.zczsj.ZczsjApi;
import com.neway.assets.module.management.api.zczsj.dto.ZczsjRespDTO;
import com.neway.assets.module.management.api.zzcxx.ZzcxxApi;
import com.neway.assets.module.management.api.zzcxx.dto.ZzcxxRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.ENTIRE;
import static com.neway.assets.module.info.enums.enable.AssetsClassEnum.PART;
import static com.neway.assets.module.maintenance.enums.ErrorCodeConstants.ZCWXJL_NOT_EXISTS;
import static com.neway.assets.module.management.enums.ErrorCodeConstants.ZCLB_NOT_EXISTS;

/**
 * 资产维修保养记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class ZcwxjlServiceImpl implements ZcwxjlService {

    /* 资产维修流程KEY */
    public static final String PROCESS_KEY = "ASSETS_MAINTAIN";

    private final ZcwxjlMapper zcwxjlMapper;

    private final ZczsjApi zczsjApi;
    private final ZzcxxApi zzcxxApi;
    private final WxyyApi wxyyApi;

    private final BpmProcessInstanceApi processInstanceApi;

    @Override
    public Long createZcwxjl(ZcwxjlCreateReqVO createReqVO) {
        // 插入
        ZcwxjlDO zcwxjl = ZcwxjlConvert.INSTANCE.convert(createReqVO);
        zcwxjlMapper.insert(zcwxjl);
        // 返回
        return zcwxjl.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createZcwxjl(Long userId, ZcwxjlCreateReqVO createReqVO) {
        // 保存维修数据
        ZcwxjlDO zcwxjl = ZcwxjlConvert.INSTANCE.convert(createReqVO);
        zcwxjlMapper.insert(zcwxjl);
        // 发起维修流程
        Map<String, Object> variables = new HashMap<>();
        // variables.put()
        String processInstanceId = processInstanceApi.createProcessInstance(userId,
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(variables).setBusinessKey(String.valueOf(zcwxjl.getId())));
        // 更新流程信息到维修记录表
        zcwxjlMapper.updateById(new ZcwxjlDO().setId(zcwxjl.getId())
                .setProcessInstanceId(processInstanceId)
                .setWxzt(BpmProcessInstanceStatusEnum.RUNNING.getStatus()));
        // 返回
        return zcwxjl.getId();
    }

    @Override
    public void updateZcwxjl(ZcwxjlUpdateReqVO updateReqVO) {
        // 校验存在
        validateZcwxjlExists(updateReqVO.getId());
        // 更新
        ZcwxjlDO updateObj = ZcwxjlConvert.INSTANCE.convert(updateReqVO);
        zcwxjlMapper.updateById(updateObj);
    }

    @Override
    public void updateZcwxjlResult(Long id, Integer result) {
        validateZcwxjlExists(id);
        // todo 维修完成相关操作
        // 更新数据库
        zcwxjlMapper.updateById(new ZcwxjlDO().setId(id).setWxzt(result));
    }

    @Override
    public void deleteZcwxjl(Long id) {
        // 校验存在
        validateZcwxjlExists(id);
        // 删除
        zcwxjlMapper.deleteById(id);
    }

    private void validateZcwxjlExists(Long id) {
        if (zcwxjlMapper.selectById(id) == null) {
            throw exception(ZCWXJL_NOT_EXISTS);
        }
    }

    @Override
    public ZcwxjlDO getZcwxjl(Long id) {
        return zcwxjlMapper.selectById(id);
    }

    @Override
    public ZcwxjlPageItemRespVO getZcwxjlDetail(Long id) {
        ZcwxjlDO zcwxjlDO = zcwxjlMapper.selectById(id);
        return convertPageItemVO(Collections.singletonList(zcwxjlDO)).get(0);
    }

    @Override
    public List<ZcwxjlDO> getZcwxjlList(Collection<Long> ids) {
        return zcwxjlMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ZcwxjlPageItemRespVO> getZcwxjlPage(ZcwxjlPageReqVO pageReqVO) {
        PageResult<ZcwxjlDO> pageResult = zcwxjlMapper.selectPage(pageReqVO);
        return new PageResult<>(convertPageItemVO(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    public List<ZcwxjlDO> getZcwxjlList(ZcwxjlExportReqVO exportReqVO) {
        return zcwxjlMapper.selectList(exportReqVO);
    }

    private List<ZcwxjlPageItemRespVO> convertPageItemVO(List<ZcwxjlDO> source) {
        if (CollUtil.isEmpty(source)) return Collections.emptyList();
        // 获取基础数据
        Map<Long, ZczsjRespDTO> assetsMap = zczsjApi.getZczsjMap(source.stream()
                .filter(o -> Objects.equals(o.getZclb(), ENTIRE.getVal()))
                .map(ZcwxjlDO::getZcxxid)
                .collect(Collectors.toSet()));
        Map<Long, ZzcxxRespDTO> assetsChildMap = zzcxxApi.getZzcxxjMap(source.stream()
                .filter(o -> Objects.equals(o.getZclb(), PART.getVal()))
                .map(ZcwxjlDO::getZcxxid)
                .collect(Collectors.toSet()));
        Map<Long, WxyyRespDTO> reasonMap = wxyyApi.getWxyyMap(convertSet(source, ZcwxjlDO::getWxyy));
        // 拼接数据
        List<ZcwxjlPageItemRespVO> result = new ArrayList<>();
        source.forEach(o -> {
            ZcwxjlPageItemRespVO respVO = ZcwxjlConvert.INSTANCE.convert2(o);
            respVO.setReason(reasonMap.get(o.getWxyy()).getWxyyms());

            AssetsClassEnum classEnum = AssetsClassEnum.getByVal(o.getZclb());
            if (classEnum == null) throw exception(ZCLB_NOT_EXISTS);
            switch (classEnum) {
                case PART:
                    respVO.setAssetsName(assetsChildMap.get(o.getZcxxid()).getZzcmc());
                    break;
                case ENTIRE:
                    respVO.setAssetsName(assetsMap.get(o.getZcxxid()).getZcmc());
                    break;
            }
            result.add(respVO);
        });
        return result;
    }
}
