package com.neway.assets.module.maintenance.controller.admin.zcwxjl;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo.*;
import com.neway.assets.module.maintenance.convert.zcwxjl.ZcwxjlConvert;
import com.neway.assets.module.maintenance.dal.dataobject.zcwxjl.ZcwxjlDO;
import com.neway.assets.module.maintenance.service.zcwxjl.ZcwxjlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 资产维修保养记录")
@RestController
@RequestMapping("/maintenance/zcwxjl")
@Validated
public class ZcwxjlController {

    @Resource
    private ZcwxjlService zcwxjlService;

    @PostMapping("/create")
    @Operation(summary = "发起资产维修流程")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:create')")
    public CommonResult<Long> createZcwxjl(@Valid @RequestBody ZcwxjlCreateReqVO createReqVO) {
        return success(zcwxjlService.createZcwxjl(getLoginUserId(), createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资产维修保养记录")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:update')")
    public CommonResult<Boolean> updateZcwxjl(@Valid @RequestBody ZcwxjlUpdateReqVO updateReqVO) {
        zcwxjlService.updateZcwxjl(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产维修保养记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:delete')")
    public CommonResult<Boolean> deleteZcwxjl(@RequestParam("id") Long id) {
        zcwxjlService.deleteZcwxjl(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产维修保养记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:query')")
    public CommonResult<ZcwxjlRespVO> getZcwxjl(@RequestParam("id") Long id) {
        ZcwxjlDO zcwxjl = zcwxjlService.getZcwxjl(id);
        return success(ZcwxjlConvert.INSTANCE.convert(zcwxjl));
    }

    @GetMapping("/get-detail")
    @Operation(summary = "获得资产维修保养记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:query')")
    public CommonResult<ZcwxjlPageItemRespVO> getZcwxjlDetail(@RequestParam("id") Long id) {
        ZcwxjlPageItemRespVO zcwxjl = zcwxjlService.getZcwxjlDetail(id);
        return success(zcwxjl);
    }

    @GetMapping("/list")
    @Operation(summary = "获得资产维修保养记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:query')")
    public CommonResult<List<ZcwxjlRespVO>> getZcwxjlList(@RequestParam("ids") Collection<Long> ids) {
        List<ZcwxjlDO> list = zcwxjlService.getZcwxjlList(ids);
        return success(ZcwxjlConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产维修保养记录分页")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:query')")
    public CommonResult<PageResult<ZcwxjlPageItemRespVO>> getZcwxjlPage(@Valid ZcwxjlPageReqVO pageVO) {
        PageResult<ZcwxjlPageItemRespVO> pageResult = zcwxjlService.getZcwxjlPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出资产维修保养记录 Excel")
    @PreAuthorize("@ss.hasPermission('maintenance:zcwxjl:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportZcwxjlExcel(@Valid ZcwxjlExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ZcwxjlDO> list = zcwxjlService.getZcwxjlList(exportReqVO);
        // 导出 Excel
        List<ZcwxjlExcelVO> datas = ZcwxjlConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "资产维修保养记录.xls", "数据", ZcwxjlExcelVO.class, datas);
    }

}
