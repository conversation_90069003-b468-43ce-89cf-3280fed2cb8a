package com.neway.assets.module.maintenance.controller.admin.sbybjl.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产质保记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SbybjlPageReqVO extends PageParam {

    @Schema(description = "主资产&子资产标识")
    private Integer zzbs;

    @Schema(description = "资产id", example = "24519")
    private Long zcid;

    @Schema(description = "质保到期日")
    private LocalDate zbdqr;

    @Schema(description = "维保供应商", example = "15775")
    private Long gysid;

    @Schema(description = "供应商合同&协议")
    private String htxy;

    @Schema(description = "维保费用")
    private BigDecimal wbfy;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
