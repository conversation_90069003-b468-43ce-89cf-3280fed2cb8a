package com.neway.assets.module.maintenance.service.zcwxjl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo.*;
import com.neway.assets.module.maintenance.dal.dataobject.zcwxjl.ZcwxjlDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 资产维修保养记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ZcwxjlService {

    /**
     * 创建资产维修保养记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createZcwxjl(@Valid ZcwxjlCreateReqVO createReqVO);

    Long createZcwxjl(Long userId, @Valid ZcwxjlCreateReqVO createReqVO);

    /**
     * 更新资产维修保养记录
     *
     * @param updateReqVO 更新信息
     */
    void updateZcwxjl(@Valid ZcwxjlUpdateReqVO updateReqVO);

    /**
     * 更新资产维修记录流程结果
     * @param id 记录ID
     * @param result 流程结果
     * <AUTHOR>
     * @since 2023/5/31 13:13
     */
    void updateZcwxjlResult(Long id, Integer result);

    /**
     * 删除资产维修保养记录
     *
     * @param id 编号
     */
    void deleteZcwxjl(Long id);

    /**
     * 获得资产维修保养记录
     *
     * @param id 编号
     * @return 资产维修保养记录
     */
    ZcwxjlDO getZcwxjl(Long id);

    ZcwxjlPageItemRespVO getZcwxjlDetail(Long id);

    /**
     * 获得资产维修保养记录列表
     *
     * @param ids 编号
     * @return 资产维修保养记录列表
     */
    List<ZcwxjlDO> getZcwxjlList(Collection<Long> ids);

    /**
     * 获得资产维修保养记录分页
     *
     * @param pageReqVO 分页查询
     * @return 资产维修保养记录分页
     */
    PageResult<ZcwxjlPageItemRespVO> getZcwxjlPage(ZcwxjlPageReqVO pageReqVO);

    /**
     * 获得资产维修保养记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产维修保养记录列表
     */
    List<ZcwxjlDO> getZcwxjlList(ZcwxjlExportReqVO exportReqVO);

}
