package com.neway.assets.module.maintenance.service.sbybjl;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.*;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 资产质保记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SbybjlService {

    /**
     * 创建资产质保记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSbybjl(@Valid SbybjlCreateReqVO createReqVO);

    /**
     * 更新资产质保记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSbybjl(@Valid SbybjlUpdateReqVO updateReqVO);

    /**
     * 删除资产质保记录
     *
     * @param id 编号
     */
    void deleteSbybjl(Long id);

    /**
     * 获得资产质保记录
     *
     * @param id 编号
     * @return 资产质保记录
     */
    SbybjlDO getSbybjl(Long id);

    /**
     * 获得资产质保记录列表
     *
     * @param ids 编号
     * @return 资产质保记录列表
     */
    List<SbybjlDO> getSbybjlList(Collection<Long> ids);

    /**
     * 获得资产质保记录分页
     *
     * @param pageReqVO 分页查询
     * @return 资产质保记录分页
     */
    PageResult<SbybjlDO> getSbybjlPage(SbybjlPageReqVO pageReqVO);

    /**
     * 获得资产质保记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 资产质保记录列表
     */
    List<SbybjlDO> getSbybjlList(SbybjlExportReqVO exportReqVO);

}
