package com.neway.assets.module.maintenance.api.sbybjl;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlCreateReqDTO;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlRespDTO;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.SbybjlCreateReqVO;
import com.neway.assets.module.maintenance.convert.sbybjl.SbybjlConvert;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import com.neway.assets.module.maintenance.service.sbybjl.SbybjlService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/6/19 8:29
 **/
@Service
@RequiredArgsConstructor
public class SbybjlApiImpl implements SbybjlApi{

    private final SbybjlService sbybjlService;

    @Override
    public List<SbybjlRespDTO> getSbybjlList(Set<Long> ids) {
        if (CollectionUtils.isAnyEmpty(ids)) return Collections.emptyList();
        List<SbybjlDO> sbybjlList = sbybjlService.getSbybjlList(ids);
        return SbybjlConvert.INSTANCE.convertList03(sbybjlList);
    }

    @Override
    public void createSbybjl(SbybjlCreateReqDTO createReqDTO) {
        SbybjlCreateReqVO createReqVO = SbybjlConvert.INSTANCE.convert(createReqDTO);
        sbybjlService.createSbybjl(createReqVO);
    }
}
