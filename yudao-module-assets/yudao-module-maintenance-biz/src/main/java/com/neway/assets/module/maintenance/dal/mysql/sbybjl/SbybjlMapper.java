package com.neway.assets.module.maintenance.dal.mysql.sbybjl;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.maintenance.dal.dataobject.sbybjl.SbybjlDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.maintenance.controller.admin.sbybjl.vo.*;

/**
 * 资产质保记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SbybjlMapper extends BaseMapperX<SbybjlDO> {

    default PageResult<SbybjlDO> selectPage(SbybjlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SbybjlDO>()
                .eqIfPresent(SbybjlDO::getZcid, reqVO.getZcid())
                .eqIfPresent(SbybjlDO::getZbdqr, reqVO.getZbdqr())
                .eqIfPresent(SbybjlDO::getGysid, reqVO.getGysid())
                .eqIfPresent(SbybjlDO::getHtxy, reqVO.getHtxy())
                .eqIfPresent(SbybjlDO::getWbfy, reqVO.getWbfy())
                .betweenIfPresent(SbybjlDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SbybjlDO::getId));
    }

    default List<SbybjlDO> selectList(SbybjlExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SbybjlDO>()
                .eqIfPresent(SbybjlDO::getZcid, reqVO.getZcid())
                .eqIfPresent(SbybjlDO::getZbdqr, reqVO.getZbdqr())
                .eqIfPresent(SbybjlDO::getGysid, reqVO.getGysid())
                .eqIfPresent(SbybjlDO::getHtxy, reqVO.getHtxy())
                .eqIfPresent(SbybjlDO::getWbfy, reqVO.getWbfy())
                .betweenIfPresent(SbybjlDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SbybjlDO::getId));
    }

}
