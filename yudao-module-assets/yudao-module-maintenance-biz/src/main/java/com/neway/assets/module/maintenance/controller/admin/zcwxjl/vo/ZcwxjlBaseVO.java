package com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
* 资产维修保养记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ZcwxjlBaseVO {

    @Schema(description = "资产类别", required = true)
    @NotNull(message = "资产类别不能为空")
    private Integer zclb;

    @Schema(description = "资产id", required = true, example = "4911")
    @NotNull(message = "资产id不能为空")
    private Long zcxxid;

    @Schema(description = "维护方式")
    private Integer whfs;

    @Schema(description = "计划开始日期")
    private LocalDate jhksrq;

    @Schema(description = "计划截至日期")
    private LocalDate jhjzrq;

    @Schema(description = "实际开始日期")
    private LocalDate sjksrq;

    @Schema(description = "实际结束日期")
    private LocalDate sjjsrq;

    @Schema(description = "维护/维修原因（bs_wxyy->id）")
    private Long wxyy;

    @Schema(description = "维护/维修方案")
    private String wxfan;

    @Schema(description = "维护/维修结果")
    private String wxjg;

    @Schema(description = "维护/维修前拍照（存储路径）")
    private String wxqpz;

    @Schema(description = "维护/维修后拍照（存储路径）")
    private String wxhpz;

    @Schema(description = "维护/维修状态")
    private Integer wxzt;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "流程编号", example = "31929")
    private String processInstanceId;

}
