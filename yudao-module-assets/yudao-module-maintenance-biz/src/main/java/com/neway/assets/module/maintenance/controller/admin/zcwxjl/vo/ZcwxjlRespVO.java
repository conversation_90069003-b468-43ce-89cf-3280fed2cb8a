package com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资产维修保养记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcwxjlRespVO extends ZcwxjlBaseVO {

    @Schema(description = "自增长id", required = true, example = "10423")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
