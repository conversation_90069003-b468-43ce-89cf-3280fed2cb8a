package com.neway.assets.module.maintenance.service.zcwxjl.listener;

import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEventListener;
import com.neway.assets.module.maintenance.service.zcwxjl.ZcwxjlService;
import com.neway.assets.module.maintenance.service.zcwxjl.ZcwxjlServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/5/31 11:48
 **/
@Component
@RequiredArgsConstructor
public class MaintenanceResultListener extends BpmProcessInstanceStatusEventListener {

    private final ZcwxjlService zcwxjlService;

    @Override
    protected String getProcessDefinitionKey() {
        return ZcwxjlServiceImpl.PROCESS_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        zcwxjlService.updateZcwxjlResult(Long.parseLong(event.getBusinessKey()), event.getStatus());
    }
}
