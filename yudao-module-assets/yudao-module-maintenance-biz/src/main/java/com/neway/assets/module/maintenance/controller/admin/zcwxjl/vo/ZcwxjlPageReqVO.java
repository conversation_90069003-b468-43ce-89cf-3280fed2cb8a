package com.neway.assets.module.maintenance.controller.admin.zcwxjl.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 资产维修保养记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ZcwxjlPageReqVO extends PageParam {

    @Schema(description = "资产类别")
    private Integer zclb;

    @Schema(description = "资产id", example = "4911")
    private Long zcxxid;

    @Schema(description = "维护方式")
    private Integer whfs;

    @Schema(description = "计划开始日期")
    private LocalDate jhksrq;

    @Schema(description = "计划截至日期")
    private LocalDate jhjzrq;

    @Schema(description = "实际开始日期")
    private LocalDate sjksrq;

    @Schema(description = "实际结束日期")
    private LocalDate sjjsrq;

    @Schema(description = "维护/维修原因（bs_wxyy->id）")
    private Long wxyy;

    @Schema(description = "维护/维修方案")
    private String wxfan;

    @Schema(description = "维护/维修结果")
    private String wxjg;

    @Schema(description = "维护/维修前拍照（存储路径）")
    private String wxqpz;

    @Schema(description = "维护/维修后拍照（存储路径）")
    private String wxhpz;

    @Schema(description = "维护/维修状态")
    private Integer wxzt;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "流程编号", example = "31929")
    private String processInstanceId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
