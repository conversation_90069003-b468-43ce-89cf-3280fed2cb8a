package com.neway.assets.module.maintenance.controller.admin.sbybjl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 资产质保记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SbybjlUpdateReqVO extends SbybjlBaseVO {

    @Schema(description = "id", required = true, example = "13746")
    @NotNull(message = "id不能为空")
    private Long id;

}
