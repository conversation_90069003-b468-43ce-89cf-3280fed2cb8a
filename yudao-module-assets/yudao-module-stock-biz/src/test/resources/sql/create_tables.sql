CREATE TABLE IF NOT EXISTS "op_bpysrk" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "bpflid" bigint,
    "pbwlh" varchar,
    "bppch" varchar,
    "cfkcdd" bigint,
    "cfkw" bigint,
    "cfhjh" bigint,
    "bppp" varchar,
    "bpzzs" bigint,
    "bpgys" bigint,
    "bpsl" varchar,
    "jldwid" bigint,
    "bphsdj" varchar,
    "bpbhsdj" varchar,
    "jybz" bigint,
    "bphsje" varchar,
    "bpbhsje" varchar,
    "bpcghth" varchar,
    "grrq" varchar,
    "bz" varchar,
    "czzt" int,
    "process_instance_id" varchar,
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "updater" varchar DEFAULT '',
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '耗材/备品备件验收入库表';


CREATE TABLE IF NOT EXISTS "op_dljkc" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "dljfl" varchar NOT NULL,
  "dljwlh" varchar NOT NULL,
  "dljmc" varchar NOT NULL,
  "dljggxh" varchar NOT NULL,
  "cfkcdd" int,
  "cfkw" bigint,
  "cfhj" int,
  "dljpp" varchar,
  "dljzzs" bigint,
  "dljgys" bigint,
  "dljsl" varchar,
  "jldwid" bigint,
  "sfmx" int,
  "bz" varchar,
  "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "creator" varchar DEFAULT '',
  "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  "updater" varchar DEFAULT '',
  "deleted" bit NOT NULL DEFAULT FALSE,
  PRIMARY KEY ("id")
) COMMENT '刀/量具库存表';