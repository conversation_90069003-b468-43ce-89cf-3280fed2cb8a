package com.neway.assets.module.stock.service.bpysrk;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkCreateReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkExportReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkPageReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkUpdateReqVO;
import com.neway.assets.module.stock.dal.dataobject.bpysrk.BpysrkDO;
import com.neway.assets.module.stock.dal.mysql.bpysrk.BpysrkMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.neway.assets.module.stock.enums.ErrorCodeConstants.BPYSRK_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BpysrkServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BpysrkServiceImpl.class)
public class BpysrkServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BpysrkServiceImpl bpysrkService;

    @Resource
    private BpysrkMapper bpysrkMapper;

    @Test
    public void testCreateBpysrk_success() {
        // 准备参数
        BpysrkCreateReqVO reqVO = randomPojo(BpysrkCreateReqVO.class);

        // 调用
        Long bpysrkId = bpysrkService.createBpysrk(reqVO);
        // 断言
        assertNotNull(bpysrkId);
        // 校验记录的属性是否正确
        BpysrkDO bpysrk = bpysrkMapper.selectById(bpysrkId);
        assertPojoEquals(reqVO, bpysrk);
    }

    @Test
    public void testUpdateBpysrk_success() {
        // mock 数据
        BpysrkDO dbBpysrk = randomPojo(BpysrkDO.class);
        bpysrkMapper.insert(dbBpysrk);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BpysrkUpdateReqVO reqVO = randomPojo(BpysrkUpdateReqVO.class, o -> {
            o.setId(dbBpysrk.getId()); // 设置更新的 ID
        });

        // 调用
        bpysrkService.updateBpysrk(reqVO);
        // 校验是否更新正确
        BpysrkDO bpysrk = bpysrkMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, bpysrk);
    }

    @Test
    public void testUpdateBpysrk_notExists() {
        // 准备参数
        BpysrkUpdateReqVO reqVO = randomPojo(BpysrkUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> bpysrkService.updateBpysrk(reqVO), BPYSRK_NOT_EXISTS);
    }

    @Test
    public void testDeleteBpysrk_success() {
        // mock 数据
        BpysrkDO dbBpysrk = randomPojo(BpysrkDO.class);
        bpysrkMapper.insert(dbBpysrk);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbBpysrk.getId();

        // 调用
        bpysrkService.deleteBpysrk(id);
       // 校验数据不存在了
       assertNull(bpysrkMapper.selectById(id));
    }

    @Test
    public void testDeleteBpysrk_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> bpysrkService.deleteBpysrk(id), BPYSRK_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetBpysrkPage() {
       // mock 数据
       BpysrkDO dbBpysrk = randomPojo(BpysrkDO.class, o -> { // 等会查询到
           o.setBpflid(null);
           o.setPbwlh(null);
           o.setBppch(null);
           o.setCfkcdd(null);
           o.setCfkw(null);
           o.setCfhjh(null);
           o.setBppp(null);
           o.setBpzzs(null);
           o.setBpgys(null);
           o.setBpsl(null);
           o.setJldwid(null);
           o.setBphsdj(null);
           o.setBpbhsdj(null);
           o.setJybz(null);
           o.setBphsje(null);
           o.setBpbhsje(null);
           o.setBpcghth(null);
           o.setGrrq(null);
           o.setBz(null);
           o.setCzzt(null);
           o.setProcessInstanceId(null);
           o.setCreateTime(null);
       });
       bpysrkMapper.insert(dbBpysrk);
       // 测试 bpflid 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpflid(null)));
       // 测试 pbwlh 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setPbwlh(null)));
       // 测试 bppch 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBppch(null)));
       // 测试 cfkcdd 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCfkcdd(null)));
       // 测试 cfkw 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCfkw(null)));
       // 测试 cfhjh 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCfhjh(null)));
       // 测试 bppp 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBppp(null)));
       // 测试 bpzzs 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpzzs(null)));
       // 测试 bpgys 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpgys(null)));
       // 测试 bpsl 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpsl(null)));
       // 测试 jldwid 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setJldwid(null)));
       // 测试 bphsdj 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBphsdj(null)));
       // 测试 bpbhsdj 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpbhsdj(null)));
       // 测试 jybz 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setJybz(null)));
       // 测试 bphsje 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBphsje(null)));
       // 测试 bpbhsje 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpbhsje(null)));
       // 测试 bpcghth 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpcghth(null)));
       // 测试 grrq 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setGrrq(null)));
       // 测试 bz 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBz(null)));
       // 测试 czzt 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCzzt(null)));
       // 测试 processInstanceId 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setProcessInstanceId(null)));
       // 测试 createTime 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCreateTime(null)));
       // 准备参数
       BpysrkPageReqVO reqVO = new BpysrkPageReqVO();
       reqVO.setBpflid(null);
       reqVO.setPbwlh(null);
       reqVO.setBppch(null);
       reqVO.setCfkcdd(null);
       reqVO.setCfkw(null);
       reqVO.setCfhjh(null);
       reqVO.setBppp(null);
       reqVO.setBpzzs(null);
       reqVO.setBpgys(null);
       reqVO.setBpsl(null);
       reqVO.setJldwid(null);
       reqVO.setBphsdj(null);
       reqVO.setBpbhsdj(null);
       reqVO.setJybz(null);
       reqVO.setBphsje(null);
       reqVO.setBpbhsje(null);
       reqVO.setBpcghth(null);
       reqVO.setGrrq(null);
       reqVO.setBz(null);
       reqVO.setCzzt(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BpysrkDO> pageResult = bpysrkService.getBpysrkPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbBpysrk, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetBpysrkList() {
       // mock 数据
       BpysrkDO dbBpysrk = randomPojo(BpysrkDO.class, o -> { // 等会查询到
           o.setBpflid(null);
           o.setPbwlh(null);
           o.setBppch(null);
           o.setCfkcdd(null);
           o.setCfkw(null);
           o.setCfhjh(null);
           o.setBppp(null);
           o.setBpzzs(null);
           o.setBpgys(null);
           o.setBpsl(null);
           o.setJldwid(null);
           o.setBphsdj(null);
           o.setBpbhsdj(null);
           o.setJybz(null);
           o.setBphsje(null);
           o.setBpbhsje(null);
           o.setBpcghth(null);
           o.setGrrq(null);
           o.setBz(null);
           o.setCzzt(null);
           o.setProcessInstanceId(null);
           o.setCreateTime(null);
       });
       bpysrkMapper.insert(dbBpysrk);
       // 测试 bpflid 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpflid(null)));
       // 测试 pbwlh 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setPbwlh(null)));
       // 测试 bppch 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBppch(null)));
       // 测试 cfkcdd 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCfkcdd(null)));
       // 测试 cfkw 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCfkw(null)));
       // 测试 cfhjh 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCfhjh(null)));
       // 测试 bppp 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBppp(null)));
       // 测试 bpzzs 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpzzs(null)));
       // 测试 bpgys 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpgys(null)));
       // 测试 bpsl 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpsl(null)));
       // 测试 jldwid 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setJldwid(null)));
       // 测试 bphsdj 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBphsdj(null)));
       // 测试 bpbhsdj 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpbhsdj(null)));
       // 测试 jybz 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setJybz(null)));
       // 测试 bphsje 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBphsje(null)));
       // 测试 bpbhsje 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpbhsje(null)));
       // 测试 bpcghth 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBpcghth(null)));
       // 测试 grrq 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setGrrq(null)));
       // 测试 bz 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setBz(null)));
       // 测试 czzt 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCzzt(null)));
       // 测试 processInstanceId 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setProcessInstanceId(null)));
       // 测试 createTime 不匹配
       bpysrkMapper.insert(cloneIgnoreId(dbBpysrk, o -> o.setCreateTime(null)));
       // 准备参数
       BpysrkExportReqVO reqVO = new BpysrkExportReqVO();
       reqVO.setBpflid(null);
       reqVO.setPbwlh(null);
       reqVO.setBppch(null);
       reqVO.setCfkcdd(null);
       reqVO.setCfkw(null);
       reqVO.setCfhjh(null);
       reqVO.setBppp(null);
       reqVO.setBpzzs(null);
       reqVO.setBpgys(null);
       reqVO.setBpsl(null);
       reqVO.setJldwid(null);
       reqVO.setBphsdj(null);
       reqVO.setBpbhsdj(null);
       reqVO.setJybz(null);
       reqVO.setBphsje(null);
       reqVO.setBpbhsje(null);
       reqVO.setBpcghth(null);
       reqVO.setGrrq(null);
       reqVO.setBz(null);
       reqVO.setCzzt(null);
       reqVO.setProcessInstanceId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BpysrkDO> list = bpysrkService.getBpysrkList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbBpysrk, list.get(0));
    }

}
