package com.neway.assets.module.stock.controller.admin.bpysrk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
* 耗材验收记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BpysrkBaseVO {

    @Schema(description = "分类id", example = "2723")
    private Long bpflid;

    @Schema(description = "物料号")
    private String pbwlh;

    @Schema(description = "批次号")
    private String bppch;

    @Schema(description = "存放库存地点")
    private Long cfkcdd;

    @Schema(description = "存放库位(bs_kwxx->id)")
    private Long cfkw;

    @Schema(description = "存放货架号(bs_hjxx->id)")
    private Long cfhjh;

    @Schema(description = "品牌")
    private String bppp;

    @Schema(description = "制造商(供应商--id)")
    private Long bpzzs;

    @Schema(description = "供应商(供应商--id)")
    private Long bpgys;

    @Schema(description = "数量")
    private BigDecimal bpsl;

    @Schema(description = "计量单位", example = "20954")
    private Long jldwid;

    @Schema(description = "单价(含税)")
    private BigDecimal bphsdj;

    @Schema(description = "单价(不含税)")
    private BigDecimal bpbhsdj;

    @Schema(description = "币种")
    private Long jybz;

    @Schema(description = "金额(含税)")
    private BigDecimal bphsje;

    @Schema(description = "金额(不含税)")
    private BigDecimal bpbhsje;

    @Schema(description = "合同/采购订单号")
    private String bpcghth;

    @Schema(description = "购入日期")
    private LocalDate grrq;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "操作状态")
    private Integer czzt;

    @Schema(description = "流程编号", example = "6236")
    private String processInstanceId;

}
