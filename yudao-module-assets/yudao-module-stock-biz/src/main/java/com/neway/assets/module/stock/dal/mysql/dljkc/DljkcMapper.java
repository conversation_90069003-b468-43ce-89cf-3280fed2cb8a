package com.neway.assets.module.stock.dal.mysql.dljkc;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.neway.assets.module.stock.dal.dataobject.dljkc.DljkcDO;
import org.apache.ibatis.annotations.Mapper;
import com.neway.assets.module.stock.controller.admin.dljkc.vo.*;

/**
 * 刀/量具库存 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DljkcMapper extends BaseMapperX<DljkcDO> {

    default PageResult<DljkcDO> selectPage(DljkcPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DljkcDO>()
                .eqIfPresent(DljkcDO::getDljfl, reqVO.getDljfl())
                .eqIfPresent(DljkcDO::getDljwlh, reqVO.getDljwlh())
                .likeIfPresent(DljkcDO::getDljmc, reqVO.getDljmc())
                .eqIfPresent(DljkcDO::getDljggxh, reqVO.getDljggxh())
                .eqIfPresent(DljkcDO::getCfkcdd, reqVO.getCfkcdd())
                .eqIfPresent(DljkcDO::getCfkw, reqVO.getCfkw())
                .eqIfPresent(DljkcDO::getCfhj, reqVO.getCfhj())
                .eqIfPresent(DljkcDO::getDljpp, reqVO.getDljpp())
                .eqIfPresent(DljkcDO::getDljzzs, reqVO.getDljzzs())
                .eqIfPresent(DljkcDO::getDljgys, reqVO.getDljgys())
                .eqIfPresent(DljkcDO::getDljsl, reqVO.getDljsl())
                .eqIfPresent(DljkcDO::getJldwid, reqVO.getJldwid())
                .eqIfPresent(DljkcDO::getSfmx, reqVO.getSfmx())
                .likeIfPresent(DljkcDO::getBz, reqVO.getBz())
                .betweenIfPresent(DljkcDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DljkcDO::getId));
    }

    default List<DljkcDO> selectList(DljkcExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DljkcDO>()
                .eqIfPresent(DljkcDO::getDljfl, reqVO.getDljfl())
                .eqIfPresent(DljkcDO::getDljwlh, reqVO.getDljwlh())
                .eqIfPresent(DljkcDO::getDljmc, reqVO.getDljmc())
                .eqIfPresent(DljkcDO::getDljggxh, reqVO.getDljggxh())
                .eqIfPresent(DljkcDO::getCfkcdd, reqVO.getCfkcdd())
                .eqIfPresent(DljkcDO::getCfkw, reqVO.getCfkw())
                .eqIfPresent(DljkcDO::getCfhj, reqVO.getCfhj())
                .eqIfPresent(DljkcDO::getDljpp, reqVO.getDljpp())
                .eqIfPresent(DljkcDO::getDljzzs, reqVO.getDljzzs())
                .eqIfPresent(DljkcDO::getDljgys, reqVO.getDljgys())
                .eqIfPresent(DljkcDO::getDljsl, reqVO.getDljsl())
                .eqIfPresent(DljkcDO::getJldwid, reqVO.getJldwid())
                .eqIfPresent(DljkcDO::getSfmx, reqVO.getSfmx())
                .likeIfPresent(DljkcDO::getBz, reqVO.getBz())
                .betweenIfPresent(DljkcDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DljkcDO::getId));
    }

}
