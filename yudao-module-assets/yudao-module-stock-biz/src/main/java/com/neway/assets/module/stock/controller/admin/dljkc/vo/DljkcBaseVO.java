package com.neway.assets.module.stock.controller.admin.dljkc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
* 刀/量具库存 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DljkcBaseVO {

    @Schema(description = "刀/量具分类（0：量具 1：刀具）", required = true)
    @NotNull(message = "刀/量具分类（0：量具 1：刀具）不能为空")
    private String dljfl;

    @Schema(description = "刀/量具物料号", required = true)
    @NotNull(message = "刀/量具物料号不能为空")
    private String dljwlh;

    @Schema(description = "刀/量具名称", required = true)
    @NotNull(message = "刀/量具名称不能为空")
    private String dljmc;

    @Schema(description = "规格型号", required = true)
    @NotNull(message = "规格型号不能为空")
    private String dljggxh;

    @Schema(description = "存放库存地点（bs_kcdd->id）")
    private Integer cfkcdd;

    @Schema(description = "存放库位（bs_kwxx->id）")
    private Long cfkw;

    @Schema(description = "存放货架号")
    private Integer cfhj;

    @Schema(description = "品牌")
    private String dljpp;

    @Schema(description = "制造商（SRM供应商管理-->id）")
    private Long dljzzs;

    @Schema(description = "供应商（SRM供应商管理-->id）")
    private Long dljgys;

    @Schema(description = "数量")
    private BigDecimal dljsl;

    @Schema(description = "计量单位（bs_jldw->id）", example = "17593")
    private Long jldwid;

    @Schema(description = "是否有编号明细（0:没有  1：有）")
    private Integer sfmx;

    @Schema(description = "备注")
    private String bz;

}
