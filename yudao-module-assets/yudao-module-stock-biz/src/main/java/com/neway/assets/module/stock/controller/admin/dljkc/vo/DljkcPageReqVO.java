package com.neway.assets.module.stock.controller.admin.dljkc.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 刀/量具库存分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DljkcPageReqVO extends PageParam {

    @Schema(description = "刀/量具分类（0：量具 1：刀具）")
    private String dljfl;

    @Schema(description = "刀/量具物料号")
    private String dljwlh;

    @Schema(description = "刀/量具名称")
    private String dljmc;

    @Schema(description = "规格型号")
    private String dljggxh;

    @Schema(description = "存放库存地点（bs_kcdd->id）")
    private Integer cfkcdd;

    @Schema(description = "存放库位（bs_kwxx->id）")
    private Long cfkw;

    @Schema(description = "存放货架号")
    private Integer cfhj;

    @Schema(description = "品牌")
    private String dljpp;

    @Schema(description = "制造商（SRM供应商管理-->id）")
    private Long dljzzs;

    @Schema(description = "供应商（SRM供应商管理-->id）")
    private Long dljgys;

    @Schema(description = "数量")
    private BigDecimal dljsl;

    @Schema(description = "计量单位（bs_jldw->id）", example = "17593")
    private Long jldwid;

    @Schema(description = "是否有编号明细（0:没有  1：有）")
    private Integer sfmx;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
