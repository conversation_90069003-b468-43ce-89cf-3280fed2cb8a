package com.neway.assets.module.stock.controller.admin.bpysrk;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.info.api.bzxx.BzxxApi;
import com.neway.assets.module.info.api.bzxx.dto.BzxxRespDTO;
import com.neway.assets.module.info.api.hbjfl.HbjflApi;
import com.neway.assets.module.info.api.hbjfl.dto.HbjflRespDTO;
import com.neway.assets.module.info.api.hjxx.HjxxApi;
import com.neway.assets.module.info.api.hjxx.dto.HjxxRespDTO;
import com.neway.assets.module.info.api.jldw.JldwApi;
import com.neway.assets.module.info.api.jldw.dto.JldwRespDTO;
import com.neway.assets.module.info.api.kcdd.KcddApi;
import com.neway.assets.module.info.api.kcdd.dto.KcddRespDTO;
import com.neway.assets.module.info.api.kwxx.KwxxApi;
import com.neway.assets.module.info.api.kwxx.dto.KwxxRespDTO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.*;
import com.neway.assets.module.stock.convert.bpysrk.BpysrkConvert;
import com.neway.assets.module.stock.dal.dataobject.bpysrk.BpysrkDO;
import com.neway.assets.module.stock.service.bpysrk.BpysrkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 耗材验收记录")
@RestController
@RequestMapping("/stock/bpysrk")
@Validated
@RequiredArgsConstructor
public class BpysrkController {

    private final BpysrkService bpysrkService;

    private final HbjflApi hbjflApi;
    private final KcddApi kcddApi;
    private final KwxxApi kwxxApi;
    private final HjxxApi hjxxApi;
    private final JldwApi jldwApi;
    private final BzxxApi bzxxApi;

    @PostMapping("/create")
    @Operation(summary = "创建耗材验收记录")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:create')")
    public CommonResult<Long> createBpysrk(@Valid @RequestBody BpysrkCreateReqVO createReqVO) {
        return success(bpysrkService.createBpysrk(createReqVO));
    }

    @PostMapping("/batch-create")
    @Operation(summary = "创建耗材验收记录")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:create')")
    public CommonResult<Long> batchCreateBpysrk(@Valid @RequestBody List<BpysrkCreateReqVO> createReqVOs) {
        return success(bpysrkService.batchCreateBpysrk(getLoginUserId(), createReqVOs));
    }

    @PutMapping("/update")
    @Operation(summary = "更新耗材验收记录")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:update')")
    public CommonResult<Boolean> updateBpysrk(@Valid @RequestBody BpysrkUpdateReqVO updateReqVO) {
        bpysrkService.updateBpysrk(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除耗材验收记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:delete')")
    public CommonResult<Boolean> deleteBpysrk(@RequestParam("id") Long id) {
        bpysrkService.deleteBpysrk(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得耗材验收记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:query')")
    public CommonResult<BpysrkRespVO> getBpysrk(@RequestParam("id") Long id) {
        BpysrkDO bpysrk = bpysrkService.getBpysrk(id);
        return success(BpysrkConvert.INSTANCE.convert(bpysrk));
    }

    @GetMapping("/list")
    @Operation(summary = "获得耗材验收记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:query')")
    public CommonResult<List<BpysrkDetailRespVO>> getBpysrkList(@RequestParam("ids") Collection<Long> ids) {
        List<BpysrkDO> list = bpysrkService.getBpysrkList(ids);
        return success(convertDetailVO(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得耗材验收记录分页")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:query')")
    public CommonResult<PageResult<BpysrkDetailRespVO>> getBpysrkPage(@Valid BpysrkPageReqVO pageVO) {
        PageResult<BpysrkDO> pageResult = bpysrkService.getBpysrkPage(pageVO);
        List<BpysrkDetailRespVO> detailList = convertDetailVO(pageResult.getList());
        return success(new PageResult<>(detailList, pageResult.getTotal()));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出耗材验收记录 Excel")
    @PreAuthorize("@ss.hasPermission('stock:bpysrk:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBpysrkExcel(@Valid BpysrkExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BpysrkDO> list = bpysrkService.getBpysrkList(exportReqVO);
        // 导出 Excel
        List<BpysrkExcelVO> datas = BpysrkConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "耗材验收记录.xls", "数据", BpysrkExcelVO.class, datas);
    }

    /**
     * 转换成DetailVO
     * @param source 原数据
     * @return java.util.List<BpysrkDetailRespVO>
     * <AUTHOR>
     * @since 2023/6/13 14:44
     */
    private List<BpysrkDetailRespVO> convertDetailVO(List<BpysrkDO> source) {
        if (CollectionUtils.isAnyEmpty(source)) return Collections.emptyList();
        // 获取基础数据
        Map<Long, HbjflRespDTO> typeMap = hbjflApi.getHbflMap(source.stream().map(BpysrkDO::getBpflid).collect(Collectors.toSet()));
        Map<Long, KcddRespDTO> locationMap = kcddApi.getKcddMap(source.stream().map(BpysrkDO::getCfkcdd).collect(Collectors.toSet()));
        Map<Long, KwxxRespDTO> positionMap = kwxxApi.getKwxxMap(source.stream().map(BpysrkDO::getCfkw).collect(Collectors.toSet()));
        Map<Long, HjxxRespDTO> goodsShelvesMap = hjxxApi.getHjxxMap(source.stream().map(BpysrkDO::getCfhjh).collect(Collectors.toSet()));
        Map<Long, JldwRespDTO> unitMap = jldwApi.getJldwMap(source.stream().map(BpysrkDO::getJldwid).collect(Collectors.toSet()));
        Map<Long, BzxxRespDTO> currencyMap = bzxxApi.getBzxxMap(source.stream().map(BpysrkDO::getJybz).collect(Collectors.toSet()));
        // 拼接数据
        List<BpysrkDetailRespVO> result = new ArrayList<>();
        source.forEach(o -> {
            BpysrkDetailRespVO respVO = BpysrkConvert.INSTANCE.convert2(o);
            respVO.setType(typeMap.get(o.getBpflid()).getBflmc());
            respVO.setLocation(locationMap.get(o.getCfkcdd()).getKcddms());
            respVO.setPosition(positionMap.get(o.getCfkw()).getKwms());
            respVO.setGoodsShelves(goodsShelvesMap.get(o.getCfhjh()).getHjbh());
            respVO.setUnit(unitMap.get(o.getJldwid()).getJldwmc());
            respVO.setCurrency(currencyMap.get(o.getJybz()).getBzmc());
            result.add(respVO);
        });
        return result;
    }
}
