package com.neway.assets.module.stock.service.dljkc;

import java.util.*;
import javax.validation.*;
import com.neway.assets.module.stock.controller.admin.dljkc.vo.*;
import com.neway.assets.module.stock.dal.dataobject.dljkc.DljkcDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 刀/量具库存 Service 接口
 *
 * <AUTHOR>
 */
public interface DljkcService {

    /**
     * 创建刀/量具库存
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDljkc(@Valid DljkcCreateReqVO createReqVO);

    /**
     * 更新刀/量具库存
     *
     * @param updateReqVO 更新信息
     */
    void updateDljkc(@Valid DljkcUpdateReqVO updateReqVO);

    /**
     * 删除刀/量具库存
     *
     * @param id 编号
     */
    void deleteDljkc(Long id);

    /**
     * 获得刀/量具库存
     *
     * @param id 编号
     * @return 刀/量具库存
     */
    DljkcDO getDljkc(Long id);

    /**
     * 获得刀/量具库存列表
     *
     * @param ids 编号
     * @return 刀/量具库存列表
     */
    List<DljkcDO> getDljkcList(Collection<Long> ids);

    /**
     * 获得刀/量具库存分页
     *
     * @param pageReqVO 分页查询
     * @return 刀/量具库存分页
     */
    PageResult<DljkcDO> getDljkcPage(DljkcPageReqVO pageReqVO);

    /**
     * 获得刀/量具库存列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 刀/量具库存列表
     */
    List<DljkcDO> getDljkcList(DljkcExportReqVO exportReqVO);

}
