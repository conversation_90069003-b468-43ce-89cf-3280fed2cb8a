package com.neway.assets.module.stock.service.dljkc;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.neway.assets.module.stock.controller.admin.dljkc.vo.*;
import com.neway.assets.module.stock.dal.dataobject.dljkc.DljkcDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.neway.assets.module.stock.convert.dljkc.DljkcConvert;
import com.neway.assets.module.stock.dal.mysql.dljkc.DljkcMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.stock.enums.ErrorCodeConstants.*;

/**
 * 刀/量具库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DljkcServiceImpl implements DljkcService {

    @Resource
    private DljkcMapper dljkcMapper;

    @Override
    public Long createDljkc(DljkcCreateReqVO createReqVO) {
        // 插入
        DljkcDO dljkc = DljkcConvert.INSTANCE.convert(createReqVO);
        dljkcMapper.insert(dljkc);
        // 返回
        return dljkc.getId();
    }

    @Override
    public void updateDljkc(DljkcUpdateReqVO updateReqVO) {
        // 校验存在
        validateDljkcExists(updateReqVO.getId());
        // 更新
        DljkcDO updateObj = DljkcConvert.INSTANCE.convert(updateReqVO);
        dljkcMapper.updateById(updateObj);
    }

    @Override
    public void deleteDljkc(Long id) {
        // 校验存在
        validateDljkcExists(id);
        // 删除
        dljkcMapper.deleteById(id);
    }

    private void validateDljkcExists(Long id) {
        if (dljkcMapper.selectById(id) == null) {
            throw exception(DLJKC_NOT_EXISTS);
        }
    }

    @Override
    public DljkcDO getDljkc(Long id) {
        return dljkcMapper.selectById(id);
    }

    @Override
    public List<DljkcDO> getDljkcList(Collection<Long> ids) {
        return dljkcMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DljkcDO> getDljkcPage(DljkcPageReqVO pageReqVO) {
        return dljkcMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DljkcDO> getDljkcList(DljkcExportReqVO exportReqVO) {
        return dljkcMapper.selectList(exportReqVO);
    }

}
