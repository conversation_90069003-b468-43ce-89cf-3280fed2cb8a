package com.neway.assets.module.stock.dal.dataobject.dljkc;

import lombok.*;

import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 刀/量具库存 DO
 *
 * <AUTHOR>
 */
@TableName(value = "op_dljkc", schema = "gdzc")
@KeySequence("op_dljkc_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DljkcDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 刀/量具分类（0：量具 1：刀具）
     */
    private String dljfl;
    /**
     * 刀/量具物料号
     */
    private String dljwlh;
    /**
     * 刀/量具名称
     */
    private String dljmc;
    /**
     * 规格型号
     */
    private String dljggxh;
    /**
     * 存放库存地点（bs_kcdd->id）
     */
    private Integer cfkcdd;
    /**
     * 存放库位（bs_kwxx->id）
     */
    private Long cfkw;
    /**
     * 存放货架号
     */
    private Integer cfhj;
    /**
     * 品牌
     */
    private String dljpp;
    /**
     * 制造商（SRM供应商管理-->id）
     */
    private Long dljzzs;
    /**
     * 供应商（SRM供应商管理-->id）
     */
    private Long dljgys;
    /**
     * 数量
     */
    private BigDecimal dljsl;
    /**
     * 计量单位（bs_jldw->id）
     */
    private Long jldwid;
    /**
     * 是否有编号明细（0:没有  1：有）
     */
    private Integer sfmx;
    /**
     * 备注
     */
    private String bz;

}
