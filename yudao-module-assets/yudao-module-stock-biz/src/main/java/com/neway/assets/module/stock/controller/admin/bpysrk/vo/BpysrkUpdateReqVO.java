package com.neway.assets.module.stock.controller.admin.bpysrk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 耗材验收记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpysrkUpdateReqVO extends BpysrkBaseVO {

    @Schema(description = "自增长id", required = true, example = "26119")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
