package com.neway.assets.module.stock.controller.admin.dljkc;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.neway.assets.module.stock.controller.admin.dljkc.vo.*;
import com.neway.assets.module.stock.convert.dljkc.DljkcConvert;
import com.neway.assets.module.stock.dal.dataobject.dljkc.DljkcDO;
import com.neway.assets.module.stock.service.dljkc.DljkcService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 刀/量具库存")
@RestController
@RequestMapping("/stock/dljkc")
@Validated
public class DljkcController {

    @Resource
    private DljkcService dljkcService;

    @PostMapping("/create")
    @Operation(summary = "创建刀/量具库存")
    @PreAuthorize("@ss.hasPermission('stock:dljkc:create')")
    public CommonResult<Long> createDljkc(@Valid @RequestBody DljkcCreateReqVO createReqVO) {
        return success(dljkcService.createDljkc(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新刀/量具库存")
    @PreAuthorize("@ss.hasPermission('stock:dljkc:update')")
    public CommonResult<Boolean> updateDljkc(@Valid @RequestBody DljkcUpdateReqVO updateReqVO) {
        dljkcService.updateDljkc(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除刀/量具库存")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('stock:dljkc:delete')")
    public CommonResult<Boolean> deleteDljkc(@RequestParam("id") Long id) {
        dljkcService.deleteDljkc(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得刀/量具库存")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('stock:dljkc:query')")
    public CommonResult<DljkcRespVO> getDljkc(@RequestParam("id") Long id) {
        DljkcDO dljkc = dljkcService.getDljkc(id);
        return success(DljkcConvert.INSTANCE.convert(dljkc));
    }

    @GetMapping("/list")
    @Operation(summary = "获得刀/量具库存列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('stock:dljkc:query')")
    public CommonResult<List<DljkcRespVO>> getDljkcList(@RequestParam("ids") Collection<Long> ids) {
        List<DljkcDO> list = dljkcService.getDljkcList(ids);
        return success(DljkcConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得刀/量具库存分页")
    @PreAuthorize("@ss.hasPermission('stock:dljkc:query')")
    public CommonResult<PageResult<DljkcRespVO>> getDljkcPage(@Valid DljkcPageReqVO pageVO) {
        PageResult<DljkcDO> pageResult = dljkcService.getDljkcPage(pageVO);
        return success(DljkcConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出刀/量具库存 Excel")
    @PreAuthorize("@ss.hasPermission('stock:dljkc:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDljkcExcel(@Valid DljkcExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<DljkcDO> list = dljkcService.getDljkcList(exportReqVO);
        // 导出 Excel
        List<DljkcExcelVO> datas = DljkcConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "刀/量具库存.xls", "数据", DljkcExcelVO.class, datas);
    }
}
