package com.neway.assets.module.stock.dal.mysql.bpysrk;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkExportReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkPageReqVO;
import com.neway.assets.module.stock.dal.dataobject.bpysrk.BpysrkDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 耗材验收记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BpysrkMapper extends BaseMapperX<BpysrkDO> {

    default PageResult<BpysrkDO> selectPage(BpysrkPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BpysrkDO>()
                .eqIfPresent(BpysrkDO::getBpflid, reqVO.getBpflid())
                .eqIfPresent(BpysrkDO::getPbwlh, reqVO.getPbwlh())
                .eqIfPresent(BpysrkDO::getBppch, reqVO.getBppch())
                .eqIfPresent(BpysrkDO::getCfkcdd, reqVO.getCfkcdd())
                .eqIfPresent(BpysrkDO::getCfkw, reqVO.getCfkw())
                .eqIfPresent(BpysrkDO::getCfhjh, reqVO.getCfhjh())
                .eqIfPresent(BpysrkDO::getBppp, reqVO.getBppp())
                .eqIfPresent(BpysrkDO::getBpzzs, reqVO.getBpzzs())
                .eqIfPresent(BpysrkDO::getBpgys, reqVO.getBpgys())
                .eqIfPresent(BpysrkDO::getBpsl, reqVO.getBpsl())
                .eqIfPresent(BpysrkDO::getJldwid, reqVO.getJldwid())
                .eqIfPresent(BpysrkDO::getBphsdj, reqVO.getBphsdj())
                .eqIfPresent(BpysrkDO::getBpbhsdj, reqVO.getBpbhsdj())
                .eqIfPresent(BpysrkDO::getJybz, reqVO.getJybz())
                .eqIfPresent(BpysrkDO::getBphsje, reqVO.getBphsje())
                .eqIfPresent(BpysrkDO::getBpbhsje, reqVO.getBpbhsje())
                .eqIfPresent(BpysrkDO::getBpcghth, reqVO.getBpcghth())
                .eqIfPresent(BpysrkDO::getGrrq, reqVO.getGrrq())
                .eqIfPresent(BpysrkDO::getBz, reqVO.getBz())
                .eqIfPresent(BpysrkDO::getCzzt, reqVO.getCzzt())
                .eqIfPresent(BpysrkDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(BpysrkDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BpysrkDO::getProcessInstanceId));
    }

    default List<BpysrkDO> selectList(BpysrkExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BpysrkDO>()
                .eqIfPresent(BpysrkDO::getBpflid, reqVO.getBpflid())
                .eqIfPresent(BpysrkDO::getPbwlh, reqVO.getPbwlh())
                .eqIfPresent(BpysrkDO::getBppch, reqVO.getBppch())
                .eqIfPresent(BpysrkDO::getCfkcdd, reqVO.getCfkcdd())
                .eqIfPresent(BpysrkDO::getCfkw, reqVO.getCfkw())
                .eqIfPresent(BpysrkDO::getCfhjh, reqVO.getCfhjh())
                .eqIfPresent(BpysrkDO::getBppp, reqVO.getBppp())
                .eqIfPresent(BpysrkDO::getBpzzs, reqVO.getBpzzs())
                .eqIfPresent(BpysrkDO::getBpgys, reqVO.getBpgys())
                .eqIfPresent(BpysrkDO::getBpsl, reqVO.getBpsl())
                .eqIfPresent(BpysrkDO::getJldwid, reqVO.getJldwid())
                .eqIfPresent(BpysrkDO::getBphsdj, reqVO.getBphsdj())
                .eqIfPresent(BpysrkDO::getBpbhsdj, reqVO.getBpbhsdj())
                .eqIfPresent(BpysrkDO::getJybz, reqVO.getJybz())
                .eqIfPresent(BpysrkDO::getBphsje, reqVO.getBphsje())
                .eqIfPresent(BpysrkDO::getBpbhsje, reqVO.getBpbhsje())
                .eqIfPresent(BpysrkDO::getBpcghth, reqVO.getBpcghth())
                .eqIfPresent(BpysrkDO::getGrrq, reqVO.getGrrq())
                .eqIfPresent(BpysrkDO::getBz, reqVO.getBz())
                .eqIfPresent(BpysrkDO::getCzzt, reqVO.getCzzt())
                .eqIfPresent(BpysrkDO::getProcessInstanceId, reqVO.getProcessInstanceId())
                .betweenIfPresent(BpysrkDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BpysrkDO::getId));
    }

}
