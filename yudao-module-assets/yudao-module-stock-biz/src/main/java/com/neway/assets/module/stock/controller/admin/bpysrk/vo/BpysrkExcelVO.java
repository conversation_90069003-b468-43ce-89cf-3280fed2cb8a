package com.neway.assets.module.stock.controller.admin.bpysrk.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 耗材验收记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BpysrkExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("分类id")
    private Long bpflid;

    @ExcelProperty("物料号")
    private String pbwlh;

    @ExcelProperty("批次号")
    private String bppch;

    @ExcelProperty("存放库存地点")
    private Long cfkcdd;

    @ExcelProperty("存放库位(bs_kwxx->id)")
    private Long cfkw;

    @ExcelProperty("存放货架号(bs_hjxx->id)")
    private Long cfhjh;

    @ExcelProperty("品牌")
    private String bppp;

    @ExcelProperty("制造商(供应商--id)")
    private Long bpzzs;

    @ExcelProperty("供应商(供应商--id)")
    private Long bpgys;

    @ExcelProperty("数量")
    private BigDecimal bpsl;

    @ExcelProperty("计量单位")
    private Long jldwid;

    @ExcelProperty("单价(含税)")
    private BigDecimal bphsdj;

    @ExcelProperty("单价(不含税)")
    private BigDecimal bpbhsdj;

    @ExcelProperty("币种")
    private Long jybz;

    @ExcelProperty("金额(含税)")
    private BigDecimal bphsje;

    @ExcelProperty("金额(不含税)")
    private BigDecimal bpbhsje;

    @ExcelProperty("合同/采购订单号")
    private String bpcghth;

    @ExcelProperty("购入日期")
    private LocalDate grrq;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty(value = "操作状态", converter = DictConvert.class)
    @DictFormat("bpm_process_instance_result")
    private Integer czzt;

    @ExcelProperty("流程编号")
    private String processInstanceId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
