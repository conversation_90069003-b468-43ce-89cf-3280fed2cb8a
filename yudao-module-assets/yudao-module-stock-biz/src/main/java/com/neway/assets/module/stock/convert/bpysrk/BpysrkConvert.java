package com.neway.assets.module.stock.convert.bpysrk;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.*;
import com.neway.assets.module.stock.dal.dataobject.bpysrk.BpysrkDO;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 耗材验收记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BpysrkConvert {

    BpysrkConvert INSTANCE = Mappers.getMapper(BpysrkConvert.class);

    BpysrkDO convert(BpysrkCreateReqVO bean);

    List<BpysrkDO> convertCreateList(List<BpysrkCreateReqVO> list);

    BpysrkDO convert(BpysrkUpdateReqVO bean);
    @Named("one")
    BpysrkRespVO convert(BpysrkDO bean);
    @Named("two")
    BpysrkDetailRespVO convert2(BpysrkDO bean);

    @IterableMapping(qualifiedByName = "one")
    List<BpysrkRespVO> convertList(List<BpysrkDO> list);

    PageResult<BpysrkRespVO> convertPage(PageResult<BpysrkDO> page);

    List<BpysrkExcelVO> convertList02(List<BpysrkDO> list);


}
