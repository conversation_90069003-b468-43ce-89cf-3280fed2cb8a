package com.neway.assets.module.stock.convert.dljkc;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.neway.assets.module.stock.controller.admin.dljkc.vo.*;
import com.neway.assets.module.stock.dal.dataobject.dljkc.DljkcDO;

/**
 * 刀/量具库存 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DljkcConvert {

    DljkcConvert INSTANCE = Mappers.getMapper(DljkcConvert.class);

    DljkcDO convert(DljkcCreateReqVO bean);

    DljkcDO convert(DljkcUpdateReqVO bean);

    DljkcRespVO convert(DljkcDO bean);

    List<DljkcRespVO> convertList(List<DljkcDO> list);

    PageResult<DljkcRespVO> convertPage(PageResult<DljkcDO> page);

    List<DljkcExcelVO> convertList02(List<DljkcDO> list);

}
