package com.neway.assets.module.stock.service.bpysrk;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceStatusEnum;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkCreateReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkExportReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkPageReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkUpdateReqVO;
import com.neway.assets.module.stock.convert.bpysrk.BpysrkConvert;
import com.neway.assets.module.stock.dal.dataobject.bpysrk.BpysrkDO;
import com.neway.assets.module.stock.dal.mysql.bpysrk.BpysrkMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.neway.assets.module.stock.enums.ErrorCodeConstants.BPYSRK_NOT_EXISTS;

/**
 * 耗材验收记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class BpysrkServiceImpl implements BpysrkService {

    /* 耗材验收入库流程KEY */
    public static final String PROCESS_KEY = "CONSUMABLE_CHECK";
    private final String ID_DELIMITER = ",";

    private final BpysrkMapper bpysrkMapper;

    private final BpmProcessInstanceApi processInstanceApi;

    @Override
    public Long createBpysrk(BpysrkCreateReqVO createReqVO) {
        // 插入
        BpysrkDO bpysrk = BpysrkConvert.INSTANCE.convert(createReqVO);
        bpysrkMapper.insert(bpysrk);
        // 返回
        return bpysrk.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long batchCreateBpysrk(Long userId, List<BpysrkCreateReqVO> createReqVOS) {
        // 保存验收单数据
        List<BpysrkDO> bpysrkDOList = BpysrkConvert.INSTANCE.convertCreateList(createReqVOS);
        bpysrkMapper.insertBatch(bpysrkDOList);
        // 发起流程
        String businessKey = bpysrkDOList.stream().map(o -> String.valueOf(o.getId())).collect(Collectors.joining(ID_DELIMITER));
        Map<String, Object> variables = new HashMap<>();
        String processInstanceId = processInstanceApi.createProcessInstance(userId, new BpmProcessInstanceCreateReqDTO()
                .setVariables(variables).setProcessDefinitionKey(PROCESS_KEY).setBusinessKey(businessKey));
        // 更新流程ID
        List<BpysrkDO> updateList = bpysrkDOList.stream().map(o -> {
            BpysrkDO bpysrkDO = new BpysrkDO();
            bpysrkDO.setId(o.getId()).setProcessInstanceId(processInstanceId)
                    .setCzzt(BpmProcessInstanceStatusEnum.RUNNING.getStatus());
            return bpysrkDO;
        }).collect(Collectors.toList());
        bpysrkMapper.updateBatch(updateList, updateList.size());
        return (long) bpysrkDOList.size();
    }

    @Override
    public void updateBpysrk(BpysrkUpdateReqVO updateReqVO) {
        // 校验存在
        validateBpysrkExists(updateReqVO.getId());
        // 更新
        BpysrkDO updateObj = BpysrkConvert.INSTANCE.convert(updateReqVO);
        bpysrkMapper.updateById(updateObj);
    }

    @Override
    public void updateBpysrkResult(String ids, Integer result) {
        Set<BpysrkDO> update = Arrays.stream(ids.split(ID_DELIMITER)).map(id -> {
            BpysrkDO bpysrkDO = new BpysrkDO();
            bpysrkDO.setId(Long.valueOf(id));
            bpysrkDO.setCzzt(result);
            return bpysrkDO;
        }).collect(Collectors.toSet());
        if (Objects.equals(result, BpmProcessInstanceStatusEnum.APPROVE.getStatus())) {
            // 通过，TODO 相关入库操作
        }
        bpysrkMapper.updateBatch(update, update.size());
    }

    @Override
    public void deleteBpysrk(Long id) {
        // 校验存在
        validateBpysrkExists(id);
        // 删除
        bpysrkMapper.deleteById(id);
    }

    private void validateBpysrkExists(Long id) {
        if (bpysrkMapper.selectById(id) == null) {
            throw exception(BPYSRK_NOT_EXISTS);
        }
    }

    @Override
    public BpysrkDO getBpysrk(Long id) {
        return bpysrkMapper.selectById(id);
    }

    @Override
    public List<BpysrkDO> getBpysrkList(Collection<Long> ids) {
        return bpysrkMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BpysrkDO> getBpysrkPage(BpysrkPageReqVO pageReqVO) {
        return bpysrkMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BpysrkDO> getBpysrkList(BpysrkExportReqVO exportReqVO) {
        return bpysrkMapper.selectList(exportReqVO);
    }

}
