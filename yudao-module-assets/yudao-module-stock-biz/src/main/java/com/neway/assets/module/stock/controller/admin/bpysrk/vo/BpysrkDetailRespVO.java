package com.neway.assets.module.stock.controller.admin.bpysrk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2023/6/13 14:37
 **/
@Schema(description = "管理后台 - 耗材验收详细 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpysrkDetailRespVO extends BpysrkRespVO {
    @Schema(description = "分类描述")
    private String type;
    @Schema(description = "库存地点")
    private String location;
    @Schema(description = "库位")
    private String position;
    @Schema(description = "货架")
    private String goodsShelves;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "币种")
    private String currency;
}
