package com.neway.assets.module.stock.service.bpysrk;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkCreateReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkExportReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkPageReqVO;
import com.neway.assets.module.stock.controller.admin.bpysrk.vo.BpysrkUpdateReqVO;
import com.neway.assets.module.stock.dal.dataobject.bpysrk.BpysrkDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 耗材验收记录 Service 接口
 *
 * <AUTHOR>
 */
public interface BpysrkService {

    /**
     * 创建耗材验收记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBpysrk(@Valid BpysrkCreateReqVO createReqVO);

    Long batchCreateBpysrk(Long userId, @Valid List<BpysrkCreateReqVO> createReqVOS);

    /**
     * 更新耗材验收记录
     *
     * @param updateReqVO 更新信息
     */
    void updateBpysrk(@Valid BpysrkUpdateReqVO updateReqVO);

    /**
     * 更新验收流程结果
     * @param ids 主键
     * @param result 结果
     * <AUTHOR>
     * @since 2023/6/14 10:29
     */
    void updateBpysrkResult(String ids, Integer result);

    /**
     * 删除耗材验收记录
     *
     * @param id 编号
     */
    void deleteBpysrk(Long id);

    /**
     * 获得耗材验收记录
     *
     * @param id 编号
     * @return 耗材验收记录
     */
    BpysrkDO getBpysrk(Long id);

    /**
     * 获得耗材验收记录列表
     *
     * @param ids 编号
     * @return 耗材验收记录列表
     */
    List<BpysrkDO> getBpysrkList(Collection<Long> ids);

    /**
     * 获得耗材验收记录分页
     *
     * @param pageReqVO 分页查询
     * @return 耗材验收记录分页
     */
    PageResult<BpysrkDO> getBpysrkPage(BpysrkPageReqVO pageReqVO);

    /**
     * 获得耗材验收记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 耗材验收记录列表
     */
    List<BpysrkDO> getBpysrkList(BpysrkExportReqVO exportReqVO);

}
