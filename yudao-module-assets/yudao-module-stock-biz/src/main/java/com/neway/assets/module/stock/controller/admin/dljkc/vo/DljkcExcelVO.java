package com.neway.assets.module.stock.controller.admin.dljkc.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 刀/量具库存 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class DljkcExcelVO {

    @ExcelProperty("自增长id")
    private Long id;

    @ExcelProperty("刀/量具分类（0：量具 1：刀具）")
    private String dljfl;

    @ExcelProperty("刀/量具物料号")
    private String dljwlh;

    @ExcelProperty("刀/量具名称")
    private String dljmc;

    @ExcelProperty("规格型号")
    private String dljggxh;

    @ExcelProperty("存放库存地点（bs_kcdd->id）")
    private Integer cfkcdd;

    @ExcelProperty("存放库位（bs_kwxx->id）")
    private Long cfkw;

    @ExcelProperty("存放货架号")
    private Integer cfhj;

    @ExcelProperty("品牌")
    private String dljpp;

    @ExcelProperty("制造商（SRM供应商管理-->id）")
    private Long dljzzs;

    @ExcelProperty("供应商（SRM供应商管理-->id）")
    private Long dljgys;

    @ExcelProperty("数量")
    private BigDecimal dljsl;

    @ExcelProperty("计量单位（bs_jldw->id）")
    private Long jldwid;

    @ExcelProperty("是否有编号明细（0:没有  1：有）")
    private Integer sfmx;

    @ExcelProperty("备注")
    private String bz;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
