package com.neway.assets.module.stock.controller.admin.bpysrk.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 耗材验收记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpysrkRespVO extends BpysrkBaseVO {

    @Schema(description = "自增长id", required = true, example = "26119")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
