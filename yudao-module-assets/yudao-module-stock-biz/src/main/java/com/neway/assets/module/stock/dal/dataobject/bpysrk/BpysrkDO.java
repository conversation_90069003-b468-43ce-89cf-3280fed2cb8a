package com.neway.assets.module.stock.dal.dataobject.bpysrk;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 耗材验收记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "op_bpysrk", schema = "gdzc")
@KeySequence("op_bpysrk_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpysrkDO extends BaseDO {

    /**
     * 自增长id
     */
    @TableId
    private Long id;
    /**
     * 分类id
     */
    private Long bpflid;
    /**
     * 物料号
     */
    private String pbwlh;
    /**
     * 批次号
     */
    private String bppch;
    /**
     * 存放库存地点
     */
    private Long cfkcdd;
    /**
     * 存放库位(bs_kwxx->id)
     */
    private Long cfkw;
    /**
     * 存放货架号(bs_hjxx->id)
     */
    private Long cfhjh;
    /**
     * 品牌
     */
    private String bppp;
    /**
     * 制造商(供应商--id)
     */
    private Long bpzzs;
    /**
     * 供应商(供应商--id)
     */
    private Long bpgys;
    /**
     * 数量
     */
    private BigDecimal bpsl;
    /**
     * 计量单位
     */
    private Long jldwid;
    /**
     * 单价(含税)
     */
    private BigDecimal bphsdj;
    /**
     * 单价(不含税)
     */
    private BigDecimal bpbhsdj;
    /**
     * 币种
     */
    private Long jybz;
    /**
     * 金额(含税)
     */
    private BigDecimal bphsje;
    /**
     * 金额(不含税)
     */
    private BigDecimal bpbhsje;
    /**
     * 合同/采购订单号
     */
    private String bpcghth;
    /**
     * 购入日期
     */
    private LocalDate grrq;
    /**
     * 备注
     */
    private String bz;
    /**
     * 操作状态
     *
     * 枚举 {@link cn.iocoder.yudao.module.bpm.enums.task.BpmProcessInstanceResultEnum}
     */
    private Integer czzt;
    /**
     * 流程编号
     */
    private String processInstanceId;

}
