package com.neway.assets.module.stock.controller.admin.dljkc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 刀/量具库存更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DljkcUpdateReqVO extends DljkcBaseVO {

    @Schema(description = "自增长id", required = true, example = "25180")
    @NotNull(message = "自增长id不能为空")
    private Long id;

}
