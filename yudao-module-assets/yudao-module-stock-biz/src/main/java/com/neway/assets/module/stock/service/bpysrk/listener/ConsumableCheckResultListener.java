package com.neway.assets.module.stock.service.bpysrk.listener;

import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEventListener;
import com.neway.assets.module.stock.service.bpysrk.BpysrkService;
import com.neway.assets.module.stock.service.bpysrk.BpysrkServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/6/14 10:19
 **/
@Component
@RequiredArgsConstructor
public class ConsumableCheckResultListener extends BpmProcessInstanceStatusEventListener {

    private final BpysrkService bpysrkService;

    @Override
    protected String getProcessDefinitionKey() {
        return BpysrkServiceImpl.PROCESS_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        bpysrkService.updateBpysrkResult(event.getBusinessKey(), event.getStatus());
    }
}
