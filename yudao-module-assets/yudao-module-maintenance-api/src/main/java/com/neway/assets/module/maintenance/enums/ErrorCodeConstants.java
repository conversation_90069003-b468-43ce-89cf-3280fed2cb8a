package com.neway.assets.module.maintenance.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 资产维修模块错误码枚举类
 * 使用 1-103-000-000 段
 * <AUTHOR>
 * @since 2023/5/25 11:34
 **/
public interface ErrorCodeConstants {

    // ========== 资产维修保养记录 1-103-001-000 ==========
    ErrorCode ZCWXJL_NOT_EXISTS = new ErrorCode(1103001000, "资产维修保养记录不存在");

    // ========== 资产质保记录 1-103-001-000 ==========
    ErrorCode SBYBJL_NOT_EXISTS = new ErrorCode(1103002000, "资产质保记录不存在");
}
