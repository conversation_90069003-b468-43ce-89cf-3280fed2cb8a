package com.neway.assets.module.maintenance.api.sbybjl.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 * @since 2023/6/19 8:44
 **/
@Data
public class SbybjlCreateReqDTO {
    // 主、子资产标识
    private Integer zzbs;
    // 资产id
    private Long zcid;
    // 质保到期日
    private LocalDate zbdqr;
    // 供应商id
    private Long gysid;
    // 合同号
    private String htxy;
    // 维保费用
    private BigDecimal wbfy;
}
