package com.neway.assets.module.maintenance.api.sbybjl;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlCreateReqDTO;
import com.neway.assets.module.maintenance.api.sbybjl.dto.SbybjlRespDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @since 2023/6/19 8:16
 **/
public interface SbybjlApi {

    List<SbybjlRespDTO> getSbybjlList(Set<Long> ids);

    default Map<Long, SbybjlRespDTO> getSbybjlMap(Set<Long> ids) {
        List<SbybjlRespDTO> sbybjlList = getSbybjlList(ids);
        return CollectionUtils.convertMap(sbybjlList, SbybjlRespDTO::getId);
    }

    void createSbybjl(SbybjlCreateReqDTO createReqDTO);
}
