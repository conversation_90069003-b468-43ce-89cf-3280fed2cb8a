package com.yzt.qr.module.reports.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Schema(description = "管理后台 - 采购订单信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseOrderRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20179")
    private Long id;

    @Schema(description = "采购订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购订单号")
    private String orderNo;

    @Schema(description = "采购订单行号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购订单行号")
    private String orderLineNo;

    @Schema(description = "销售订单号")
    @ExcelProperty("销售订单号")
    private String salesOrderNo;

    @Schema(description = "销售订单行号")
    @ExcelProperty("销售订单行号")
    private String salesOrderLineNo;

    @Schema(description = "订单材质规范")
    @ExcelProperty("订单材质规范")
    private String textureSpec;

    @Schema(description = "采购部件代码")
    @ExcelProperty("采购部件代码")
    private String partsCode;

    @Schema(description = "物料号")
    @ExcelProperty("物料号")
    private String materialCode;

    @Schema(description = "物料描述")
    @ExcelProperty("物料描述")
    private String materialDesc;

    @Schema(description = "物料组")
    @ExcelProperty("物料组")
    private String materialGroup;

    @Schema(description = "材质")
    @ExcelProperty("材质")
    private String texture;

    @Schema(description = "系列号")
    @ExcelProperty("系列号")
    private Map<String, String> serials;

    @Schema(description = "供应商代码")
    @ExcelProperty("供应商代码")
    private String supplier;

    @Schema(description = "工厂")
    @ExcelProperty("工厂")
    private String factory;

    @Schema(description = "采购组")
    @ExcelProperty("采购组")
    private String purchasingGroup;

    @Schema(description = "订单数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单数量")
    private Integer qty;

}