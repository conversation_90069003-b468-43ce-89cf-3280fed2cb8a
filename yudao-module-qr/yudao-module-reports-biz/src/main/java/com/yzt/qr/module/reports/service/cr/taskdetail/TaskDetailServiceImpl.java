package com.yzt.qr.module.reports.service.cr.taskdetail;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.storage.util.AbstractModuleMinioUtil;
import com.yzt.common.minio.utils.QmsMinioUtil;
import com.yzt.qr.module.reports.controller.admin.cr.taskdetail.vo.TaskDetailPageReqVO;
import com.yzt.qr.module.reports.controller.admin.cr.taskdetail.vo.TaskDetailSaveReqVO;
import com.yzt.qr.module.reports.controller.admin.report.vo.SalesOrderReportSaveReqVO;
import com.yzt.qr.module.reports.dal.dataobject.attach.ReportAttachDO;
import com.yzt.qr.module.reports.dal.dataobject.cr.taskdetail.TaskDetailDO;
import com.yzt.qr.module.reports.dal.dataobject.cr.taskmain.TaskMainDO;
import com.yzt.qr.module.reports.dal.dataobject.cr.taskmiddle.TaskMiddleDO;
import com.yzt.qr.module.reports.dal.dataobject.report.ReportHeadDO;
import com.yzt.qr.module.reports.dal.dataobject.report.SalesOrderReportDO;
import com.yzt.qr.module.reports.dal.mysql.cr.taskdetail.TaskDetailMapper;
import com.yzt.qr.module.reports.dal.mysql.cr.taskmain.TaskMainMapper;
import com.yzt.qr.module.reports.dal.mysql.cr.taskmiddle.TaskMiddleMapper;
import com.yzt.qr.module.reports.enums.TaskStatusEnum;
import com.yzt.qr.module.reports.service.attach.ReportAttachService;
import com.yzt.qr.module.reports.service.cr.machine.MachineReportGenerateService;
import com.yzt.qr.module.reports.service.cr.machine.MachineReportGenerateServiceImpl;
import com.yzt.qr.module.reports.service.cr.pdf.PdfMergeService;
import com.yzt.qr.module.reports.service.report.ReportHeadService;
import com.yzt.qr.module.reports.service.report.SalesOrderReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.yzt.qr.module.reports.enums.ErrorCodeConstants.TASK_DETAIL_NOT_EXISTS;
import com.yzt.qr.module.reports.service.cr.lock.DistributedLockService;
import org.springframework.context.annotation.Lazy;

/**
 * 竣工报告任务明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TaskDetailServiceImpl implements TaskDetailService {

    @Resource
    private TaskDetailMapper taskDetailMapper;
    
    @Resource
    private TaskMiddleMapper taskMiddleMapper;
    
    @Resource
    private SalesOrderReportService salesOrderReportService;

    @Resource
    private QmsMinioUtil qmsMinioUtil;

    @Resource
    @Lazy
    private PdfMergeService pdfMergeService;
    
    @Resource
    private MachineReportGenerateService machineReportGenerateService;

    @Resource
    private ReportHeadService reportHeadService;

    @Resource
    private ReportAttachService reportAttachService;

    @Resource
    private TaskMainMapper taskMainMapper;

    @Resource
    private DistributedLockService distributedLockService;

    @Override
    public Integer createTaskDetail(@Valid TaskDetailSaveReqVO createReqVO) {
        // 插入
        TaskDetailDO taskDetail = BeanUtils.toBean(createReqVO, TaskDetailDO.class);
        taskDetailMapper.insert(taskDetail);
        // 返回
        return taskDetail.getId();
    }

    @Override
    public void updateTaskDetail(@Valid TaskDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateTaskDetailExists(updateReqVO.getId());
        
        TaskDetailDO originalTask = taskDetailMapper.selectById(updateReqVO.getId());
        
        // 如果要启用使用附件，需要先检查是否存在附件
        if (originalTask != null && Boolean.TRUE.equals(updateReqVO.getUseAttached())) {
            
            if (originalTask.getReportType() == 2) { // 零件类型
                // 检查是否有关联的报告ID
                if (originalTask.getReportId() == null) {
                    throw exception0(5000, "零件任务没有关联的报告ID，无法启用使用附件");
                }

                // 检查是否有关联的附件
                List<ReportAttachDO> attachments = reportAttachService.listByReportIds(
                        Collections.singletonList(originalTask.getReportId()));

                if (attachments == null || attachments.isEmpty()) {
                    throw exception0(5000, "零件报告没有关联的附件，无法启用使用附件");
                }

                log.info("零件任务启用使用附件 - 任务ID: {}, 报告ID: {}, 附件数量: {}",
                        updateReqVO.getId(), originalTask.getReportId(), attachments.size());
            } else if (originalTask.getReportType() == 1) { // 整机类型
                // 检查是否有附件URL
                if (StrUtil.isBlank(originalTask.getAttachUrl())) {
                    throw exception0(5000, "整机任务没有上传附件，无法启用使用附件");
                }
                
                // 检查附件文件是否存在
                try {
                    String objectPath = extractObjectPathFromUrl(originalTask.getAttachUrl());
                    // 尝试获取文件，如果文件不存在会抛出异常
                    qmsMinioUtil.getLatestFile(objectPath);
                } catch (Exception e) {
                    throw exception0(5000, "整机任务的附件文件不存在，无法启用使用附件: " + e.getMessage());
                }

                // 检查是否有关联的报告ID
                if (originalTask.getReportId() == null) {
                    throw exception0(5000, "整机任务没有关联的报告ID，无法启用使用附件");
                }

                // 检查报告是否存在
                SalesOrderReportDO salesOrderReport = salesOrderReportService.getSalesOrderReport(originalTask.getReportId());
                if (salesOrderReport == null) {
                    throw exception0(5000, "整机任务关联的报告不存在，无法启用使用附件");
                }

                // 检查报告是否有PDF文件
                if (StrUtil.isBlank(salesOrderReport.getExportPdfUrl())) {
                    throw exception0(5000, "整机任务关联的报告没有PDF文件，无法启用使用附件");
                }
                
                log.info("整机任务启用使用附件 - 任务ID: {}, 报告ID: {}, 附件URL: {}", 
                        updateReqVO.getId(), originalTask.getReportId(), originalTask.getAttachUrl());
            } else {
                // 固定文件类型
                // 检查是否有关联的附件URL
                if (StrUtil.isBlank(originalTask.getAttachUrl())) {
                    throw exception0(5000, "固定文件任务没有关联的附件，无法启用使用附件");
                }
                
                log.info("固定文件任务启用使用附件 - 任务ID: {}, 附件URL: {}",
                        updateReqVO.getId(), originalTask.getAttachUrl());
            }
        }
        
        // 记录状态变更
        Integer oldStatus = originalTask != null ? originalTask.getStatus() : null;
        Integer newStatus = updateReqVO.getStatus();
        boolean statusChanged = newStatus != null && !newStatus.equals(oldStatus);
        
        // 更新
        TaskDetailDO updateObj = BeanUtils.toBean(updateReqVO, TaskDetailDO.class);
        taskDetailMapper.updateById(updateObj);
        
        // 状态变化处理
        if (statusChanged && originalTask != null) {
            // 如果状态变为已完成，检查并更新中期任务状态
            if (TaskStatusEnum.COMPLETED.getStatus().equals(newStatus)) {
                checkAndUpdateMiddleTaskStatus(originalTask.getMiddleTaskCode());
            }
            // 如果状态变为异常，立即更新中期任务和主任务状态
            else if (TaskStatusEnum.EXCEPTION.getStatus().equals(newStatus)) {
                // 获取中期任务
                TaskMiddleDO middleTask = taskMiddleMapper.selectOne(
                    TaskMiddleDO::getMiddleTaskCode, originalTask.getMiddleTaskCode());
                
                if (middleTask != null) {
                    // 更新中期任务状态为异常
                    middleTask.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                    taskMiddleMapper.updateById(middleTask);
                    log.info("子任务状态变为异常，更新中期任务状态为异常 - 中期任务编码: {}", middleTask.getMiddleTaskCode());
                    
                    // 更新主任务状态为异常
                    checkAndUpdateMainTaskStatus(middleTask.getCrTaskCode());
                }
            }
        }
    }

    @Override
    public void deleteTaskDetail(Integer id) {
        // 校验存在
        validateTaskDetailExists(id);
        // 删除
        taskDetailMapper.deleteById(id);
    }

    private void validateTaskDetailExists(Integer id) {
        if (taskDetailMapper.selectById(id) == null) {
            throw exception(TASK_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public TaskDetailDO getTaskDetail(Integer id) {
        TaskDetailDO taskDetail = taskDetailMapper.selectById(id);
        // 清除零件类型任务的attachUrl，零件类型任务不应该有attachUrl
        if (taskDetail != null && taskDetail.getReportType() != null && taskDetail.getReportType() == 2) {
            taskDetail.setAttachUrl(null);
        }
        return taskDetail;
    }

    @Override
    public PageResult<TaskDetailDO> getTaskDetailPage(TaskDetailPageReqVO pageReqVO) {
        PageResult<TaskDetailDO> pageResult = taskDetailMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<TaskDetailDO>()
                .eqIfPresent(TaskDetailDO::getMiddleTaskCode, pageReqVO.getMiddleTaskCode())
                .eqIfPresent(TaskDetailDO::getIndex, pageReqVO.getIndex())
                .eqIfPresent(TaskDetailDO::getStatus, pageReqVO.getStatus())
                .eqIfPresent(TaskDetailDO::getReportId, pageReqVO.getReportId())
                .likeIfPresent(TaskDetailDO::getSoNo, pageReqVO.getSoNo())
                .likeIfPresent(TaskDetailDO::getSoLineNo, pageReqVO.getSoLineNo())
                .eqIfPresent(TaskDetailDO::getReportType, pageReqVO.getReportType())
                .likeIfPresent(TaskDetailDO::getReportFormat, pageReqVO.getReportFormat())
                .eqIfPresent(TaskDetailDO::getExportTemplateId, pageReqVO.getExportTemplateId())
                .eqIfPresent(TaskDetailDO::getTemplateType, pageReqVO.getTemplateType())
                .likeIfPresent(TaskDetailDO::getSerials, pageReqVO.getSerials())
                .eqIfPresent(TaskDetailDO::getUseAttached, pageReqVO.getUseAttached())
                .eqIfPresent(TaskDetailDO::getNeedExport, pageReqVO.getNeedExport())
                .orderByDesc(TaskDetailDO::getId));
        
        // 清除零件类型任务的attachUrl，零件类型任务不应该有attachUrl
        if (pageResult.getList() != null) {
            for (TaskDetailDO taskDetail : pageResult.getList()) {
                if (taskDetail.getReportType() != null && taskDetail.getReportType() == 2) {
                    taskDetail.setAttachUrl(null);
                }
            }
        }
        
        return pageResult;
    }
    
    @Override
    public void deleteTaskDetailByCrTaskCode(String crTaskCode) {
        // 查询所有与该主任务关联的中期任务
        List<TaskMiddleDO> middleTasks = taskMiddleMapper.selectListByCrTaskCode(crTaskCode);
        
        // 如果没有中期任务，直接返回
        if (middleTasks == null || middleTasks.isEmpty()) {
            return;
        }
        
        // 根据中期任务编码批量删除子任务
        for (TaskMiddleDO middleTask : middleTasks) {
            taskDetailMapper.delete(TaskDetailDO::getMiddleTaskCode, middleTask.getMiddleTaskCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadAttachment(Integer id, MultipartFile file) {
        // 校验任务明细存在
        TaskDetailDO taskDetail = getTaskDetail(id);
        if (taskDetail == null) {
            throw exception(TASK_DETAIL_NOT_EXISTS);
        }
        
        // 校验报告类型是否支持附件上传
        if (taskDetail.getReportType() != 3 && taskDetail.getReportType() != 1) {
            throw new RuntimeException("只有固定文件类型(3)和整机类型(1)支持附件上传");
        }

        try {
            // 构建文件名：任务明细ID_原文件名
            String fileName = taskDetail.getId() + "_" + file.getOriginalFilename();
            
            // 使用QmsMinioUtil上传文件到report-attach目录
            AbstractModuleMinioUtil.UploadResult uploadResult = qmsMinioUtil.uploadReportAttachment(fileName, file);
            String fileUrl = uploadResult.getFileUrl();
            
            log.info("文件上传成功 - 任务明细ID: {}, 文件名: {}, 文件URL: {}", id, fileName, fileUrl);
            
            // 更新任务明细的附件URL
            taskDetail.setAttachUrl(fileUrl);
            taskDetailMapper.updateById(taskDetail);
            
            log.info("任务明细附件URL更新成功 - ID: {}, URL: {}", id, fileUrl);
            
            // 根据报告类型进行不同处理
            if (taskDetail.getReportType() == 1) {
                // 整机类型：创建sales_order_report记录
                createSalesOrderReport(taskDetail, fileUrl);
            }
            
            // 检查主任务是否已启动，只有启动后才修改任务状态
            boolean shouldUpdateStatus = shouldUpdateTaskStatus(taskDetail);
            if (shouldUpdateStatus) {
                // 更新任务状态为已完成
                taskDetail.setStatus(TaskStatusEnum.COMPLETED.getStatus());
                taskDetailMapper.updateById(taskDetail);
                log.info("主任务已启动，更新任务状态为已完成 - 任务明细ID: {}", id);
            } else {
                log.info("主任务未启动，不修改任务状态 - 任务明细ID: {}", id);
            }

            if (shouldUpdateStatus) {
                checkAndUpdateMiddleTaskStatus(taskDetail.getMiddleTaskCode());
            }

        } catch (Exception e) {
            log.error("文件上传失败 - 任务明细ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void downloadAttachment(Integer id, HttpServletResponse response) {
        // 校验任务明细存在
        TaskDetailDO taskDetail = getTaskDetail(id);
        if (taskDetail == null) {
            throw exception(TASK_DETAIL_NOT_EXISTS);
        }
        
        // 零件类型任务不支持直接下载附件
        if (taskDetail.getReportType() == 2) {
            throw new RuntimeException("零件类型任务不支持直接下载附件，请使用下载报告功能");
        }
        
        if (taskDetail.getAttachUrl() == null || taskDetail.getAttachUrl().isEmpty()) {
            throw new RuntimeException("该任务明细没有附件");
        }
        
        try {
            // 从attachUrl中提取对象路径
            String attachUrl = taskDetail.getAttachUrl();
            String objectPath = extractObjectPathFromUrl(attachUrl);
            
            // 使用QmsMinioUtil获取文件流
            InputStream fileStream = qmsMinioUtil.getLatestFile(objectPath);
            
            // 从URL中提取文件名
            String fileName = objectPath.substring(objectPath.lastIndexOf("/") + 1);
            
            // 添加调试日志
            log.info("下载附件调试信息 - 任务明细ID: {}, 对象路径: {}, 提取的文件名: {}", id, objectPath, fileName);
            
            // 根据文件扩展名设置Content-Type
            String contentType = getContentTypeByFileName(fileName);
            
            // 设置响应头
            response.setContentType(contentType);
            response.setCharacterEncoding("UTF-8");
            
            // 使用自定义响应头传递文件名，避免Content-Disposition被过滤的问题
            response.setHeader("X-File-Name", fileName);
            response.setHeader("X-File-Name-Encoded", java.net.URLEncoder.encode(fileName, "UTF-8"));
            
            // 仍然尝试设置Content-Disposition（备用）
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
            String contentDisposition = "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName;
            response.setHeader("Content-Disposition", contentDisposition);
            
            // 写入文件内容到响应流
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 使用缓冲区读取文件流并写入响应流
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fileStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
            
            // 关闭文件流
            fileStream.close();
            
            log.info("文件下载成功 - 任务明细ID: {}, 文件名: {}", id, fileName);
            
        } catch (Exception e) {
            log.error("文件下载失败 - 任务明细ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadReportPdf(Integer id, byte[] pdfBytes, String fileName) {
        // 校验任务明细存在
        TaskDetailDO taskDetail = getTaskDetail(id);
        if (taskDetail == null) {
            throw exception(TASK_DETAIL_NOT_EXISTS);
        }
        
        try {
            // 构建文件名：任务明细ID_时间戳_文件名
            String timeStamp = String.valueOf(System.currentTimeMillis());
            String newFileName = taskDetail.getId() + "_" + timeStamp + "_" + fileName;
            
            // 使用QmsMinioUtil上传PDF文件（使用uploadMergedReport方法支持byte[]）
            AbstractModuleMinioUtil.UploadResult uploadResult = qmsMinioUtil.uploadMergedReport(newFileName, pdfBytes);
            String fileUrl = uploadResult.getFileUrl();
            
            log.info("报告PDF上传成功 - 任务明细ID: {}, 文件名: {}, 文件URL: {}", id, newFileName, fileUrl);
            
            // 如果是整机类型，需要更新sales_order_report记录的export_pdf_url字段
            if (taskDetail.getReportType() == 1) {
                if (taskDetail.getReportId() != null) {
                    // 获取现有的销售订单报告记录
                    SalesOrderReportDO salesOrderReport = salesOrderReportService.getSalesOrderReport(taskDetail.getReportId());
                    if (salesOrderReport != null) {
                        // 更新export_pdf_url字段
                        SalesOrderReportSaveReqVO updateReqVO = new SalesOrderReportSaveReqVO();
                        updateReqVO.setId(salesOrderReport.getId());
                        updateReqVO.setOrderNo(salesOrderReport.getOrderNo());
                        updateReqVO.setOrderLineNo(salesOrderReport.getOrderLineNo());
                        updateReqVO.setExportTemplateId(salesOrderReport.getExportTemplateId());
                        updateReqVO.setExportPdfUrl(fileUrl); // 设置新的PDF URL
                        updateReqVO.setIsAttach(salesOrderReport.getIsAttach());
                        updateReqVO.setReportData(salesOrderReport.getReportData());
                        
                        // 处理系列号
                        if (salesOrderReport.getSerials() != null) {
                            updateReqVO.setSerials(new ArrayList<>(salesOrderReport.getSerials()));
                        }
                        
                        salesOrderReportService.updateSalesOrderReport(updateReqVO);
                        log.info("更新销售订单报告PDF URL成功 - 报告ID: {}, URL: {}", taskDetail.getReportId(), fileUrl);
                    } else {
                        log.warn("找不到关联的销售订单报告记录 - 报告ID: {}", taskDetail.getReportId());
                    }
                } else {
                    // 如果没有关联的报告ID，创建新的销售订单报告记录
                createSalesOrderReport(taskDetail, fileUrl);
                    log.info("创建新的销售订单报告记录 - 任务明细ID: {}", taskDetail.getId());
                }
            } else {
                // 非整机类型，更新任务明细的附件URL
                taskDetail.setAttachUrl(fileUrl);
                log.info("非整机类型任务，更新附件URL - 任务明细ID: {}, URL: {}", id, fileUrl);
            }
            
            // 更新任务状态为已完成
            taskDetail.setStatus(TaskStatusEnum.COMPLETED.getStatus());
            taskDetailMapper.updateById(taskDetail);
            
            log.info("任务明细状态更新成功 - ID: {}, 状态: 已完成", id);
            
        } catch (Exception e) {
            log.error("报告PDF上传失败 - 任务明细ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("报告PDF上传失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从完整URL中提取对象路径
     * 例如：http://180.106.149.4:8063/qms/qms/report-attach/2025/06/26/396_test.pdf 
     * 提取出：qms/report-attach/2025/06/26/396_test.pdf
     */
    private String extractObjectPathFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        try {
            // 使用URL解析，避免手动分割可能出现的问题
            java.net.URL parsedUrl = new java.net.URL(url);
            String path = parsedUrl.getPath();
            
            // URL解码，处理中文文件名
            path = java.net.URLDecoder.decode(path, "UTF-8");
            
            // 移除开头的斜杠
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            
            // 如果路径以桶名开头，则移除桶名部分
            // 例如：qms/qms/report-attach/... -> qms/report-attach/...
            String bucketName = qmsMinioUtil.getBucketName();
            if (path.startsWith(bucketName + "/")) {
                path = path.substring(bucketName.length() + 1);
            }
            
            log.debug("从URL提取对象路径: {} -> {}", url, path);
            return path;
            
        } catch (Exception e) {
            // 如果URL解析失败，回退到原来的方法
            log.warn("URL解析失败，使用备用方法: {}", e.getMessage());
            
            try {
                // 找到桶名称后的路径部分
                String[] parts = url.split("/");
                if (parts.length < 5) {
                    throw new IllegalArgumentException("URL格式不正确: " + url);
                }
                
                // 跳过协议、空字符串、主机和端口，从桶名称后开始拼接
                StringBuilder objectPath = new StringBuilder();
                for (int i = 4; i < parts.length; i++) {
                    if (i > 4) {
                        objectPath.append("/");
                    }
                    // 对每个部分进行URL解码
                    String part = java.net.URLDecoder.decode(parts[i], "UTF-8");
                    objectPath.append(part);
                }
                
                String result = objectPath.toString();
                log.debug("备用方法提取对象路径: {} -> {}", url, result);
                return result;
                
            } catch (Exception ex) {
                log.error("对象路径提取失败: {}", ex.getMessage());
                throw new RuntimeException("无法从URL提取对象路径: " + url, ex);
            }
        }
    }

    /**
     * 根据文件名获取Content-Type
     */
    private String getContentTypeByFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "application/octet-stream";
        }
        
        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFileName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerFileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerFileName.endsWith(".txt")) {
            return "text/plain";
        } else {
            return "application/octet-stream";
        }
    }

    private void createSalesOrderReport(TaskDetailDO taskDetail, String fileUrl) {
        // 检查是否已经存在销售订单报告记录
        if (taskDetail.getReportId() == null) {
            // 首次上传：创建整机报告记录
            SalesOrderReportSaveReqVO salesOrderReport = new SalesOrderReportSaveReqVO();
            salesOrderReport.setOrderNo(taskDetail.getSoNo());
            salesOrderReport.setOrderLineNo(taskDetail.getSoLineNo());
            salesOrderReport.setExportTemplateId(taskDetail.getExportTemplateId());
            salesOrderReport.setIsAttach(true);
            salesOrderReport.setReportData(new HashMap<>());
            
            // 设置PDF URL
            if (taskDetail.getReportType() == 1) {
                // 整机类型报告，设置export_pdf_url字段
                salesOrderReport.setExportPdfUrl(fileUrl);
            }
            
            // 处理系列号
            if (taskDetail.getSerials() != null && !taskDetail.getSerials().isEmpty()) {
                salesOrderReport.setSerials(Arrays.asList(taskDetail.getSerials().split(",")));
            }
            
            // 创建销售订单报告
            Long reportId = salesOrderReportService.createSalesOrderReport(salesOrderReport);
            
            // 更新任务明细的报告ID
            taskDetail.setReportId(reportId);
            taskDetailMapper.updateById(taskDetail);
            
            log.info("首次上传整机文件，创建销售订单报告记录 - 任务明细ID: {}, 报告ID: {}", taskDetail.getId(), reportId);
        } else {
            // 重复上传：更新销售订单报告的export_pdf_url字段
            SalesOrderReportDO existingReport = salesOrderReportService.getSalesOrderReport(taskDetail.getReportId());
            if (existingReport != null && taskDetail.getReportType() == 1) {
                SalesOrderReportSaveReqVO updateReqVO = new SalesOrderReportSaveReqVO();
                updateReqVO.setId(existingReport.getId());
                updateReqVO.setOrderNo(existingReport.getOrderNo());
                updateReqVO.setOrderLineNo(existingReport.getOrderLineNo());
                updateReqVO.setExportTemplateId(existingReport.getExportTemplateId());
                updateReqVO.setExportPdfUrl(fileUrl); // 设置新的PDF URL
                updateReqVO.setIsAttach(existingReport.getIsAttach());
                updateReqVO.setReportData(existingReport.getReportData());
                
                // 处理系列号
                if (existingReport.getSerials() != null) {
                    updateReqVO.setSerials(new ArrayList<>(existingReport.getSerials()));
                }
                
                salesOrderReportService.updateSalesOrderReport(updateReqVO);
                log.info("更新销售订单报告PDF URL - 任务明细ID: {}, 报告ID: {}, URL: {}", 
                        taskDetail.getId(), taskDetail.getReportId(), fileUrl);
            } else {
                log.info("重复上传整机文件，但找不到现有报告记录 - 任务明细ID: {}, 报告ID: {}", 
                        taskDetail.getId(), taskDetail.getReportId());
            }
        }
    }
    
    /**
     * 异步执行PDF合并检查
     * 使用专门的线程池避免阻塞主线程
     */
    @Async("pdfMergeExecutor")
    public void asyncCheckAndMergePdf(Integer taskDetailId) {
        try {
            log.info("开始异步PDF合并检查 - 任务明细ID: {}", taskDetailId);
            TaskDetailDO taskDetail = getTaskDetail(taskDetailId);
            if (taskDetail != null) {
                pdfMergeService.checkAndMergePdf(taskDetail);
                log.info("异步PDF合并检查完成 - 任务明细ID: {}", taskDetailId);
            } else {
                log.warn("任务明细不存在 - ID: {}", taskDetailId);
            }
        } catch (Exception e) {
            log.error("异步PDF合并检查失败 - 任务明细ID: {}, 错误信息: {}", taskDetailId, e.getMessage(), e);
            // PDF合并失败不影响文件上传的成功，只记录错误日志
        }
    }
    
    /**
     * 检查是否应该更新任务状态
     * 只有当主任务已启动（状态为执行中或已完成）时，才更新子任务状态
     */
    private boolean shouldUpdateTaskStatus(TaskDetailDO taskDetail) {
        try {
            // 通过中期任务找到主任务
            TaskMiddleDO middleTask = taskMiddleMapper.selectOne(
                TaskMiddleDO::getMiddleTaskCode, taskDetail.getMiddleTaskCode());
            
            if (middleTask == null) {
                log.warn("找不到中期任务 - 中期任务编码: {}", taskDetail.getMiddleTaskCode());
                return false;
            }
            
            // 检查中期任务状态，如果中期任务还是未开始状态，说明主任务也未启动
            if (middleTask.getStatus() == TaskStatusEnum.NOT_STARTED.getStatus()) {
                log.info("中期任务未启动 - 中期任务编码: {}, 状态: {}", middleTask.getMiddleTaskCode(), middleTask.getStatus());
                return false;
            }
            
            // 中期任务已启动（状态为1执行中、2已完成或3异常），允许更新子任务状态
            log.info("中期任务已启动 - 中期任务编码: {}, 状态: {}", middleTask.getMiddleTaskCode(), middleTask.getStatus());
            return true;
            
        } catch (Exception e) {
            log.error("检查任务状态失败 - 任务明细ID: {}, 错误: {}", taskDetail.getId(), e.getMessage(), e);
            // 发生异常时，为了安全起见，不更新状态
            return false;
        }
    }
    
    /**
     * 异步处理整机任务的自动报告生成
     */
    @Async("pdfMergeExecutor")
    public void processMachineReportGeneration(TaskDetailDO taskDetail) {
        // 检查是否是整机类型的任务
        if (taskDetail.getReportType() != 1) {
            log.debug("非整机类型任务，跳过自动报告生成 - 任务ID: {}, 报告类型: {}", 
                taskDetail.getId(), taskDetail.getReportType());
            return;
        }
        
        // 检查是否已经有报告ID（避免重复生成）
        if (taskDetail.getReportId() != null) {
            log.info("任务已有报告ID，跳过自动报告生成 - 任务ID: {}, 报告ID: {}", 
                taskDetail.getId(), taskDetail.getReportId());
            return;
        }
        
        // 检查任务所属主任务的停止标志
        String crTaskCode = null;
        try {
            TaskMiddleDO middleTask = taskMiddleMapper.selectOne(
                TaskMiddleDO::getMiddleTaskCode, taskDetail.getMiddleTaskCode());
            if (middleTask != null) {
                crTaskCode = middleTask.getCrTaskCode();
                
                // 检查是否已设置停止标志
                String stopFlagKey = "process_generate_task_" + crTaskCode;
                if (crTaskCode != null && distributedLockService.isTaskStopped(stopFlagKey)) {
                    log.info("检测到任务停止标志，中断整机报告生成 - 任务ID: {}, 主任务: {}", 
                        taskDetail.getId(), crTaskCode);
                    return;
                }
            }
        } catch (Exception e) {
            log.error("检查任务停止标志失败: {}", taskDetail.getId(), e);
            // 检查失败继续处理，不中断流程
        }
        
        log.info("开始为整机任务自动生成PDF报告 - 任务ID: {}, 报告格式: {}", 
            taskDetail.getId(), taskDetail.getReportFormat());
        
        try {
            // 再次检查任务所属主任务的停止标志
            if (crTaskCode != null) {
                String stopFlagKey = "process_generate_task_" + crTaskCode;
                if (distributedLockService.isTaskStopped(stopFlagKey)) {
                    log.info("生成前再次检测到任务停止标志，中断整机报告生成 - 任务ID: {}, 主任务: {}", 
                        taskDetail.getId(), crTaskCode);
                    return;
                }
            }
            
            // 调用整机报告生成服务，获取包含reportId和pdfUrl的结果
            MachineReportGenerateServiceImpl.MachineReportResult result = machineReportGenerateService.generateMachineReportPdfWithResult(taskDetail);
            
            // 更新任务的报告ID，但不设置attachUrl
            taskDetail.setReportId(result.getReportId());
            
            // 更新任务状态为已完成
            taskDetail.setStatus(TaskStatusEnum.COMPLETED.getStatus());
            
            taskDetailMapper.updateById(taskDetail);
            
            log.info("整机任务PDF报告自动生成完成 - 任务ID: {}, 报告ID: {}", 
                taskDetail.getId(), result.getReportId());
       
            // 触发PDF合并检查
            pdfMergeService.checkAndMergePdf(taskDetail);
            
        } catch (Exception e) {
            log.error("整机任务PDF报告自动生成失败 - 任务ID: {}, 错误: {}", 
                taskDetail.getId(), e.getMessage(), e);
            
            // 生成失败时，将任务状态设置为异常
            taskDetail.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
            taskDetailMapper.updateById(taskDetail);
        }
    }
    
    /**
     * 批量处理整机任务的自动报告生成，支持停止检查
     * 
     * @param middleTaskCode 中期任务编号
     * @param taskKey 任务键，用于检查是否应该停止处理
     * @return 是否成功完成处理（false表示被中断）
     */
    public boolean batchProcessMachineReportGeneration(String middleTaskCode, String taskKey) {
        log.info("开始批量处理整机任务自动报告生成 - 中期任务编号: {}, 任务键: {}", middleTaskCode, taskKey);
        
        // 先检查是否已设置停止标志
        if (taskKey != null && distributedLockService.isTaskStopped(taskKey)) {
            log.info("检测到停止标志，中断整机报告生成批处理 - 中期任务编号: {}", middleTaskCode);
            return false;
        }
        
        // 查询该中期任务下的所有子任务
        List<TaskDetailDO> taskDetails = taskDetailMapper.selectList(
            new LambdaQueryWrapperX<TaskDetailDO>().eq(TaskDetailDO::getMiddleTaskCode, middleTaskCode));
        
        // 过滤出整机类型的任务，并且不是已完成状态的
        List<TaskDetailDO> machineTypeTasks = taskDetails.stream()
            .filter(task -> task.getReportType() == 1) // 整机类型
            .filter(task -> task.getStatus() != TaskStatusEnum.COMPLETED.getStatus()) // 非完成状态
            .collect(java.util.stream.Collectors.toList());
        
        if (machineTypeTasks.isEmpty()) {
            log.info("该中期任务下没有需要处理的整机类型任务 - 中期任务编号: {}", middleTaskCode);
            return true;
        }
        
        log.info("找到 {} 个整机类型任务需要自动生成报告 - 中期任务编号: {}", 
            machineTypeTasks.size(), middleTaskCode);
        
        // 异步处理每个整机任务
        for (TaskDetailDO taskDetail : machineTypeTasks) {
            // 检查是否应该停止处理
            if (taskKey != null && distributedLockService.isTaskStopped(taskKey)) {
                log.info("检测到停止标志，中断整机报告生成 - 中期任务编号: {}, 子任务ID: {}", 
                    middleTaskCode, taskDetail.getId());
                return false;
            }
            processMachineReportGeneration(taskDetail);
        }
        
        return true;
    }

    @Override
    public void downloadReport(Integer id, HttpServletResponse response) {
        // 校验任务明细存在
        TaskDetailDO taskDetail = getTaskDetail(id);
        if (taskDetail == null) {
            throw exception(TASK_DETAIL_NOT_EXISTS);
        }
        
        try {
            // 根据任务类型获取报告文件URL
            String reportUrl = null;
            String reportFileName = null;
            
            if (taskDetail.getReportId() == null) {
                throw new RuntimeException("该任务明细没有关联的报告ID");
            }
            
            // 根据reportType判断从哪里获取报告
            switch(taskDetail.getReportType()) {
                case 1: // 整机类型
                    // 从sales_order_report表获取报告URL
                    SalesOrderReportDO salesOrderReport = salesOrderReportService.getSalesOrderReport(taskDetail.getReportId());
                    if (salesOrderReport == null) {
                        throw new RuntimeException("找不到关联的整机报告记录");
                    }
                    
                    // 使用useAttached判断是否使用附件
                    if (Boolean.TRUE.equals(taskDetail.getUseAttached()) && taskDetail.getAttachUrl() != null) {
                        // 使用附件作为报告
                        reportUrl = taskDetail.getAttachUrl();
                        reportFileName = "整机报告附件_" + taskDetail.getId() + ".pdf";
                        log.info("使用整机任务附件作为报告 - 任务ID: {}, URL: {}", id, reportUrl);
                    } else {
                        // 使用系统生成的报告
                        reportUrl = salesOrderReport.getExportPdfUrl();
                        if (reportUrl == null || reportUrl.isEmpty()) {
                            throw new RuntimeException("整机报告没有PDF文件");
                        }
                        reportFileName = "整机报告_" + salesOrderReport.getOrderNo() + "_" + salesOrderReport.getOrderLineNo() + ".pdf";
                        log.info("使用整机系统报告 - 任务ID: {}, 报告ID: {}, URL: {}", id, taskDetail.getReportId(), reportUrl);
                    }
                    break;
                    
                case 2: // 零件类型
                    // 从report_head表获取报告URL
                    ReportHeadDO reportHead = reportHeadService.getReportHead(taskDetail.getReportId());
                    if (reportHead == null) {
                        throw new RuntimeException("找不到关联的零件报告记录");
                    }
                    
                    // 零件类型任务总是下载report_head表中的报告文件，不考虑useAttached标志
                    reportUrl = reportHead.getExportPdfUrl();
                    if (reportUrl == null || reportUrl.isEmpty()) {
                        throw new RuntimeException("零件报告没有PDF文件");
                    }
                    reportFileName = "零件报告_" + reportHead.getId() + ".pdf";
                    log.info("下载零件报告 - 任务ID: {}, 报告ID: {}, URL: {}", id, taskDetail.getReportId(), reportUrl);
                    break;
                    
                case 3: // 固定文件类型
                    // 固定文件类型直接使用任务的附件URL
                    if (taskDetail.getAttachUrl() == null || taskDetail.getAttachUrl().isEmpty()) {
                        throw new RuntimeException("该固定文件任务没有关联的附件");
                    }
                    reportUrl = taskDetail.getAttachUrl();
                    reportFileName = "固定文件_" + taskDetail.getId() + ".pdf";
                    log.info("使用固定文件附件 - 任务ID: {}, URL: {}", id, reportUrl);
                    break;
                    
                default:
                    throw new RuntimeException("不支持的报告类型: " + taskDetail.getReportType());
            }
            
            // 检查报告URL是否获取成功
            if (reportUrl == null || reportUrl.isEmpty()) {
                throw new RuntimeException("未能获取报告文件URL");
            }
            
            // 从URL获取文件内容
            InputStream fileStream = null;
            
            // 判断文件是本地路径还是MinIO URL
            if (reportUrl.startsWith("http")) {
                // MinIO URL
                String objectPath = extractObjectPathFromUrl(reportUrl);
                fileStream = qmsMinioUtil.getLatestFile(objectPath);
                reportFileName = objectPath.substring(objectPath.lastIndexOf("/") + 1);
            } else {
                // 本地文件路径
                File file = new File(reportUrl);
                if (!file.exists()) {
                    throw new RuntimeException("报告文件不存在: " + reportUrl);
                }
                fileStream = new FileInputStream(file);
                reportFileName = file.getName();
            }
            
            // 根据文件扩展名设置Content-Type
            String contentType = getContentTypeByFileName(reportFileName);
            
            // 设置响应头
            response.setContentType(contentType);
            response.setCharacterEncoding("UTF-8");
            
            // 设置文件名
            String encodedFileName = java.net.URLEncoder.encode(reportFileName, "UTF-8").replace("+", "%20");
            String contentDisposition = "attachment; filename=\"" + reportFileName + "\"; filename*=UTF-8''" + encodedFileName;
            response.setHeader("Content-Disposition", contentDisposition);
            
            // 使用自定义响应头传递文件名，避免Content-Disposition被过滤的问题
            response.setHeader("X-File-Name", reportFileName);
            response.setHeader("X-File-Name-Encoded", encodedFileName);
            
            // 写入文件内容到响应流
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                // 使用缓冲区读取文件流并写入响应流
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fileStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            } finally {
                if (fileStream != null) {
                    fileStream.close();
                }
            }
            
        } catch (Exception e) {
            log.error("下载报告失败 - 任务ID: {}, 错误: {}", id, e.getMessage(), e);
            try {
                response.setContentType("text/plain");
                response.setCharacterEncoding("UTF-8");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载报告失败: " + e.getMessage());
            } catch (IOException ex) {
                log.error("设置错误响应失败", ex);
            }
        }
    }

    /**
     * 检查并更新中期任务状态
     * 当一个子任务状态更新为已完成时，检查其关联的中期任务是否所有子任务都已完成
     * 如果是，则将中期任务状态更新为待合并
     */
    @Override
    public void checkAndUpdateMiddleTaskStatus(String middleTaskCode) {
        log.info("检查并更新中期任务状态 - 中期任务编码: {}", middleTaskCode);
        try {
            // 查询该中期任务下的所有子任务
            List<TaskDetailDO> taskDetails = taskDetailMapper.selectList(
                new LambdaQueryWrapperX<TaskDetailDO>().eq(TaskDetailDO::getMiddleTaskCode, middleTaskCode));
            
            // 过滤出执行中的子任务
            List<TaskDetailDO> generatingTasks = taskDetails.stream()
                .filter(task -> task.getStatus() == TaskStatusEnum.GENERATING_REPORT.getStatus())
                .collect(java.util.stream.Collectors.toList());
            
            // 如果执行中的子任务数量为0，说明所有子任务都已完成或异常
            if (generatingTasks.isEmpty()) {
                // 查询中期任务
                TaskMiddleDO middleTask = taskMiddleMapper.selectOne(
                    TaskMiddleDO::getMiddleTaskCode, middleTaskCode);
                
                if (middleTask != null) {
                    // 检查是否所有子任务都已完成（没有异常任务）
                    boolean allCompleted = taskDetails.stream()
                        .allMatch(task -> TaskStatusEnum.COMPLETED.getStatus().equals(task.getStatus()));
                    
                    // 检查是否有异常子任务
                    boolean hasException = taskDetails.stream()
                        .anyMatch(task -> TaskStatusEnum.EXCEPTION.getStatus().equals(task.getStatus()));
                    
                    // 检查是否所有子任务都已处理完毕（完成或异常）
                    boolean allProcessed = taskDetails.stream()
                        .allMatch(task -> 
                            TaskStatusEnum.COMPLETED.getStatus().equals(task.getStatus()) || 
                            TaskStatusEnum.EXCEPTION.getStatus().equals(task.getStatus()));
                    
                    if (allCompleted) {
                        // 如果所有子任务都已完成（没有异常），将中期任务状态更新为待合并
                        middleTask.setStatus(TaskStatusEnum.WAITING_MERGE.getStatus());
                        taskMiddleMapper.updateById(middleTask);
                        log.info("中期任务所有子任务已完成，更新中期任务状态为待合并 - 中期任务编码: {}, 状态: {}", 
                            middleTask.getMiddleTaskCode(), middleTask.getStatus());
                        
                        // 检查主任务状态
                        checkAndUpdateMainTaskStatus(middleTask.getCrTaskCode());
                    } else if (allProcessed && hasException) {
                        // 如果所有子任务都已处理完毕但有异常任务，将中期任务状态更新为异常
                        middleTask.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                        taskMiddleMapper.updateById(middleTask);
                        log.info("中期任务有异常子任务，更新中期任务状态为异常 - 中期任务编码: {}, 状态: {}", 
                            middleTask.getMiddleTaskCode(), middleTask.getStatus());
                    }
                } else {
                    log.warn("找不到中期任务 - 中期任务编码: {}", middleTaskCode);
                }
            } else {
                // 如果还有执行中的子任务，则不更新中期任务状态
                log.info("中期任务还有执行中的子任务，不更新中期任务状态 - 中期任务编码: {}", 
                    middleTaskCode);
            }
        } catch (Exception e) {
            log.error("检查并更新中期任务状态失败 - 中期任务编码: {}, 错误: {}", middleTaskCode, e.getMessage(), e);
        }
    }

    /**
     * 检查主任务下的所有中期任务是否都已完成或待合并，如果是则更新主任务状态为待合并
     */
    private void checkAndUpdateMainTaskStatus(String crTaskCode) {
        try {
            // 获取主任务下的所有中期任务
            List<TaskMiddleDO> middleTasks = taskMiddleMapper.selectList(
                new LambdaQueryWrapperX<TaskMiddleDO>().eq(TaskMiddleDO::getCrTaskCode, crTaskCode));
            
            if (middleTasks.isEmpty()) {
                log.warn("主任务 {} 下没有中期任务", crTaskCode);
                return;
            }
            
            // 检查是否有任何中期任务处于异常状态
            boolean hasException = middleTasks.stream()
                .anyMatch(task -> task.getStatus() == TaskStatusEnum.EXCEPTION.getStatus());
            
            // 获取主任务信息
            TaskMainDO taskMain = taskMainMapper.selectOne(
                TaskMainDO::getCrTaskCode, crTaskCode);
                
            if (taskMain == null) {
                log.warn("找不到主任务信息 - 主任务编号: {}", crTaskCode);
                return;
            }
            
            if (hasException) {
                // 如果有任何中期任务处于异常状态，将主任务状态更新为异常
                log.info("主任务 {} 下有中期任务处于异常状态，更新主任务状态为异常", crTaskCode);
                taskMain.setStatus(TaskStatusEnum.EXCEPTION.getStatus());
                taskMainMapper.updateById(taskMain);
                return;
            }
            
            // 检查是否所有中期任务都已完成或待合并
            boolean allReadyForMerge = middleTasks.stream()
                .allMatch(task -> task.getStatus() == TaskStatusEnum.COMPLETED.getStatus() || 
                                 task.getStatus() == TaskStatusEnum.WAITING_MERGE.getStatus());
            
            if (allReadyForMerge) {
                log.info("主任务 {} 下的所有中期任务都已完成或待合并，更新主任务状态为待合并", crTaskCode);
                
                // 更新主任务状态为待合并
                taskMain.setStatus(TaskStatusEnum.WAITING_MERGE.getStatus());
                taskMainMapper.updateById(taskMain);
            }
        } catch (Exception e) {
            log.error("检查主任务状态异常: {}", crTaskCode, e);
        }
    }

    /**
     * 批量处理整机任务的自动报告生成
     * 用于任务启动时批量处理所有整机类型的任务
     * 
     * @param middleTaskCode 中期任务编号
     */
    public void batchProcessMachineReportGeneration(String middleTaskCode) {
        log.info("开始批量处理整机任务自动报告生成 - 中期任务编号: {}", middleTaskCode);
        
        // 获取任务所属的主任务编码，用于检查全局停止标志
        String crTaskCode = null;
        try {
            TaskMiddleDO middleTask = taskMiddleMapper.selectOne(
                TaskMiddleDO::getMiddleTaskCode, middleTaskCode);
            if (middleTask != null) {
                crTaskCode = middleTask.getCrTaskCode();
            }
        } catch (Exception e) {
            log.error("获取中期任务所属主任务编码失败: {}", middleTaskCode, e);
        }
        
        // 调用带停止检查的方法，传递全局停止标志的键
        String taskKey = crTaskCode != null ? "process_generate_task_" + crTaskCode : null;
        batchProcessMachineReportGeneration(middleTaskCode, taskKey);
    }
}