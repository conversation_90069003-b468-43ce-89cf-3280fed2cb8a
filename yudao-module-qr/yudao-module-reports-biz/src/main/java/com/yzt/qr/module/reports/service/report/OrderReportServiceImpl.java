package com.yzt.qr.module.reports.service.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yzt.qr.module.qrbase.api.usersgysxx.UsersGysxxApi;
import com.yzt.qr.module.reports.controller.admin.order.vo.PurchaseOrderPageReqVO;
import com.yzt.qr.module.reports.controller.admin.order.vo.PurchaseOrderRespVO;
import com.yzt.qr.module.reports.controller.admin.report.vo.*;
import com.yzt.qr.module.reports.dal.dataobject.order.PurchaseOrderDO;
import com.yzt.qr.module.reports.dal.dataobject.report.OrderReportDO;
import com.yzt.qr.module.reports.dal.dataobject.report.ReportHeadDO;
import com.yzt.qr.module.reports.dal.dataobject.reportpre.ReportHeadPreDO;
import com.yzt.qr.module.reports.dal.mysql.report.OrderReportMapper;
import com.yzt.qr.module.reports.enums.report.ReportsStatusEnum;
import com.yzt.qr.module.reports.handler.validate.ValidateReportService;
import com.yzt.qr.module.reports.service.order.PurchaseOrderService;
import com.yzt.qr.module.reports.service.reportpre.ReportHeadPreService;
import com.yzt.service.qms.vo.SrmFurnaceNoReqVO;
import com.yzt.service.qms.vo.SrmFurnaceNoRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.yzt.qr.module.reports.enums.ErrorCodeConstants.*;

/**
 * 订单报告 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class OrderReportServiceImpl implements OrderReportService {

    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private ReportHeadService reportHeadService;
    @Resource
    private ReportHeadPreService reportHeadPreService;
    @Resource
    private ValidateReportService validateReportService;
    @Resource
    private UsersGysxxApi usersGysxxApi;
    @Resource
    private OrderReportMapper orderReportMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createReport(ReportHeadSaveReqVO reqVO) {
        // 验证数据
        validateReportService.validate(reqVO);
        // 创建头表-明细表数据
        Long reportId = reportHeadService.createReportHead(reqVO);
        // 补充头表ID数据
        List<OrderReportDO> orderReportList = purchaseOrderService
                .getPOListByOrderNos(BeanUtils.toBean(reqVO.getPoInfos(), PurchaseOrderDO.class))
                .stream().map(po -> new OrderReportDO().setReportId(reportId).setOrderId(po.getId()))
                .collect(Collectors.toList());
        // 插入采购订单-报告关系表
        orderReportMapper.insertBatch(orderReportList);
        return reportId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createReport(ReportHeadSaveReqVO reqVO, Long preHeadId) {
        if (Objects.isNull(preHeadId)) return createReport(reqVO);
        // 预存数据状态
        ReportHeadPreDO reportHeadPre = reportHeadPreService.getReportHeadPre(preHeadId);
        if (Objects.isNull(reportHeadPre)) throw exception(PRE_DATA_USED_OR_DELETED);
        synchronized (this) {
            try {
                reportHeadPreService.deleteReportHeadPre(preHeadId);
            } catch (ServiceException se) {
                throw exception(PRE_DATA_USED_OR_DELETED);
            }
        }
        return createReport(reqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateReport(ReportHeadSaveReqVO reqVO) {
        validateReportService.validate(reqVO);
        reportHeadService.updateReportHead(reqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateReportStatus(Long reportId, ReportsStatusEnum status) {
        ReportHeadDO update = new ReportHeadDO().setId(reportId).setStatus(status.getVal());
        // 提交
        if (ReportsStatusEnum.TO_BE_CONFIRM == status) {
            update.setSubmitTime(LocalDateTime.now());
        }
        // 确认
        if (ReportsStatusEnum.CONFIRMED == status) {
            update.setConfirmTime(LocalDateTime.now()).setConfirmUser(getLoginUserId().toString());
        }
        reportHeadService.updateHeadOnly(update);
        // 作废删除操作
        if (ReportsStatusEnum.DELETED == status) {
            reportHeadService.deleteReportHead(reportId);
            orderReportMapper.delete(OrderReportDO::getReportId, reportId);
        }
        return true;
    }

    @Override
    public Long createOrderReport(OrderReportSaveReqVO createReqVO) {
        // 插入
        OrderReportDO orderReport = BeanUtils.toBean(createReqVO, OrderReportDO.class);
        orderReportMapper.insert(orderReport);
        // 返回
        return orderReport.getId();
    }

    @Override
    public void updateOrderReport(OrderReportSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderReportExists(updateReqVO.getId());
        // 更新
        OrderReportDO updateObj = BeanUtils.toBean(updateReqVO, OrderReportDO.class);
        orderReportMapper.updateById(updateObj);
    }

    @Override
    public void deleteOrderReport(Long id) {
        // 校验存在
        validateOrderReportExists(id);
        // 删除
        orderReportMapper.deleteById(id);
    }

    private void validateOrderReportExists(Long id) {
        if (orderReportMapper.selectById(id) == null) {
            throw exception(ORDER_REPORT_NOT_EXISTS);
        }
    }

    @Override
    public List<OrderReportRespVO> listByReportIds(List<Long> reportIds) {
        List<OrderReportDO> list = orderReportMapper.selectList(new LambdaQueryWrapperX<OrderReportDO>()
                .in(OrderReportDO::getReportId, reportIds));
        return fulfillDetail(list);
    }

    @Override
    public List<OrderReportRespVO> listDetailByPoIds(Collection<Long> poIds) {
        List<OrderReportDO> list = orderReportMapper.selectList(new LambdaQueryWrapperX<OrderReportDO>()
                .in(OrderReportDO::getOrderId, poIds));
        return fulfillDetail(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FurnaceNoNumRespVO getFurnaceNoNum(FurnaceNoNumReqVO reqVO) {
        FurnaceNoNumRespVO result = new FurnaceNoNumRespVO();
        // 查询炉号数量
        if (StrUtil.isNotEmpty(reqVO.getFurnaceNo())) {
            ReportHeadPageReqVO headReq = new ReportHeadPageReqVO().setFurnaceNo(reqVO.getFurnaceNo())
                    .setReportFormat(reqVO.getReportFormat());
            headReq.setPageSize(PageParam.PAGE_SIZE_NONE);
            int qtyInFurnace = reportHeadService.getReportHeadPage(headReq).getList()
                    .stream().mapToInt(ReportHeadDO::getQty).sum();
            result.setQtyInFurnace(qtyInFurnace);
        }
        // 查询订单行的已建数量
        if (StrUtil.isAllNotEmpty(reqVO.getOrderNo(), reqVO.getOrderLineNo())) {
            PurchaseOrderPageReqVO purchaseOrderPageReqVO = new PurchaseOrderPageReqVO()
                    .setOrderNo(reqVO.getOrderNo())
                    .setOrderLineNo(reqVO.getOrderLineNo())
                    .setSupplier(usersGysxxApi.getSupplierCodeByUserId(getLoginUserId()));
            purchaseOrderPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            List<PurchaseOrderDO> po = purchaseOrderService.getPurchaseOrderPage(purchaseOrderPageReqVO).getList();
            if (CollUtil.isEmpty(po)) throw exception(PURCHASE_ORDER_NOT_EXISTS);
            List<OrderReportDO> reportIds = orderReportMapper.selectList(OrderReportDO::getOrderId, po.get(0).getId());
            if (CollUtil.isNotEmpty(reportIds)) {
                int qtyInReport = reportHeadService.getByIds(reportIds.stream().map(OrderReportDO::getReportId).collect(Collectors.toSet()))
                        .stream().filter(o -> Objects.equals(o.getReportFormat(), reqVO.getReportFormat()))
                        .mapToInt(ReportHeadDO::getQty).sum();
                result.setQtyInReport(qtyInReport);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SrmFurnaceNoRespVO> getSrmFurnaceNoNum(List<SrmFurnaceNoReqVO> list) {
        if (CollUtil.isEmpty(list)) return Collections.emptyList();
        // 采购订单行信息
        List<PurchaseOrderDO> poInfos = list.stream()
                .map(o -> new PurchaseOrderDO().setOrderNo(o.getCgddh()).setOrderLineNo(o.getHh()))
                .collect(Collectors.toList());
        List<PurchaseOrderDO> poList = purchaseOrderService.getPOListByOrderNos(poInfos);
        if (CollUtil.isEmpty(poList)) throw exception(PURCHASE_ORDER_NOT_EXISTS);
        // 订单行与报告关系表
        List<OrderReportDO> relation = orderReportMapper.selectList(new LambdaQueryWrapperX<OrderReportDO>()
                .in(OrderReportDO::getOrderId, poList.stream().map(PurchaseOrderDO::getId).collect(Collectors.toList())));
        // 相关的所有报告信息
        Map<Long, ReportHeadDO> reportMap = reportHeadService.getByIds(relation.stream().map(OrderReportDO::getReportId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ReportHeadDO::getId, o -> o, (o1, o2) -> o1));
        return list.stream().map(po -> {
            SrmFurnaceNoRespVO bean = new SrmFurnaceNoRespVO().setOrderNo(po.getCgddh()).setOrderLineNo(po.getHh())
                    .setFurnaceNo(po.getLh()).setQty(0);
            // 按报告格式分组对应的炉号的报告
            Map<String, List<ReportHeadDO>> furnaceNoReportMap = poList.stream()
                    .filter(o -> Objects.equals(o.getOrderNo(), bean.getOrderNo()) && Objects.equals(o.getOrderLineNo(), bean.getOrderLineNo()))
                    .flatMap(o -> relation.stream().filter(r -> Objects.equals(r.getOrderId(), o.getId())))
                    .map(OrderReportDO::getReportId)
                    .filter(reportMap::containsKey)
                    .map(reportMap::get)
                    .filter(o -> Objects.equals(o.getFurnaceNo(), po.getLh()))
                    .collect(Collectors.groupingBy(ReportHeadDO::getReportFormat));
            // 按报告格式分组统计数量，最终取最大值作为当前采购订单行的指定炉号的数量
            int maxQty = 0; // 最大数量
            for (Map.Entry<String, List<ReportHeadDO>> entry : furnaceNoReportMap.entrySet()) {
                int qty = entry.getValue().stream().mapToInt(ReportHeadDO::getQty).sum();
                maxQty = Math.max(maxQty, qty);
            }
            bean.setQty(maxQty);
            return bean;
        }).collect(Collectors.toList());
    }

    private List<OrderReportRespVO> fulfillDetail(List<OrderReportDO> list) {
        if (CollUtil.isEmpty(list)) return Collections.emptyList();
        // 查询采购订单行信息
        Map<Long, PurchaseOrderRespVO> poMap = purchaseOrderService.getByIds(list
                        .stream().map(OrderReportDO::getOrderId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(PurchaseOrderDO::getId, o -> BeanUtils.toBean(o, PurchaseOrderRespVO.class), (o1, o2) -> o1));
        // 查询报告头表信息
        Map<Long, ReportHeadRespVO> reportMap = reportHeadService.getByIds(list.stream().map(OrderReportDO::getReportId).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(ReportHeadDO::getId, o -> BeanUtils.toBean(o, ReportHeadRespVO.class), (o1, o2) -> o1));

        return list.stream().map(item -> BeanUtils.toBean(item, OrderReportRespVO.class)
                        .setReport(reportMap.get(item.getReportId()))
                        .setPurchaseOrder(poMap.get(item.getOrderId())))
                .collect(Collectors.toList());
    }

}