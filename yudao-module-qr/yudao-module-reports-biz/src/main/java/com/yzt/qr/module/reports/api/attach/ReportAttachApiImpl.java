package com.yzt.qr.module.reports.api.attach;

import com.yzt.qr.module.reports.service.attach.ReportAttachService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2025/2/26 13:45
 */
@RequiredArgsConstructor
@Service
public class ReportAttachApiImpl implements ReportAttachApi {

    private final ReportAttachService reportAttachService;

    @Override
    public void uploadReportAttach(Integer reportScope, Long reportId, MultipartFile file) {
        reportAttachService.uploadAttach(reportScope, reportId, file);
    }
}
