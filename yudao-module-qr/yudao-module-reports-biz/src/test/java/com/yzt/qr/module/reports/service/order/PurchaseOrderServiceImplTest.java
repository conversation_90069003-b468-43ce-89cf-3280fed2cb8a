package com.yzt.qr.module.reports.service.order;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import com.yzt.qr.module.reports.controller.admin.order.vo.PurchaseOrderPageReqVO;
import com.yzt.qr.module.reports.controller.admin.order.vo.PurchaseOrderSaveReqVO;
import com.yzt.qr.module.reports.dal.dataobject.order.PurchaseOrderDO;
import com.yzt.qr.module.reports.dal.mysql.order.PurchaseOrderMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.yzt.qr.module.reports.enums.ErrorCodeConstants.PURCHASE_ORDER_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PurchaseOrderServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(PurchaseOrderServiceImpl.class)
public class PurchaseOrderServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PurchaseOrderServiceImpl purchaseOrderService;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Test
    public void testCreatePurchaseOrder_success() {
        // 准备参数
        PurchaseOrderSaveReqVO createReqVO = randomPojo(PurchaseOrderSaveReqVO.class).setId(null);

        // 调用
        Long purchaseOrderId = purchaseOrderService.createPurchaseOrder(createReqVO);
        // 断言
        assertNotNull(purchaseOrderId);
        // 校验记录的属性是否正确
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        assertPojoEquals(createReqVO, purchaseOrder, "id");
    }

    @Test
    public void testUpdatePurchaseOrder_success() {
        // mock 数据
        PurchaseOrderDO dbPurchaseOrder = randomPojo(PurchaseOrderDO.class);
        purchaseOrderMapper.insert(dbPurchaseOrder);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PurchaseOrderSaveReqVO updateReqVO = randomPojo(PurchaseOrderSaveReqVO.class, o -> {
            o.setId(dbPurchaseOrder.getId()); // 设置更新的 ID
        });

        // 调用
        purchaseOrderService.updatePurchaseOrder(updateReqVO);
        // 校验是否更新正确
        PurchaseOrderDO purchaseOrder = purchaseOrderMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, purchaseOrder);
    }

    @Test
    public void testUpdatePurchaseOrder_notExists() {
        // 准备参数
        PurchaseOrderSaveReqVO updateReqVO = randomPojo(PurchaseOrderSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> purchaseOrderService.updatePurchaseOrder(updateReqVO), PURCHASE_ORDER_NOT_EXISTS);
    }

    @Test
    public void testDeletePurchaseOrder_success() {
        // mock 数据
        PurchaseOrderDO dbPurchaseOrder = randomPojo(PurchaseOrderDO.class);
        purchaseOrderMapper.insert(dbPurchaseOrder);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPurchaseOrder.getId();

        // 调用
        purchaseOrderService.deletePurchaseOrder(id);
       // 校验数据不存在了
       assertNull(purchaseOrderMapper.selectById(id));
    }

    @Test
    public void testDeletePurchaseOrder_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> purchaseOrderService.deletePurchaseOrder(id), PURCHASE_ORDER_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPurchaseOrderPage() {
       // mock 数据
       PurchaseOrderDO dbPurchaseOrder = randomPojo(PurchaseOrderDO.class, o -> { // 等会查询到
           o.setOrderNo(null);
           o.setOrderLineNo(null);
           o.setSalesOrderNo(null);
           o.setSalesOrderLineNo(null);
           o.setTextureSpec(null);
           o.setSerials(null);
           o.setSupplier(null);
           o.setQty(null);
       });
       purchaseOrderMapper.insert(dbPurchaseOrder);
       // 测试 orderNo 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setOrderNo(null)));
       // 测试 orderLineNo 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setOrderLineNo(null)));
       // 测试 salesOrderNo 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setSalesOrderNo(null)));
       // 测试 salesOrderLineNo 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setSalesOrderLineNo(null)));
       // 测试 textureSpec 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setTextureSpec(null)));
       // 测试 serials 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setSerials(null)));
       // 测试 supplier 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setSupplier(null)));
       // 测试 qty 不匹配
       purchaseOrderMapper.insert(cloneIgnoreId(dbPurchaseOrder, o -> o.setQty(null)));
       // 准备参数
       PurchaseOrderPageReqVO reqVO = new PurchaseOrderPageReqVO();
       reqVO.setOrderNo(null);
       reqVO.setOrderLineNo(null);
       reqVO.setSerials(null);
       reqVO.setSupplier(null);

       // 调用
       PageResult<PurchaseOrderDO> pageResult = purchaseOrderService.getPurchaseOrderPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPurchaseOrder, pageResult.getList().get(0));
    }

}