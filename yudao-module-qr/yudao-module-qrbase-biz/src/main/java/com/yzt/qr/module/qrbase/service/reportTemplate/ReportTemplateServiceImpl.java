package com.yzt.qr.module.qrbase.service.reportTemplate;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yzt.common.minio.utils.QmsMinioUtil;
import com.yzt.qr.module.qrbase.controller.admin.reportTemplate.vo.ReportTemplatePageReqVO;
import com.yzt.qr.module.qrbase.controller.admin.reportTemplate.vo.ReportTemplateSaveReqVO;
import com.yzt.qr.module.qrbase.controller.admin.reportTemplate.vo.TemplateFieldPageReqVO;
import com.yzt.qr.module.qrbase.controller.admin.reportTemplate.vo.TemplateFieldSaveReqVO;
import com.yzt.qr.module.qrbase.dal.dataobject.reportTemplate.ReportTemplateDO;
import com.yzt.qr.module.qrbase.dal.dataobject.reportTemplate.TemplateFieldDO;
import com.yzt.qr.module.qrbase.dal.mysql.reportTemplate.ReportTemplateMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static com.yzt.qr.module.qrbase.dal.dataobject.reportTemplate.ReportTemplateDO.VERSION_DEFAULT;
import static com.yzt.qr.module.qrbase.enums.ErrorCodeConstants.*;
import static sun.font.CreatedFontTracker.MAX_FILE_SIZE;

/**
 * 报告模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ReportTemplateServiceImpl implements ReportTemplateService {

    @Resource
    private ReportTemplateMapper reportTemplateMapper;
    @Resource
    private TemplateFieldService templateFieldService;
    @Resource
    private QmsMinioUtil qmsMinioUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createReportTemplate(@Valid ReportTemplateSaveReqVO createReqVO) {
        // 验证插入数据有效性
        List<TemplateFieldSaveReqVO> createFields = createReqVO.getFields();
        validateTemplateFieldDuplicate(createFields);
        // 计算版本号
        if (Objects.isNull(createReqVO.getVersion())) {
            int version = reportTemplateMapper.selectList(new LambdaQueryWrapperX<ReportTemplateDO>()
                            .eqIfPresent(ReportTemplateDO::getReportFormat, createReqVO.getReportFormat())
                            .eqIfPresent(ReportTemplateDO::getTemplateDesc, createReqVO.getTemplateDesc()))
                    .stream().mapToInt(ReportTemplateDO::getVersion).max().orElse(VERSION_DEFAULT - 1);
            createReqVO.setVersion((short) (version + 1));
        }
        // 插入模板头表数据
        ReportTemplateDO reportTemplate = BeanUtils.toBean(createReqVO, ReportTemplateDO.class);
        reportTemplateMapper.insert(reportTemplate);
        // 插入模板字段表数据
        createFields.forEach(field -> {
            field.setTemplateId(reportTemplate.getId());
            templateFieldService.createTemplateField(field);
        });
        return reportTemplate.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void copyReportTemplate(Long oldTemplateId, String newTemplateDesc) {
        ReportTemplateDO template = validateReportTemplateExists(oldTemplateId);
        List<ReportTemplateDO> newExistList = reportTemplateMapper.selectList(new LambdaQueryWrapperX<ReportTemplateDO>()
                .eq(ReportTemplateDO::getTemplateDesc, newTemplateDesc)
                .orderByDesc(ReportTemplateDO::getVersion));
        // 计算新模板名称的版本号
        short version = CollUtil.isEmpty(newExistList) ? VERSION_DEFAULT : (short) (newExistList.get(0).getVersion() + 1);
        template.setId(null).setTemplateDesc(newTemplateDesc).setVersion(version)
                // 移除自动填充的字段
                .setDeleted(null).setCreateTime(null).setCreator(null).setUpdateTime(null).setUpdater(null);
        reportTemplateMapper.insert(template);
        // 复制模板字段
        templateFieldService.copyTemplateField(oldTemplateId, template.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateReportTemplate(ReportTemplateSaveReqVO updateReqVO) {
        if (Objects.nonNull(updateReqVO.getId())) {
            // 校验存在
            validateReportTemplateExists(updateReqVO.getId());
            // 更新头表
            ReportTemplateDO updateObj = BeanUtils.toBean(updateReqVO, ReportTemplateDO.class);
            reportTemplateMapper.updateById(updateObj);
        }
        // 更新字段
        updateReqVO.getFields().stream().filter(o -> Objects.nonNull(o.getTemplateId()))
                .forEach(field -> {
                    if (Objects.nonNull(field.getId())) templateFieldService.updateTemplateField(field);
                    else templateFieldService.createTemplateField(field);
                });
    }

    @Override
    public void uploadEditTemplate(MultipartFile file, Long id) {
        ReportTemplateDO reportTemplate = validateReportTemplateExists(id);
        // 验证文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw exception(FILE_SIZE_EXCEEDS_LIMIT);
        }

        try {
            // 使用QMS模块的MinIO工具类上传文件到填报模板目录
            String fileUrl = qmsMinioUtil.uploadInputTemplateFile(file);
            // 更新模板URL
            reportTemplate.setTemplateUrl(fileUrl);
            reportTemplateMapper.updateById(reportTemplate);
        } catch (IOException e) {
            throw exception(REPORT_TEMPLATE_FILE_ERROR);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteReportTemplate(Long id) {
        // 校验存在
        validateReportTemplateExists(id);
        // 删除模板头
        reportTemplateMapper.deleteById(id);
        // 删除模板字段
        templateFieldService.deleteFieldsBy(id);

    }

    @Override
    public CommonResult<Boolean> deleteTemplateField(Long fieldId) {
//        根据父id查找
        List<TemplateFieldDO> templateFieldDOS = templateFieldService.selectByPatientId(fieldId);
        if (templateFieldDOS.size()!=0){
            return success( false);
        }
        templateFieldService.deleteFieldBy(fieldId);
        return success(true);
    }

    private ReportTemplateDO validateReportTemplateExists(Long id) {
        ReportTemplateDO template = reportTemplateMapper.selectById(id);
        if (template == null) {
            throw exception(REPORT_TEMPLATE_NOT_EXISTS);
        }
        return template;
    }

    /**
     * 验证模版内字段是否重复
     *
     * <AUTHOR>
     * @since 2024/4/7 15:30
     */
    private void validateTemplateFieldDuplicate(List<TemplateFieldSaveReqVO> fields) {
        if (CollUtil.isEmpty(fields)) throw exception(TEMPLATE_FIELDS_EMPTY);
        long distinct = fields.stream().map(TemplateFieldSaveReqVO::getField).distinct().count();
        if (distinct != fields.size()) throw exception(TEMPLATE_FIELD_DUPLICATE);
    }

    @Override
    public ReportTemplateDO getReportTemplate(Long id) {
        return reportTemplateMapper.selectById(id);
    }

    @Override
    public ReportTemplateDO getReportTemplate(String templateDesc, Integer version) {
        return reportTemplateMapper.selectOne(new LambdaQueryWrapperX<ReportTemplateDO>()
                .eq(ReportTemplateDO::getTemplateDesc, templateDesc)
                .eq(ReportTemplateDO::getVersion, version));
    }

    @Override
    public List<ReportTemplateDO> listByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) return Collections.emptyList();
        return reportTemplateMapper.selectByIds(ids);
    }

    /**
     * 只分页获取模板头信息
     *
     * @param pageReqVO 分页查询
     * @return 报告模板头信息
     */
    @Override
    public PageResult<ReportTemplateDO> getReportTemplatePage(ReportTemplatePageReqVO pageReqVO) {
        return reportTemplateMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<ReportTemplateDO>()
                .likeIfPresent(ReportTemplateDO::getReportFormat, pageReqVO.getReportFormat())
                .likeIfPresent(ReportTemplateDO::getTemplateDesc, pageReqVO.getTemplateDesc())
                .eqIfPresent(ReportTemplateDO::getVersion, pageReqVO.getVersion())
                .orderByAsc(ReportTemplateDO::getReportFormat, ReportTemplateDO::getTemplateDesc, ReportTemplateDO::getVersion));
    }

    @Override
    public List<TemplateFieldDO> getTemplateAllFields(Long templateId) {
        validateReportTemplateExists(templateId);
        TemplateFieldPageReqVO fieldPageReqVO = new TemplateFieldPageReqVO().setTemplateId(templateId);
        fieldPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        return templateFieldService.getTemplateFieldPage(fieldPageReqVO).getList();
    }

}