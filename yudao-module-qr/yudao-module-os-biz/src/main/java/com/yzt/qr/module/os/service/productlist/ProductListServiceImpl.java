package com.yzt.qr.module.os.service.productlist;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yzt.qr.module.os.controller.admin.productlist.pojo.ProductListComparePO;
import com.yzt.qr.module.os.controller.admin.productlist.vo.*;
import com.yzt.qr.module.os.dal.dataobject.productlist.ProductListDO;
import com.yzt.qr.module.os.dal.mysql.productlist.ProductListMapper;
import com.yzt.qr.module.os.enums.productlist.ProductListStatusEnum;
import com.yzt.qr.module.qrbase.api.requireReportFormat.RequireReportFormatApi;
import com.yzt.qr.module.qrbase.api.requireReportFormat.dto.RequireReportFormatRespDTO;
import com.yzt.qr.module.qrbase.api.spec.SpecFormatApi;
import com.yzt.qr.module.qrbase.api.spec.dto.SpecFormatRespDTO;
import com.yzt.qr.module.reports.api.order.EipApi;
import com.yzt.qr.module.reports.api.order.OrderRequirementApi;
import com.yzt.qr.module.reports.api.order.PurchaseOrderApi;
import com.yzt.qr.module.reports.api.order.dto.EipRespDTO;
import com.yzt.qr.module.reports.api.order.dto.OrderRequirementRespDTO;
import com.yzt.qr.module.reports.api.order.dto.PurchaseOrderRespDTO;
import com.valve.module.base.api.partclass.PartClassApi;
import com.valve.module.base.api.partclass.dto.PartClassRespDTO;
import com.valve.module.base.api.texture.TextureApi;
import com.valve.module.base.api.texture.dto.TextureRespDTO;
import com.yzt.qr.module.qrbase.api.valvetypeparts.ValveTypePartsApi;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yzt.qr.module.os.enums.ErrorCodeConstants.*;
import static com.yzt.qr.module.os.enums.LogRecordConstants.*;
import static com.yzt.qr.module.reports.enums.ErrorCodeConstants.PURCHASE_ORDER_NOT_EXISTS;

/**
 * 产品清单表 Service 实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Validated
public class ProductListServiceImpl implements ProductListService {
    @Resource
    private ProductListFormatService productListFormatService;
    @Resource
    private ProductListMapper productListMapper;

    private final PurchaseOrderApi purchaseOrderApi;
    private final EipApi eipApi;
    private final ValveTypePartsApi valveTypePartsApi;
    private final SpecFormatApi specFormatApi;
    private final TextureApi textureApi;
    private final OrderRequirementApi orderRequirementApi;
    private final RequireReportFormatApi requireReportFormatApi;
    private final PartClassApi partClassApi;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveProductList(List<ProductListItemSaveReqVO> saveReqVOs) {
        // 验证数据
        validateSaveList(saveReqVOs);
        // 比对分批
        ProductListItemSaveReqVO first = saveReqVOs.get(0);
        List<ProductListDO> oldList = getProductList(new ProductListPageReqVO().setOrderNo(first.getOrderNo()).setOrderLineNo(first.getOrderLineNo()));
        ProductListComparePO compared = compareProductList(oldList, saveReqVOs);
        // 写入
        List<ProductListFormatSaveReqVO> formatInsertList = ListUtil.list(false);
        if (CollUtil.isNotEmpty(compared.getCreateList())) {
            List<ProductListDO> insert = BeanUtils.toBean(compared.getCreateList(), ProductListDO.class);
            productListMapper.insertBatch(insert);
            // 报告需求
            formatInsertList.addAll(insert.stream().flatMap(bean -> bean.getReportFormats()
                            .stream()
                            .map(format -> new ProductListFormatSaveReqVO().setProductListId(bean.getId()).setReportFormat(format)))
                    .collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(compared.getUpdateList())) {
            productListMapper.updateById(BeanUtils.toBean(compared.getUpdateList(), ProductListDO.class));
            // 报告需求
            formatInsertList.addAll(compared.getUpdateList().stream().flatMap(bean -> bean.getReportFormats()
                            .stream()
                            .map(format -> new ProductListFormatSaveReqVO().setProductListId(bean.getId()).setReportFormat(format)))
                    .collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(compared.getDeleteList())) productListMapper.deleteByIds(BeanUtils.toBean(compared.getDeleteList(), ProductListDO.class));
        // 删除报告需求表旧数据，补充新数据
        List<Long> delIds = compared.getUpdateList().stream().map(ProductListItemSaveReqVO::getId).collect(Collectors.toList());
        delIds.addAll(compared.getDeleteList().stream().map(ProductListItemSaveReqVO::getId).collect(Collectors.toList()));
        productListFormatService.deleteProductListFormat(delIds);
        return productListFormatService.createProductListFormat(formatInsertList);
    }

    @Override
    public void deleteProductList(String orderNo, String orderLineNo) {
        List<ProductListDO> ids = productListMapper.selectList(new LambdaQueryWrapperX<ProductListDO>()
                .eq(ProductListDO::getOrderNo, orderNo)
                .eq(ProductListDO::getOrderLineNo, orderLineNo));
        if (CollUtil.isEmpty(ids)) throw exception(PRODUCT_LIST_NOT_EXISTS);
        // 删除
        productListMapper.deleteByIds(ids);
        // 删除报告需求表
        productListFormatService.deleteProductListFormat(ids.stream().map(ProductListDO::getId).collect(Collectors.toList()));
    }

    /**
     * 保存验证
     */
    private void validateSaveList(List<ProductListItemSaveReqVO> list) {
        if (list.stream().map(item -> item.getOrderNo() + item.getOrderLineNo()).distinct().count() > 1)
            throw exception(PRODUCT_LIST_ORDER_NO_MULTI);
        if (list.stream().map(ProductListItemSaveReqVO::getMaterialCode).distinct().count() > 1)
            throw exception(PRODUCT_LIST_MATERIAL_MULTI);
        if (list.stream().anyMatch(item -> StrUtil.isEmpty(item.getTexture()) || item.getQty() == null || item.getQty() <= 0))
            throw exception(PRODUCT_LIST_TEXTURE_QTY_ERROR);
        Map<String, List<ProductListItemSaveReqVO>> serialMap = list.stream().collect(Collectors.groupingBy(ProductListItemSaveReqVO::getSerials));
        serialMap.forEach((k, v) -> {
            if (v.stream().map(ProductListItemSaveReqVO::getPartClass).distinct().count() != v.size())
                throw exception(PRODUCT_LIST_PART_CLASS_DUPLICATE);
        });
        List<String> allSerials = serialMap.keySet().stream().flatMap(s -> Arrays.stream(s.split(","))).collect(Collectors.toList());
        if (CollectionUtils.convertSet(allSerials).size() != allSerials.size())
            throw exception(PRODUCT_LIST_SERIALS_DUPLICATE);

    }

    /**
     * 比对分批
     * @param oldList 原始数据
     * @param newList 新数据
     * @return 分批结果
     */
    private ProductListComparePO compareProductList(List<ProductListDO> oldList, List<ProductListItemSaveReqVO> newList) {
        ProductListComparePO result = ProductListComparePO.builder()
                .createList(new ArrayList<>())
                .updateList(new ArrayList<>()).build();
        for (ProductListItemSaveReqVO item : newList) {
            Optional<ProductListDO> found = oldList.stream().filter(old ->
                            StrUtil.equals(old.getOrderNo(), item.getOrderNo()) &&
                            StrUtil.equals(old.getOrderLineNo(), item.getOrderLineNo()) &&
                            StrUtil.equals(old.getSerials(), item.getSerials()) &&
                            StrUtil.equals(old.getPartClass(), item.getPartClass()))
                    .findFirst();
            if (found.isPresent()) {
                // 更新部分
                item.setId(found.get().getId());
                result.getUpdateList().add(item);
            } else {
                // 新增部分
                item.setId(null);
                result.getCreateList().add(item);
            }
        }
        // 删除部分
        result.setDeleteList(oldList.stream()
                .filter(old -> newList.stream().noneMatch(newItem -> Objects.equals(old.getId(), newItem.getId())))
                .map(old -> BeanUtils.toBean(old, ProductListItemSaveReqVO.class))
                .collect(Collectors.toList()));
        return result;
    }

    @Override
    public PageResult<ProductListDO> getProductListPage(ProductListPageReqVO pageReqVO) {
        PageResult<ProductListDO> result = productListMapper.selectPage(pageReqVO);
        List<PurchaseOrderRespDTO> pos = purchaseOrderApi.listBy(result.getList().stream()
                .map(item -> Pair.of(item.getOrderNo(), item.getOrderLineNo()))
                .collect(Collectors.toList()));
        // 此处变更了qty字段的含义，并不规范，特此批注
        result.getList().forEach(item -> item.setQty(pos.stream()
                .filter(po -> Objects.equals(po.getOrderNo(), item.getOrderNo()) && Objects.equals(po.getOrderLineNo(), item.getOrderLineNo()))
                .findFirst().orElseThrow(() -> exception(PURCHASE_ORDER_NOT_EXISTS)).getQty()));
        return result;
    }

    @Override
    public ProductListDO getProductList(Long id) {
        return productListMapper.selectById(id);
    }

    @Override
    public List<ProductListDO> getProductList(ProductListPageReqVO params) {
        return productListMapper.selectList(new LambdaQueryWrapperX<ProductListDO>()
                .eqIfPresent(ProductListDO::getOrderNo, params.getOrderNo())
                .eqIfPresent(ProductListDO::getOrderLineNo, params.getOrderLineNo())
                .eqIfPresent(ProductListDO::getStatus, params.getStatus()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ProductListItemRespVO> initProductList(String orderNo, String orderLineNo) {
        Long existCount = productListMapper.selectCount(new LambdaQueryWrapperX<ProductListDO>()
                .eqIfPresent(ProductListDO::getOrderNo, orderNo)
                .eqIfPresent(ProductListDO::getOrderLineNo, orderLineNo));
        if (existCount > 0) throw exception(PRODUCT_LIST_EXISTS);
        PurchaseOrderRespDTO po = purchaseOrderApi.getBy(orderNo, orderLineNo);
        EipRespDTO eipInfo = eipApi.getEipInfo(po.getSalesOrderNo(), po.getSalesOrderLineNo());
        eipInfo.setValveType("EBM");
        List<ProductListDO> list = valveTypePartsApi.getPartsByType(eipInfo.getValveType())
                .stream().map(partClass -> {
                    ProductListDO item = new ProductListDO();
                    item.setOrderNo(orderNo);
                    item.setOrderLineNo(orderLineNo);
                    item.setMaterialCode(po.getMaterialCode());
                    item.setPartClass(partClass.getPartClassCode());
                    item.setSerials(String.join(",", po.getSerials().keySet()));
                    item.setQty(partClass.getQty());
                    item.setStatus(ProductListStatusEnum.DRAFT.getCode());
                    return item;
                }).collect(Collectors.toList());
        return initProductListFormat(list);
    }

    @Override
    public void fillDetails(List<ProductListItemRespVO> list) {
        Map<String, String> partClassMap = partClassApi
                .listByCodes(list.stream().map(ProductListItemRespVO::getPartClass).collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(PartClassRespDTO::getClassCode,
                        o -> o.getClassDesc().stream().min(Comparator.comparing(PartClassRespDTO.Description::getLangId))
                                .map(PartClassRespDTO.Description::getClassDesc).orElse(StrUtil.EMPTY)));
        list.forEach(o -> o.setPartClassName(partClassMap.get(o.getPartClass())));
    }

    @LogRecord(type = PRODUCT_LIST_RECORD, subType = PRODUCT_LIST_STATUS,
            bizNo = "{{ #statusReqVO.orderNo}}-{{#statusReqVO.orderLineNo}}", success = PRODUCT_LIST_STATUS_SUCCESS)
    @Override
    public boolean updateProductListStatus(ProductListStatusReqVO statusReqVO) {
        int updateCount = productListMapper.update(new LambdaUpdateWrapper<ProductListDO>()
                .eq(ProductListDO::getOrderNo, statusReqVO.getOrderNo())
                .eq(ProductListDO::getOrderLineNo, statusReqVO.getOrderLineNo())
                .set(ProductListDO::getStatus, statusReqVO.getStatus()));
        return updateCount > 0;
    }

    /**
     * 补充不同部件的报告需求
     * @param productList 产品清单
     */
    private List<ProductListItemRespVO> initProductListFormat(List<ProductListDO> productList) {
        List<Pair<String, String>> orderNos = productList.stream()
                .map(item -> Pair.of(item.getOrderNo(), item.getOrderLineNo()))
                .collect(Collectors.toList());
        List<PurchaseOrderRespDTO> pos = purchaseOrderApi.listBy(orderNos);
        Map<String, TextureRespDTO> textureMap = textureApi.listByTextures(productList.stream().map(ProductListDO::getTexture).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(TextureRespDTO::getTexture, Function.identity(), (o1, o2) -> o1));
        List<ProductListItemRespVO> result = ListUtil.list(false);
        for (ProductListDO item : productList) {
            PurchaseOrderRespDTO po = pos.stream()
                    .filter(order -> Objects.equals(order.getOrderNo(), item.getOrderNo())
                            && Objects.equals(order.getOrderLineNo(), item.getOrderLineNo()))
                    .findFirst().orElseThrow(() -> exception(PURCHASE_ORDER_NOT_EXISTS));
            Set<String> specFormats = CollUtil.set(false);
            // 采购规范定义的报告需求
            specFormatApi.listBySpecification(Arrays.asList(StrUtil.nullToEmpty(po.getTextureSpec()).split(",")))
                    .stream().collect(Collectors.groupingBy(SpecFormatRespDTO::getSpecification))
                    .forEach((spec, formats) -> {
                        Set<String> filterFormats = formats.stream()
                                .filter(format -> StrUtil.isEmpty(format.getProcureType())
                                        || Objects.equals(format.getProcureType(), "外购"))
                                .filter(format -> StrUtil.isEmpty(format.getMaterialGroup())
                                        || Objects.equals(po.getMaterialGroup(), format.getMaterialGroup()))
                                .filter(format -> StrUtil.isEmpty(format.getTextureCategory())
                                        || Objects.equals(textureMap.get(item.getTexture()).getCategory(), format.getTextureCategory()))
                                .filter(format -> StrUtil.isEmpty(format.getTexture())
                                        || Objects.equals(item.getTexture(), format.getTexture()))
                                .map(SpecFormatRespDTO::getReportFormat)
                                .collect(Collectors.toSet());
                        specFormats.addAll(filterFormats);
                    });
            // 销售订单特殊要求的报告需求
            List<OrderRequirementRespDTO> requirements = orderRequirementApi.getBy(po.getSalesOrderNo(), po.getSalesOrderLineNo())
                    .stream().filter(require -> Objects.equals(require.getPartsClass(), item.getPartClass()))
                    .collect(Collectors.toList());
            Set<String> requireFormats = requireReportFormatApi.listBy(requirements.stream().map(OrderRequirementRespDTO::getTestCode).collect(Collectors.toList()))
                    .stream().map(RequireReportFormatRespDTO::getFormatCode).collect(Collectors.toSet());
            ProductListItemRespVO bean = BeanUtils.toBean(item, ProductListItemRespVO.class);
            specFormats.addAll(requireFormats);
            bean.setReportFormats(CollUtil.newArrayList(specFormats));
            result.add(bean);
        }
        return result;
    }
}